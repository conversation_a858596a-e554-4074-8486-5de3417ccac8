-- Add read field to messages table
-- This migration adds a read field to the messages table to track read status

-- Note: The read field is now added in the ensure_messages_table_exists migration
-- This is kept for reference only

-- <PERSON>reate function to count unread messages for a user
CREATE OR REPLACE FUNCTION count_unread_messages(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  unread_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO unread_count
  FROM messages m
  JOIN chat_participants cp ON m.chat_id = cp.chat_id
  WHERE cp.user_id = user_id
  AND m.sender_id != user_id
  AND m.read = FALSE;

  RETURN unread_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON><PERSON> function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(p_chat_id UUID, p_user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE messages
  SET read = TRUE
  WHERE chat_id = p_chat_id
  AND sender_id != p_user_id
  AND read = FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate function to get chats with unread counts
CREATE OR REPLACE FUNCTION get_chats_with_unread(user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  unread_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id AS chat_id,
    c.last_message,
    c.last_message_at,
    c.updated_at,
    COUNT(m.id) FILTER (WHERE m.sender_id != user_id AND m.read = FALSE) AS unread_count
  FROM
    chats c
  JOIN
    chat_participants cp ON c.id = cp.chat_id
  LEFT JOIN
    messages m ON c.id = m.chat_id
  WHERE
    cp.user_id = user_id
  GROUP BY
    c.id, c.last_message, c.last_message_at, c.updated_at
  ORDER BY
    c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if a user is VIP
CREATE OR REPLACE FUNCTION is_user_vip(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_vip BOOLEAN;
BEGIN
  SELECT p.is_vip
  INTO is_vip
  FROM profiles p
  WHERE p.id = user_id;

  RETURN COALESCE(is_vip, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create a chat between two users
CREATE OR REPLACE FUNCTION create_chat_between_users(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
  new_chat_id UUID;
  existing_chat_id UUID;
BEGIN
  -- Check if a chat already exists between these users
  SELECT c.id
  INTO existing_chat_id
  FROM chats c
  JOIN chat_participants cp1 ON c.id = cp1.chat_id
  JOIN chat_participants cp2 ON c.id = cp2.chat_id
  WHERE cp1.user_id = user1_id AND cp2.user_id = user2_id;

  IF existing_chat_id IS NOT NULL THEN
    RETURN existing_chat_id;
  END IF;

  -- Create a new chat
  INSERT INTO chats (created_at, updated_at)
  VALUES (now(), now())
  RETURNING id INTO new_chat_id;

  -- Add participants
  INSERT INTO chat_participants (chat_id, user_id)
  VALUES (new_chat_id, user1_id), (new_chat_id, user2_id);

  RETURN new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create policy to allow VIP users to send messages (if it doesn't exist)
-- Skip this policy creation as it's already handled in another migration
-- This avoids issues with dollar-quoted strings in DO blocks
