import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, Users, MessageSquare, Eye, Gift, Heart, Crown, Clock, ChevronDown, Home, Calendar } from 'lucide-react';
import Header from './Header';
import StoryBar from './StoryBar';
import UserCard from './UserCard';
import MessagesPage from './MessagesPage';
import OnlineFriendsPage from './OnlineFriendsPage';
import FreeTodayPage from './FreeTodayPage';
import ViewedMePage from './ViewedMePage';
import IViewedPage from './IViewedPage';
import ContactedMePage from './ContactedMePage';
import GiftsReceivedPage from './GiftsReceivedPage';
import { supabase } from '../lib/supabase';
import { Profile } from '../types/supabase';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from '../hooks/useNavigate';

function HomePage() {
  const { user } = useAuth();
  const { navigateToProfile } = useNavigate();
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [activeTab, setActiveTab] = useState('דאשי');
  const [showNewUsers, setShowNewUsers] = useState(false);
  const [showVipOnly, setShowVipOnly] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [isFreeToday, setIsFreeToday] = useState(false);
  const [isVip, setIsVip] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Counters for tabs
  const [onlineFriendsCount, setOnlineFriendsCount] = useState<number>(0);
  const [messagesCount, setMessagesCount] = useState<number>(0);
  const [viewedMeCount, setViewedMeCount] = useState<number>(0);
  const [iViewedCount, setIViewedCount] = useState<number>(0);
  const [contactedMeCount, setContactedMeCount] = useState<number>(0);
  const [giftsReceivedCount, setGiftsReceivedCount] = useState<number>(0);

  useEffect(() => {
    if (user?.id) {
      fetchProfiles();
      checkVipStatus();
      checkFreeToday();
      fetchCounts();

      // Check for tab parameter in URL
      const urlParams = new URLSearchParams(window.location.search);
      const tabParam = urlParams.get('tab');
      if (tabParam) {
        setActiveTab(tabParam);

        // Clear the URL parameter after setting the tab
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);

        // Check if we need to create a chat with someone
        const chatWithProfileId = sessionStorage.getItem('chat_with_profile_id');
        if (tabParam === 'הודעות' && chatWithProfileId) {
          console.log('Creating chat with profile ID:', chatWithProfileId);

          // Create a chat with this user
          const createChatWithUser = async () => {
            try {
              // Check if user is VIP
              const { data: isVip, error: vipError } = await supabase
                .from('profiles')
                .select('is_vip')
                .eq('id', user.id)
                .single();

              if (vipError || !isVip?.is_vip) {
                console.error('User is not VIP, cannot create chat');
                return;
              }

              // Get profile details of the user we want to chat with
              const { data: profileData, error: profileError } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', chatWithProfileId)
                .single();

              if (profileError) {
                console.error('Error fetching profile:', profileError);
                return;
              }

              console.log('Got profile data:', profileData);

              // Create a chat between the current user and the selected user
              const { data: chatId, error: chatError } = await supabase.rpc(
                'create_chat_between_users',
                {
                  user1_id: user.id,
                  user2_id: chatWithProfileId
                }
              );

              if (chatError) {
                console.error('Error creating chat:', chatError);
                return;
              }

              console.log('Created chat with ID:', chatId);

              // Store the chat ID and profile data in sessionStorage
              sessionStorage.setItem('selected_chat_id', chatId);
              sessionStorage.setItem('selected_chat_profile', JSON.stringify({
                id: profileData.id,
                username: profileData.username,
                profile_image_url: profileData.profile_image_url,
                is_online: profileData.is_online,
                is_vip: profileData.is_vip
              }));

              // Clear the stored profile ID
              sessionStorage.removeItem('chat_with_profile_id');
            } catch (err) {
              console.error('Error creating chat:', err);
            }
          };

          createChatWithUser();
        }
      }

      // Set up interval to refresh counts more frequently
      const countsInterval = setInterval(() => {
        fetchCounts();
      }, 10000); // Refresh every 10 seconds

      // Set up realtime subscription for online status changes
      const onlineStatusSubscription = supabase
        .channel('online_status_changes')
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'profiles',
            filter: 'is_online=eq.true'
          },
          () => {
            fetchCounts();
          }
        )
        .subscribe();

      // Set up realtime subscription for profile data changes
      const profileDataSubscription = supabase
        .channel('profile_data_changes')
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'profiles'
          },
          (payload) => {
            console.log('Profile data change detected:', payload);

            // Refresh profiles when any profile is updated
            console.log('Refreshing profiles due to profile update');
            fetchProfiles();
          }
        )
        .subscribe();

      // Listen for profile data updates from our custom event
      const handleProfileDataUpdate = (event: CustomEvent) => {
        const { userId, updateType, newImageUrl } = event.detail;
        console.log('📡 Received profileDataUpdated event:', { userId, updateType, newImageUrl });

        // Refresh profiles to get the latest data
        fetchProfiles();
      };

      window.addEventListener('profileDataUpdated', handleProfileDataUpdate as EventListener);

      return () => {
        clearInterval(countsInterval);
        onlineStatusSubscription.unsubscribe();
        profileDataSubscription.unsubscribe();
        window.removeEventListener('profileDataUpdated', handleProfileDataUpdate as EventListener);
      };
    }
  }, [activeTab, user?.id]);

  // Function to fetch counts for tabs
  const fetchCounts = async () => {
    if (!user?.id) return;

    try {
      // Get current timestamp
      const now = new Date();
      // Consider users active if they were active in the last 5 minutes
      const activeThreshold = new Date(now.getTime() - 5 * 60 * 1000).toISOString();

      // Fetch online users count
      const { count: onlineCount, error: onlineError } = await supabase
        .from('profiles')
        .select('id', { count: 'exact', head: true })
        .eq('is_online', true)
        .gt('last_active', activeThreshold)
        .neq('id', user.id);

      if (onlineError) {
        console.error('Error fetching online users count:', onlineError);
      } else {
        setOnlineFriendsCount(onlineCount || 0);
      }

      // Fetch unread messages count - simplified approach
      let unreadCount = 0;
      try {
        // First get user's chat IDs
        const { data: userChats, error: chatsError } = await supabase
          .from('chat_participants')
          .select('chat_id')
          .eq('user_id', user.id);

        if (!chatsError && userChats && userChats.length > 0) {
          const chatIds = userChats.map(uc => uc.chat_id);

          // Then count unread messages in those chats
          const { count: unreadCountResult, error: unreadError } = await supabase
            .from('messages')
            .select('id', { count: 'exact', head: true })
            .eq('read', false)
            .neq('sender_id', user.id)
            .in('chat_id', chatIds);

          if (!unreadError) {
            unreadCount = unreadCountResult || 0;
          }
        }
      } catch (err) {
        console.log('Error counting unread messages, using 0 count');
        unreadCount = 0;
      }

      setMessagesCount(unreadCount);

      // Fetch viewed me count
      const { data: viewedMeData, error: viewedMeError } = await supabase.rpc(
        'count_profiles_who_viewed_me',
        { user_id: user.id }
      );

      if (viewedMeError) {
        console.error('Error fetching viewed me count:', viewedMeError);
      } else {
        setViewedMeCount(viewedMeData || 0);
      }

      // Fetch i viewed count
      const { data: iViewedData, error: iViewedError } = await supabase.rpc(
        'count_profiles_i_viewed',
        { user_id: user.id }
      );

      if (iViewedError) {
        console.error('Error fetching i viewed count:', iViewedError);
      } else {
        setIViewedCount(iViewedData || 0);
      }

      // Fetch contacted me count
      const { data: contactedMeData, error: contactedMeError } = await supabase.rpc(
        'count_profiles_who_contacted_me',
        { user_id: user.id }
      );

      if (contactedMeError) {
        console.error('Error fetching contacted me count:', contactedMeError);
      } else {
        setContactedMeCount(contactedMeData || 0);
      }

      // Skip gifts count for now - table doesn't exist
      setGiftsReceivedCount(0);
    } catch (err) {
      console.error('Error fetching counts:', err);
    }
  };

  const checkVipStatus = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const { data, error: supabaseError } = await supabase
        .from('profiles')
        .select('is_vip')
        .eq('id', user.id)
        .single();

      if (supabaseError) {
        console.error('Supabase error checking VIP status:', supabaseError);
        setError('Failed to check VIP status. Please try again later.');
        return;
      }

      setIsVip(data?.is_vip ?? false);
    } catch (err) {
      console.error('Error checking VIP status:', err);
      setError('An unexpected error occurred. Please try again later.');
    }
  };

  const checkFreeToday = async () => {
    if (!user?.id) return;

    let retryCount = 0;
    const maxRetries = 3;

    const attemptCheck = async () => {
      try {
        setError(null);
        const { data, error: supabaseError } = await supabase
          .from('free_today')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

        if (supabaseError) {
          throw supabaseError;
        }

        setIsFreeToday(!!data);
      } catch (err) {
        console.error('Error checking free today status:', err);

        if (retryCount < maxRetries) {
          retryCount++;
          const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
          console.log(`Retrying in ${delay}ms... (Attempt ${retryCount} of ${maxRetries})`);

          setTimeout(attemptCheck, delay);
        } else {
          setError('Unable to check free today status. Please check your internet connection and try again.');
        }
      }
    };

    await attemptCheck();
  };

  const handleFreeToday = async () => {
    if (!user?.id || !isVip) return;

    try {
      setError(null);
      if (isFreeToday) {
        const { error: deleteError } = await supabase
          .from('free_today')
          .delete()
          .eq('user_id', user.id);

        if (deleteError) {
          console.error('Error removing free today status:', deleteError);
          setError('Failed to remove free today status. Please try again later.');
          return;
        }
        setIsFreeToday(false);
      } else {
        const { error: insertError } = await supabase
          .from('free_today')
          .insert([{ user_id: user.id }]);

        if (insertError) {
          console.error('Error setting free today status:', insertError);
          setError('Failed to set free today status. Please try again later.');
          return;
        }
        setIsFreeToday(true);
      }
    } catch (err) {
      console.error('Error updating free today status:', err);
      setError('An unexpected error occurred. Please try again later.');
    }
  };

  const fetchProfiles = async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('Fetching profiles for tab:', activeTab);

      let query = supabase.from('profiles').select('*');

      if (activeTab === 'פנויים היום') {
        console.log('Fetching free today profiles with expiry check');
        query = supabase
          .from('profiles')
          .select('*, free_today!inner(*)')
          .not('free_today', 'is', null)
          .gt('free_today.expires_at', new Date().toISOString());
      }

      // Add cache control to avoid stale data
      const timestamp = new Date().getTime();
      const { data: profilesData, error: profilesError } = await query
        .neq('id', user.id)
        .order('updated_at', { ascending: false });

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        setError('Failed to load profiles. Please try again later.');
        return;
      }

      const profilesWithPhotos = await Promise.all((profilesData || []).map(async (profile) => {
        try {
          // Check if user has a profile image set in metadata
          if (profile.user_metadata?.profile_image_url) {
            return { ...profile, photos: [profile.user_metadata.profile_image_url] };
          }

          // Fallback to first image in storage
          const { data: photos, error: photosError } = await supabase.storage
            .from('photos')
            .list(profile.id + '/', {
              limit: 1,
              sortBy: { column: 'name', order: 'asc' },
            });

          if (photosError) {
            console.error('Error fetching photos for profile:', photosError);
            return { ...profile, photos: [] };
          }

          if (!photos || photos.length === 0) {
            return { ...profile, photos: [] };
          }

          const { data: { publicUrl } } = supabase.storage
            .from('photos')
            .getPublicUrl(`${profile.id}/${photos[0].name}`);

          return { ...profile, photos: [publicUrl] };
        } catch (error) {
          console.error('Error fetching photos for profile:', error);
          return { ...profile, photos: [] };
        }
      }));

      setProfiles(profilesWithPhotos);
    } catch (error) {
      console.error('Error fetching profiles:', error);
      setError('An unexpected error occurred while loading profiles.');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredProfiles = () => {
    let filtered = [...profiles];

    if (searchTerm) {
      filtered = filtered.filter(profile =>
        profile.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        profile.city.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedGender) {
      filtered = filtered.filter(profile => profile.gender === selectedGender);
    }

    if (showVipOnly) {
      filtered = filtered.filter(profile => profile.is_vip);
    }

    if (showNewUsers) {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      filtered = filtered.filter(profile =>
        new Date(profile.created_at) > sevenDaysAgo
      );
    }

    switch (activeTab) {
      case 'חברים אונליין':
        filtered = filtered.filter(profile => profile.is_online);
        break;
      case 'צפו בי':
        filtered = filtered.filter(profile => profile.viewed_me);
        break;
      case 'צפיתי בהם':
        filtered = filtered.filter(profile => profile.i_viewed);
        break;
      case 'פנו אלי':
        filtered = filtered.filter(profile => profile.contacted_me);
        break;
      case 'מתנות שקיבלת':
        filtered = filtered.filter(profile => profile.sent_gift);
        break;
      case 'פנויים היום':
        filtered = filtered.filter(profile => profile.free_today);
        break;
      default:
        break;
    }

    return filtered;
  };

  const tabs = [
    { id: 'דאשי', icon: Home },
    { id: 'פנויים היום', icon: Calendar, className: 'bg-amber-100 border-2 border-amber-400 text-amber-800' },
    { id: 'חברים אונליין', icon: Users, count: onlineFriendsCount },
    { id: 'הודעות', icon: MessageSquare, count: messagesCount },
    { id: 'צפו בי', icon: Eye, count: viewedMeCount },
    { id: 'צפיתי בהם', icon: Eye, count: iViewedCount },
    { id: 'פנו אלי', icon: Heart, count: contactedMeCount },
    { id: 'מתנות שקיבלת', icon: Gift, count: giftsReceivedCount },
  ];

  const showFilters = activeTab === 'דאשי' || activeTab === 'חברים אונליין' || activeTab === 'פנויים היום';



  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      {error && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        </div>
      )}
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8">
        <StoryBar />
        <div className="flex flex-col md:flex-row gap-4 md:gap-8">
          {/* Desktop Sidebar */}
          <div className="hidden md:block w-64 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-sm p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <motion.button
                    key={tab.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center justify-between px-4 py-3 rounded-xl text-right transition-colors ${
                      activeTab === tab.id
                        ? tab.className || 'bg-pink-50 text-pink-600'
                        : tab.className || 'hover:bg-gray-50 text-gray-600'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <tab.icon className={`w-5 h-5 ${
                        activeTab === tab.id
                          ? tab.className ? 'text-amber-800' : 'text-pink-600'
                          : tab.className ? 'text-amber-600' : 'text-gray-400'
                      }`} />
                      <span className="font-medium">{tab.id}</span>
                    </div>
                    {tab.count !== undefined && (
                      <span className={`text-sm px-2 py-1 rounded-full ${
                        activeTab === tab.id
                          ? 'bg-pink-100 text-pink-600'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {tab.count}
                      </span>
                    )}
                  </motion.button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeTab === 'הודעות' ? (
              <MessagesPage />
            ) : activeTab === 'פנויים היום' ? (
              <FreeTodayPage
                profiles={getFilteredProfiles()}
                isVip={isVip}
              />
            ) : activeTab === 'חברים אונליין' ? (
              <OnlineFriendsPage />
            ) : activeTab === 'צפו בי' ? (
              <ViewedMePage />
            ) : activeTab === 'צפיתי בהם' ? (
              <IViewedPage />
            ) : activeTab === 'פנו אלי' ? (
              <ContactedMePage />
            ) : activeTab === 'מתנות שקיבלת' ? (
              <GiftsReceivedPage profiles={getFilteredProfiles()} />
            ) : (
              <>
                {/* Search and Filters */}
                {showFilters && (
                  <div className="bg-white rounded-2xl shadow-sm p-4 mb-4 md:mb-8">
                    {/* Mobile Filters */}
                    <div className="md:hidden flex items-center gap-2">
                      <div className="relative flex-1">
                        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          placeholder="חיפוש לפי שם או עיר..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-xl focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                        />
                      </div>

                      <button
                        onClick={() => setShowVipOnly(!showVipOnly)}
                        className={`flex items-center justify-center p-2 rounded-xl border transition-colors ${
                          showVipOnly
                            ? 'bg-yellow-50 border-yellow-500 text-yellow-700'
                            : 'border-gray-300 text-gray-700'
                        }`}
                      >
                        <Crown className={`w-5 h-5 ${showVipOnly ? 'text-yellow-500' : 'text-gray-400'}`} />
                      </button>

                      <button
                        onClick={() => setShowNewUsers(!showNewUsers)}
                        className={`flex items-center justify-center p-2 rounded-xl border transition-colors ${
                          showNewUsers
                            ? 'bg-green-50 border-green-500 text-green-700'
                            : 'border-gray-300 text-gray-700'
                        }`}
                      >
                        <Clock className={`w-5 h-5 ${showNewUsers ? 'text-green-500' : 'text-gray-400'}`} />
                      </button>

                      <button
                        onClick={() => setShowMobileFilters(!showMobileFilters)}
                        className={`flex items-center justify-center p-2 rounded-xl border transition-colors ${
                          selectedGender
                            ? 'bg-pink-50 border-pink-500 text-pink-700'
                            : 'border-gray-300 text-gray-700'
                        }`}
                      >
                        <Filter className="w-5 h-5" />
                      </button>
                    </div>

                    {/* Mobile Gender Filter Dropdown */}
                    <AnimatePresence>
                      {showMobileFilters && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          className="md:hidden mt-4"
                        >
                          <select
                            value={selectedGender}
                            onChange={(e) => {
                              setSelectedGender(e.target.value);
                              setShowMobileFilters(false);
                            }}
                            className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:border-pink-500 focus:ring-1 focus:ring-pink-500 bg-white"
                          >
                            <option value="">כל המגדרים</option>
                            <option value="male">גברים</option>
                            <option value="female">נשים</option>
                            <option value="couple">זוגות</option>
                          </select>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Desktop Filters */}
                    <div className="hidden md:flex flex-wrap md:flex-nowrap items-center gap-2">
                      {/* Search Input */}
                      <div className="relative flex-1 min-w-[200px]">
                        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          placeholder="חיפוש לפי שם או עיר..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-xl focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                        />
                      </div>

                      {/* Gender Filter */}
                      <div className="relative w-32">

                        <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <select
                          value={selectedGender}
                          onChange={(e) => setSelectedGender(e.target.value)}
                          className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-xl focus:border-pink-500 focus:ring-1 focus:ring-pink-500 appearance-none bg-white"
                        >
                          <option value="">כל המגדרים</option>
                          <option value="male">גברים</option>
                          <option value="female">נשים</option>
                          <option value="couple">זוגות</option>
                        </select>
                      </div>

                      {/* VIP Filter */}
                      <button
                        onClick={() => setShowVipOnly(!showVipOnly)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-xl border transition-colors whitespace-nowrap ${
                          showVipOnly
                            ? 'bg-yellow-50 border-yellow-500 text-yellow-700'
                            : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Crown className={`w-5 h-5 ${showVipOnly ? 'text-yellow-500' : 'text-gray-400'}`} />
                        <span>VIP</span>
                      </button>

                      {/* New Users Filter */}
                      <button
                        onClick={() => setShowNewUsers(!showNewUsers)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-xl border transition-colors whitespace-nowrap ${
                          showNewUsers
                            ? 'bg-green-50 border-green-500 text-green-700'
                            : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Clock className={`w-5 h-5 ${showNewUsers ? 'text-green-500' : 'text-gray-400'}`} />
                        <span>חדשים</span>
                      </button>

                      {/* Free Today Button - Only for VIP users */}
                      {isVip && (
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={handleFreeToday}
                          className={`flex items-center gap-2 px-4 py-2 rounded-xl border transition-colors whitespace-nowrap ${
                            isFreeToday
                              ? 'bg-amber-400 border-amber-500 text-white'
                              : 'bg-amber-400 border-amber-500 text-white opacity-90 hover:opacity-100'
                          }`}
                        >
                          <Calendar className="w-5 h-5" />
                          <span>{isFreeToday ? 'לא פנוי היום' : 'פנוי היום'}</span>
                        </motion.button>
                      )}
                    </div>
                  </div>
                )}

                {/* Profile Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3 md:gap-4">
                  {getFilteredProfiles().map((profile) => (
                    <UserCard key={profile.id} profile={profile} />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-40">
        <div className="flex justify-around items-center h-16">
          {tabs.slice(0, 5).map((tab) => (
            <motion.button
              key={tab.id}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveTab(tab.id)}
              className={`flex flex-col items-center gap-1 px-2 py-1 relative ${
                tab.className && activeTab === tab.id ? 'text-amber-800' : ''
              }`}
            >
              <tab.icon className={`w-6 h-6 ${
                activeTab === tab.id
                  ? tab.className ? 'text-amber-800' : 'text-pink-600'
                  : tab.className ? 'text-amber-600' : 'text-gray-400'
              }`} />
              <span className={`text-xs ${
                activeTab === tab.id
                  ? tab.className ? 'text-amber-800 font-medium' : 'text-pink-600 font-medium'
                  : 'text-gray-500'
              }`}>
                {tab.id}
              </span>
              {tab.count !== undefined && (
                <span className={`absolute -top-1 -right-1 text-xs w-4 h-4 flex items-center justify-center rounded-full ${
                  activeTab === tab.id
                    ? 'bg-pink-100 text-pink-600'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </motion.button>
          ))}
        </div>
      </nav>
    </div>
  );
}

export default HomePage;