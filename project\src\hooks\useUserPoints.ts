import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';

export function useUserPoints() {
  const { user } = useAuth();
  const [points, setPoints] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user points
  const fetchPoints = async () => {
    if (!user?.id) {
      setPoints(0);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('profiles')
        .select('points')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      setPoints(data?.points || 0);
    } catch (err) {
      console.error('Error fetching user points:', err);
      setError('שגיאה בטעינת נקודות');
      setPoints(0);
    } finally {
      setLoading(false);
    }
  };

  // Add points to user account
  const addPoints = async (amount: number, description?: string) => {
    if (!user?.id || amount <= 0) return false;

    try {
      // Create transaction record
      const { error: transactionError } = await supabase
        .from('points_transactions')
        .insert([{
          user_id: user.id,
          amount: amount,
          description: description || `הוספת ${amount} נקודות`
        }]);

      if (transactionError) {
        throw transactionError;
      }

      // The trigger will automatically update the user's points
      // Refresh points to get the updated value
      await fetchPoints();
      return true;
    } catch (err) {
      console.error('Error adding points:', err);
      setError('שגיאה בהוספת נקודות');
      return false;
    }
  };

  // Deduct points from user account
  const deductPoints = async (amount: number, description?: string) => {
    if (!user?.id || amount <= 0) return false;

    try {
      // Check if user has enough points
      if (points < amount) {
        setError('אין מספיק נקודות');
        return false;
      }

      // Create transaction record with negative amount
      const { error: transactionError } = await supabase
        .from('points_transactions')
        .insert([{
          user_id: user.id,
          amount: -amount,
          description: description || `ניכוי ${amount} נקודות`
        }]);

      if (transactionError) {
        throw transactionError;
      }

      // The trigger will automatically update the user's points
      // Refresh points to get the updated value
      await fetchPoints();
      return true;
    } catch (err) {
      console.error('Error deducting points:', err);
      setError('שגיאה בניכוי נקודות');
      return false;
    }
  };

  // Subscribe to points changes
  useEffect(() => {
    if (!user?.id) return;

    // Initial fetch
    fetchPoints();

    // Subscribe to points transactions for real-time updates
    const subscription = supabase
      .channel('points_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'points_transactions',
          filter: `user_id=eq.${user.id}`
        },
        () => {
          // Refresh points when a new transaction is added
          fetchPoints();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [user?.id]);

  return {
    points,
    loading,
    error,
    fetchPoints,
    addPoints,
    deductPoints,
    refreshPoints: fetchPoints
  };
}
