/*
  # Add is_blocked field to profiles

  1. Changes
    - Add is_blocked boolean field to profiles table
    - Set default value to false
    - Add index for better query performance

  2. Security
    - Only admins can update is_blocked field
*/

-- Add is_blocked column if it doesn't exist
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS is_blocked boolean DEFAULT false;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_is_blocked 
ON profiles(is_blocked);

-- Create policy for updating is_blocked
CREATE POLICY "Admins can update is_blocked"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.email() = '<EMAIL>')
  WITH CHECK (auth.email() = '<EMAIL>');