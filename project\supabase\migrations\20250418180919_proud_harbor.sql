/*
  # Fix Chat Creation Policies

  1. Changes
    - Drop and recreate chat policies to fix creation issue
    - Add proper policies for chat creation and management
    
  2. Security
    - Allow authenticated users to create chats
    - Maintain existing security model for chat access
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their chats" ON chats;
DROP POLICY IF EXISTS "Users can create chats" ON chats;

-- Create policies for chat management
CREATE POLICY "Users can create chats"
ON chats
FOR INSERT
TO authenticated
WITH CHECK (true);

CREATE POLICY "Users can view their chats"
ON chats
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = id
    AND chat_participants.user_id = auth.uid()
  )
);

-- Ensure RLS is enabled
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;