/*
  # Fix Storage Configuration

  1. Changes
    - Create storage bucket for photos if it doesn't exist
    - Set proper bucket configuration
    - Add RLS policies for photo management
    
  2. Security
    - Enable RLS on storage.objects
    - Add policies for authenticated users to:
      - Upload their own photos
      - View all photos
      - Delete their own photos
*/

-- Drop existing policies if they exist
DO $$
BEGIN
    DROP POLICY IF EXISTS "Users can upload their own photos" ON storage.objects;
    DROP POLICY IF EXISTS "Users can view all photos" ON storage.objects;
    DROP POLICY IF EXISTS "Users can delete their own photos" ON storage.objects;
EXCEPTION
    WHEN others THEN NULL;
END $$;

-- Create storage bucket for photos if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE id = 'photos'
    ) THEN
        INSERT INTO storage.buckets (id, name, public)
        VALUES ('photos', 'photos', true);

        -- Set bucket configuration
        UPDATE storage.buckets
        SET file_size_limit = 5242880, -- 5MB in bytes
            allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        WHERE id = 'photos';
    END IF;
END $$;

-- Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to upload their own photos
CREATE POLICY "Users can upload their own photos"
ON storage.objects FOR INSERT TO authenticated
WITH CHECK (
    bucket_id = 'photos' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Create policy to allow authenticated users to view all photos
CREATE POLICY "Users can view all photos"
ON storage.objects FOR SELECT TO authenticated
USING (bucket_id = 'photos');

-- Create policy to allow users to delete their own photos
CREATE POLICY "Users can delete their own photos"
ON storage.objects FOR DELETE TO authenticated
USING (
    bucket_id = 'photos' AND
    (storage.foldername(name))[1] = auth.uid()::text
);