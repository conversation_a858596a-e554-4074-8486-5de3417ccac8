import React, { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';

interface ProfileData {
  name: string;
  height: string;
  weight: string;
  hairColor: string;
  hairStyle: string;
  eyeColor: string;
  bodyType: string;
  maritalStatus: string;
  children: string;
  birthCountry: string;
  ethnicity: string;
  sexualPreference: string;
  swingingExperience: string;
  smokingHabits: string;
  drinkingHabits: string;
  aboutUs: string;
  lookingFor: string;
  seekingGender: string[];
  meetingTimes: string[];
}

interface SingleProfileFormProps {
  onNext: (data: ProfileData) => void;
  onBack: () => void;
  initialData?: Partial<ProfileData>;
}

function SingleProfileForm({ onNext, onBack, initialData = {} }: SingleProfileFormProps) {
  const [profileData, setProfileData] = useState<ProfileData>({
    name: initialData.name || '',
    height: initialData.height || '',
    weight: initialData.weight || '',
    hairColor: initialData.hairColor || '',
    hairStyle: initialData.hairStyle || '',
    eyeColor: initialData.eyeColor || '',
    bodyType: initialData.bodyType || '',
    maritalStatus: initialData.maritalStatus || '',
    children: initialData.children || '',
    birthCountry: initialData.birthCountry || 'ישראל',
    ethnicity: initialData.ethnicity || '',
    sexualPreference: initialData.sexualPreference || '',
    swingingExperience: initialData.swingingExperience || '',
    smokingHabits: initialData.smokingHabits || '',
    drinkingHabits: initialData.drinkingHabits || '',
    aboutUs: initialData.aboutUs || '',
    lookingFor: initialData.lookingFor || '',
    seekingGender: initialData.seekingGender || [],
    meetingTimes: initialData.meetingTimes || []
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value,
      // Reset children if marital status is single
      ...(name === 'maritalStatus' && value === 'single' ? { children: '' } : {})
    }));
  };

  const handleCheckboxChange = (field: 'seekingGender' | 'meetingTimes', value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext(profileData);
  };

  const gender = (window as any).__APP_STATE?.gender || 'male';
  const nameLabel = gender === 'male' ? 'שם גבר*' : 'שם אישה*';

  const renderSexualPreferenceOptions = () => {
    if (gender === 'male') {
      return (
        <>
          <option value="">בחר</option>
          <option value="straight">סטרייט</option>
          <option value="bisexual">דו מיני</option>
          <option value="gay">הומו</option>
        </>
      );
    } else if (gender === 'female') {
      return (
        <>
          <option value="">בחר</option>
          <option value="straight">סטרייט</option>
          <option value="bisexual">דו מינית</option>
          <option value="lesbian">לסבית</option>
        </>
      );
    }
    return <option value="">בחר</option>;
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white rounded-2xl shadow-[0_20px_50px_rgba(255,192,203,0.3)] p-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="text-right mb-8">
            <h1 className="text-2xl font-bold text-black">פרטים נוספים</h1>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">{nameLabel}</label>
                <input
                  type="text"
                  name="name"
                  value={profileData.name}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                />
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">גובה*</label>
                <select
                  name="height"
                  value={profileData.height}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  {Array.from({ length: 61 }, (_, i) => i + 140).map(height => (
                    <option key={height} value={height}>{height} ס"מ</option>
                  ))}
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">משקל</label>
                <select
                  name="weight"
                  value={profileData.weight}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                >
                  <option value="">בחר</option>
                  {Array.from({ length: 151 }, (_, i) => i + 40).map(weight => (
                    <option key={weight} value={weight}>{weight} ק"ג</option>
                  ))}
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">צבע השער*</label>
                <select
                  name="hairColor"
                  value={profileData.hairColor}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="black">שחור</option>
                  <option value="brown">חום</option>
                  <option value="blonde">בלונדיני</option>
                  <option value="red">ג'ינג'י</option>
                  <option value="gray">אפור</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">תסרוקת*</label>
                <select
                  name="hairStyle"
                  value={profileData.hairStyle}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="long">ארוך</option>
                  <option value="short">קצר</option>
                  <option value="bald">קרח</option>
                  <option value="medium">בינוני</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">צבע עיניים*</label>
                <select
                  name="eyeColor"
                  value={profileData.eyeColor}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="brown">חום</option>
                  <option value="blue">כחול</option>
                  <option value="green">ירוק</option>
                  <option value="hazel">דבש</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">מבנה גוף*</label>
                <select
                  name="bodyType"
                  value={profileData.bodyType}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="slim">רזה</option>
                  <option value="athletic">אתלטי</option>
                  <option value="average">ממוצע</option>
                  <option value="curvy">מלא</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">מצב משפחתי*</label>
                <select
                  name="maritalStatus"
                  value={profileData.maritalStatus}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="single">רווק/ה</option>
                  <option value="married">נשוי/אה</option>
                  <option value="divorced">גרוש/ה</option>
                </select>
              </div>

              {profileData.maritalStatus !== 'single' && (
                <div className="flex flex-col">
                  <label className="text-sm font-medium text-black mb-1">מס' ילדים*</label>
                  <select
                    name="children"
                    value={profileData.children}
                    onChange={handleInputChange}
                    className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                    required
                  >
                    <option value="">בחר</option>
                    {Array.from({ length: 11 }, (_, i) => (
                      <option key={i} value={i}>{i}</option>
                    ))}
                  </select>
                </div>
              )}

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">מוצא*</label>
                <select
                  name="ethnicity"
                  value={profileData.ethnicity}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="ashkenazi">אשכנזי</option>
                  <option value="sephardi">ספרדי</option>
                  <option value="mixed">מעורב</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">העדפה מינית*</label>
                <select
                  name="sexualPreference"
                  value={profileData.sexualPreference}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  {renderSexualPreferenceOptions()}
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">ניסיון בחילופי זוגות*</label>
                <select
                  name="swingingExperience"
                  value={profileData.swingingExperience}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="none">ללא ניסיון</option>
                  <option value="some">מעט ניסיון</option>
                  <option value="experienced">מנוסה</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">הרגלי עישון*</label>
                <select
                  name="smokingHabits"
                  value={profileData.smokingHabits}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="non-smoker">לא מעשן</option>
                  <option value="occasional">מעשן לעיתים</option>
                  <option value="regular">מעשן קבוע</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">הרגלי שתיה*</label>
                <select
                  name="drinkingHabits"
                  value={profileData.drinkingHabits}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="non-drinker">לא שותה</option>
                  <option value="social">שותה חברתי</option>
                  <option value="regular">שותה באופן קבוע</option>
                </select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">מעט עלינו (לפחות 10 תווים)*</label>
                <textarea
                  name="aboutUs"
                  value={profileData.aboutUs}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500 min-h-[100px]"
                  minLength={10}
                  required
                />
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">מה אני/אנחנו מחפש/ים (לפחות 10 תווים)*</label>
                <textarea
                  name="lookingFor"
                  value={profileData.lookingFor}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500 min-h-[100px]"
                  minLength={10}
                  required
                />
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">את מי אני/אנחנו מחפש/ים*</label>
                <div className="flex gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={profileData.seekingGender.includes('male')}
                      onChange={() => handleCheckboxChange('seekingGender', 'male')}
                      className="rounded text-pink-600 focus:ring-pink-500"
                    />
                    <span>גבר</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={profileData.seekingGender.includes('couple')}
                      onChange={() => handleCheckboxChange('seekingGender', 'couple')}
                      className="rounded text-pink-600 focus:ring-pink-500"
                    />
                    <span>זוג</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={profileData.seekingGender.includes('female')}
                      onChange={() => handleCheckboxChange('seekingGender', 'female')}
                      className="rounded text-pink-600 focus:ring-pink-500"
                    />
                    <span>אישה</span>
                  </label>
                </div>
              </div>

              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">שעות מפגש מועדפות*</label>
                <div className="flex gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={profileData.meetingTimes.includes('morning')}
                      onChange={() => handleCheckboxChange('meetingTimes', 'morning')}
                      className="rounded text-pink-600 focus:ring-pink-500"
                    />
                    <span>בוקר</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={profileData.meetingTimes.includes('afternoon')}
                      onChange={() => handleCheckboxChange('meetingTimes', 'afternoon')}
                      className="rounded text-pink-600 focus:ring-pink-500"
                    />
                    <span>צהריים</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={profileData.meetingTimes.includes('evening')}
                      onChange={() => handleCheckboxChange('meetingTimes', 'evening')}
                      className="rounded text-pink-600 focus:ring-pink-500"
                    />
                    <span>ערב</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={onBack}
              className="flex items-center justify-center gap-2 px-6 py-3 border-2 border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <ArrowRight className="w-5 h-5" />
              חזור
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              className="flex-1 bg-pink-600 text-white py-3 px-6 rounded-xl font-medium hover:bg-pink-700 transition-colors duration-300"
            >
              המשך
            </motion.button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default SingleProfileForm;