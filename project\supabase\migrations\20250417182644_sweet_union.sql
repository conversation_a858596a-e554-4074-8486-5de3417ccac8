/*
  # Update Chat Policies and Relationships

  1. Changes
    - Drop and recreate policies with correct joins
    - Ensure proper access control for chat functionality
    
  2. Security
    - Update policies to use proper table relationships
    - Maintain existing security model
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view chat participants" ON chat_participants;
DROP POLICY IF EXISTS "Users can view messages in their chats" ON messages;
DROP POLICY IF EXISTS "Users can send messages to their chats" ON messages;
DROP POLICY IF EXISTS "Users can view their chats" ON chats;

-- Update policies with correct joins
CREATE POLICY "Users can view their chats"
ON chats
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = chats.id
    AND chat_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Users can view chat participants"
ON chat_participants
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM chat_participants cp
    WHERE cp.chat_id = chat_participants.chat_id
    AND cp.user_id = auth.uid()
  )
);

CREATE POLICY "Users can view messages in their chats"
ON messages
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Users can send messages to their chats"
ON messages
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
  AND sender_id = auth.uid()
);