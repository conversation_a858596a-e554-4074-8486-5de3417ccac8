-- Fix the profile views functions
-- This migration fixes the functions that retrieve profile views

-- First, drop the existing functions
DROP FUNCTION IF EXISTS get_profiles_who_viewed_me(uuid);
DROP FUNCTION IF EXISTS get_profiles_i_viewed(uuid);

-- <PERSON>reate improved function to get profiles that viewed a user
CREATE OR REPLACE FUNCTION get_profiles_who_viewed_me(user_id uuid)
RETURNS TABLE (
  id uuid,
  username text,
  gender text,
  birth_date date,
  partner_birth_date date,
  city text,
  area text,
  phone text,
  height integer,
  weight integer,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children integer,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text[],
  meeting_times text[],
  created_at timestamptz,
  updated_at timestamptz,
  user_metadata jsonb,
  profile_data jsonb,
  is_vip boolean,
  is_blocked boolean,
  points integer,
  is_online boolean,
  view_date timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.*,
    EXISTS(SELECT 1 FROM vip_subscriptions vs WHERE vs.user_id = p.id AND vs.expires_at > NOW()) as is_vip,
    EXISTS(SELECT 1 FROM blocked_users bu WHERE (bu.blocker_id = user_id AND bu.blocked_id = p.id) OR (bu.blocker_id = p.id AND bu.blocked_id = user_id)) as is_blocked,
    pv.created_at as view_date
  FROM 
    profiles p
  JOIN 
    profile_views pv ON p.id = pv.viewer_id
  WHERE 
    pv.viewed_id = user_id
  ORDER BY 
    pv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create improved function to get profiles that a user viewed
CREATE OR REPLACE FUNCTION get_profiles_i_viewed(user_id uuid)
RETURNS TABLE (
  id uuid,
  username text,
  gender text,
  birth_date date,
  partner_birth_date date,
  city text,
  area text,
  phone text,
  height integer,
  weight integer,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children integer,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text[],
  meeting_times text[],
  created_at timestamptz,
  updated_at timestamptz,
  user_metadata jsonb,
  profile_data jsonb,
  is_vip boolean,
  is_blocked boolean,
  points integer,
  is_online boolean,
  view_date timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.*,
    EXISTS(SELECT 1 FROM vip_subscriptions vs WHERE vs.user_id = p.id AND vs.expires_at > NOW()) as is_vip,
    EXISTS(SELECT 1 FROM blocked_users bu WHERE (bu.blocker_id = user_id AND bu.blocked_id = p.id) OR (bu.blocker_id = p.id AND bu.blocked_id = user_id)) as is_blocked,
    pv.created_at as view_date
  FROM 
    profiles p
  JOIN 
    profile_views pv ON p.id = pv.viewed_id
  WHERE 
    pv.viewer_id = user_id
  ORDER BY 
    pv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
