import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Gift, X, Send, <PERSON>ertTriangle, Loader2, CreditCard, Info, Heart, Flower, Wine, Cookie, CircleDot, Star, Diamond, Banknote, HeartCrack } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import { useUserPoints } from '../hooks/useUserPoints';

interface Gift {
  id: string;
  name: string;
  price: number;
  image_url: string | null;
}

interface SendGiftDialogProps {
  isOpen: boolean;
  onClose: () => void;
  receiverId: string;
  receiverName: string;
}

// Function to get the appropriate icon component based on gift data
const getGiftIcon = (gift: Gift) => {
  const iconName = gift.image_url || gift.icon || '';

  // Extract icon name from URL if it's a URL
  let iconType = iconName;
  if (iconName.includes('/')) {
    // Extract filename from URL and remove extension
    const filename = iconName.split('/').pop() || '';
    iconType = filename.split('.')[0];
  }

  switch (iconType.toLowerCase()) {
    case 'heart':
      return <Heart className="w-6 h-6 text-red-500" />;
    case 'flower':
      return <Flower className="w-6 h-6 text-pink-500" />;
    case 'champagne':
    case 'glass':
    case 'wine':
      return <Wine className="w-6 h-6 text-purple-500" />;
    case 'chocolate':
    case 'candy':
    case 'cookie':
      return <Cookie className="w-6 h-6 text-yellow-500" />;
    case 'ring':
      return <CircleDot className="w-6 h-6 text-yellow-400" />;
    case 'star':
      return <Star className="w-6 h-6 text-yellow-400" />;
    case 'diamond':
      return <Diamond className="w-6 h-6 text-blue-400" />;
    case 'banknote':
    case 'money':
      return <Banknote className="w-6 h-6 text-green-500" />;
    case 'heart-crack':
    case 'heartcrack':
      return <HeartCrack className="w-6 h-6 text-red-500" />;
    case 'gift':
    default:
      return <Gift className="w-6 h-6 text-pink-500" />;
  }
};

function SendGiftDialog({ isOpen, onClose, receiverId, receiverName }: SendGiftDialogProps) {
  const { user } = useAuth();
  const { points: userPoints, deductPoints, refreshPoints } = useUserPoints();
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [selectedGift, setSelectedGift] = useState<string | null>(null);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [insufficientPoints, setInsufficientPoints] = useState(false);



  useEffect(() => {
    if (isOpen) {
      fetchGifts();
      refreshPoints(); // Refresh points when dialog opens
    } else {
      // Reset state when dialog closes
      setSelectedGift(null);
      setMessage('');
      setError(null);
      setSuccess(false);
      setInsufficientPoints(false);
    }
  }, [isOpen]); // Remove refreshPoints from dependencies



  const fetchGifts = async () => {
    try {
      setLoading(true);
      setError(null);
      setInsufficientPoints(false);

      const { data, error: fetchError } = await supabase
        .from('gifts')
        .select('*')
        .order('price', { ascending: true });

      if (fetchError) throw fetchError;

      // If no gifts found, create default gifts in database
      if (!data || data.length === 0) {
        console.log('No gifts found, creating default gifts in database...');
        await createDefaultGifts();

        // Fetch again after creating
        const { data: newData, error: newError } = await supabase
          .from('gifts')
          .select('*')
          .order('price', { ascending: true });

        if (newError) {
          console.error('Error fetching gifts after creation:', newError);
          throw newError;
        }

        console.log('Gifts after creation:', newData);
        setGifts(newData || []);
      } else {
        console.log('Found existing gifts:', data);
        setGifts(data);
      }
    } catch (err) {
      console.error('Error fetching gifts:', err);
      setError('אירעה שגיאה בטעינת המתנות. אנא נסה שוב מאוחר יותר.');
    } finally {
      setLoading(false);
    }
  };



  const createDefaultGifts = async () => {
    try {
      const defaultGifts = [
        { name: 'לב', price: 10, image_url: 'heart' },
        { name: 'פרח', price: 15, image_url: 'flower' },
        { name: 'שמפניה', price: 25, image_url: 'champagne' },
        { name: 'שוקולד', price: 20, image_url: 'chocolate' },
        { name: 'טבעת', price: 50, image_url: 'ring' },
        { name: 'כוכב', price: 30, image_url: 'star' },
        { name: 'יהלום', price: 100, image_url: 'diamond' },
        { name: 'כסף', price: 75, image_url: 'money' }
      ];

      const { data: insertedGifts, error } = await supabase
        .from('gifts')
        .insert(defaultGifts)
        .select();

      if (error) {
        console.error('Error creating default gifts:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));
      } else {
        console.log('✅ Default gifts created successfully');
        console.log('Inserted gifts:', insertedGifts);
      }
    } catch (err) {
      console.error('Error in createDefaultGifts:', err);
    }
  };

  const handleSendGift = async () => {
    if (!user?.id || !selectedGift) return;

    // Find the selected gift to get its price
    const selectedGiftObj = gifts.find(gift => gift.id === selectedGift);
    if (!selectedGiftObj) return;

    // Check if user has enough points
    if (userPoints < selectedGiftObj.price) {
      setInsufficientPoints(true);
      return;
    }

    try {
      setSending(true);
      setError(null);

      // 1. Send gift notification to receiver
      console.log('Sending gift:', selectedGiftObj.name, 'to user:', receiverId);

      const giftMessage = `🎁 ${selectedGiftObj.name} (${selectedGiftObj.price} נקודות)`;
      const fullMessage = message.trim() ? `${giftMessage}\n\n${message.trim()}` : giftMessage;

      // Send as notification to the receiver
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert([{
          user_id: receiverId,
          type: 'gift',
          title: 'מתנה חדשה!',
          message: fullMessage,
          data: {
            gift_name: selectedGiftObj.name,
            gift_price: selectedGiftObj.price,
            sender_id: user.id
          }
        }]);

      if (notificationError) {
        console.error('Error sending notification:', notificationError);
        // Don't throw error, just log it - we'll still deduct points
      }

      // 2. Deduct points from sender using the hook
      const success = await deductPoints(selectedGiftObj.price, `שליחת מתנה: ${selectedGiftObj.name}`);

      if (!success) {
        throw new Error('Failed to deduct points');
      }

      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
        setSelectedGift(null);
        setMessage('');
      }, 2000);
    } catch (err) {
      console.error('Error sending gift:', err);
      console.error('Error details:', JSON.stringify(err, null, 2));

      // More detailed error message for debugging
      let errorMessage = 'Unknown error';
      if (err && typeof err === 'object') {
        if ('message' in err) {
          errorMessage = err.message;
        } else if ('error' in err) {
          errorMessage = err.error;
        } else if ('details' in err) {
          errorMessage = err.details;
        }
      }
      setError(`אירעה שגיאה בשליחת המתנה: ${errorMessage}`);
    } finally {
      setSending(false);
    }
  };

  const handleAddPoints = () => {
    // Here you would redirect to a page to purchase points
    // For now, we'll just close the dialog
    onClose();
    // Redirect to points purchase page
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'points' } }));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={(e) => {
            // Close dialog when clicking outside
            if (e.target === e.currentTarget) {
              onClose();
            }
          }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="bg-white rounded-xl shadow-xl w-full max-w-md overflow-hidden max-h-[90vh]"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between bg-gradient-to-r from-purple-600 to-pink-600 p-3 text-white">
              <div className="flex items-center gap-2">
                <Gift className="w-4 h-4" />
                <h3 className="text-base font-semibold">שלח מתנה ל{receiverName}</h3>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4 overflow-y-auto max-h-[70vh]">
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="w-6 h-6 text-pink-600 animate-spin" />
                </div>
              ) : error ? (
                <div className="bg-red-50 text-red-700 p-3 rounded-lg flex items-center gap-2 mb-3 text-sm">
                  <AlertTriangle className="w-4 h-4 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              ) : success ? (
                <div className="flex flex-col items-center justify-center h-32 text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                    <Gift className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">המתנה נשלחה בהצלחה!</h4>
                  <p className="text-gray-600 text-sm">{receiverName} יקבל את המתנה שלך בקרוב.</p>
                </div>
              ) : gifts.length === 0 ? (
                <div className="text-center py-8">
                  <Gift className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 mb-4">אין מתנות זמינות כרגע</p>
                  <button
                    onClick={fetchGifts}
                    className="bg-pink-500 text-white px-4 py-2 rounded-lg hover:bg-pink-600"
                  >
                    רענן
                  </button>
                </div>
              ) : insufficientPoints ? (
                <div className="text-center py-2">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Info className="w-6 h-6 text-yellow-600" />
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">אין מספיק נקודות</h4>
                  <p className="text-gray-600 text-sm mb-4">
                    יש לך {userPoints} נקודות, אבל המתנה עולה {gifts.find(g => g.id === selectedGift)?.price} נקודות.
                  </p>
                  <div className="flex flex-col gap-2">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleAddPoints}
                      className="flex items-center justify-center gap-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm"
                    >
                      <CreditCard className="w-4 h-4" />
                      <span>טען נקודות</span>
                    </motion.button>
                    <button
                      onClick={() => setInsufficientPoints(false)}
                      className="text-gray-600 hover:text-gray-800 transition-colors text-sm"
                    >
                      חזור לבחירת מתנה
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-3">
                    <p className="text-gray-600 text-sm">
                      בחר מתנה לשלוח ל{receiverName}
                    </p>
                    <div className="flex items-center gap-1 bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs">
                      <CreditCard className="w-3 h-3" />
                      <span>{userPoints} נקודות</span>
                    </div>
                  </div>

                  {/* Gift Selection */}
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {gifts.map((gift) => (
                      <motion.div
                        key={gift.id}
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => setSelectedGift(gift.id)}
                        className={`border rounded-lg overflow-hidden cursor-pointer transition-all ${
                          selectedGift === gift.id
                            ? 'border-pink-500 ring-1 ring-pink-500 ring-opacity-50'
                            : 'border-gray-200 hover:border-pink-200'
                        }`}
                      >
                        <div className="aspect-square bg-gray-100 flex items-center justify-center">
                          <div className="w-full h-full flex items-center justify-center">
                            {getGiftIcon(gift)}
                          </div>
                        </div>
                        <div className="p-1 text-center">
                          <div className="font-medium text-xs text-gray-900">{gift.name}</div>
                          <div className={`text-xs ${userPoints >= gift.price ? 'text-green-600' : 'text-red-600'}`}>
                            {gift.price}
                            {userPoints < gift.price && ' ❌'}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Message Input */}
                  <div className="mb-4">
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      הוסף הודעה (אופציונלי)
                    </label>
                    <textarea
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500 resize-none text-sm"
                      rows={2}
                      placeholder="כתוב הודעה אישית..."
                      maxLength={100}
                    />
                    <div className="text-xs text-gray-500 text-left mt-1">
                      {message.length}/100
                    </div>
                  </div>

                  {/* Send Button */}
                  <div className="flex justify-between items-center">
                    <div className="text-xs">
                      {(() => {
                        const selectedGiftObj = gifts.find(g => g.id === selectedGift);
                        if (selectedGiftObj) {
                          return (
                            <div className="flex items-center gap-1">
                              <span className="text-gray-600">מחיר:</span>
                              <span className={`font-medium ${userPoints >= selectedGiftObj.price ? 'text-green-600' : 'text-red-600'}`}>
                                {selectedGiftObj.price} נקודות
                              </span>
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={handleSendGift}
                      disabled={!selectedGift || sending}
                      className={`flex items-center gap-2 px-4 py-1.5 rounded-lg text-sm ${
                        !selectedGift || sending
                          ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                          : 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                      }`}
                    >
                      {sending ? (
                        <>
                          <Loader2 className="w-3 h-3 animate-spin" />
                          <span>שולח...</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-3 h-3" />
                          <span>שלח מתנה</span>
                        </>
                      )}
                    </motion.button>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default SendGiftDialog;
