import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Gift, X, Send, <PERSON>ertT<PERSON>gle, Loader2, CreditCard, Info, Heart, Flower, Wine, Cookie, CircleDot, Star, Diamond, Banknote, HeartCrack } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import { useUserPoints } from '../hooks/useUserPoints';

interface Gift {
  id: string;
  name: string;
  price: number;
  image_url: string | null;
}

interface SendGiftDialogProps {
  isOpen: boolean;
  onClose: () => void;
  receiverId: string;
  receiverName: string;
}

// Function to get the appropriate icon component based on icon name
const getGiftIcon = (iconName: string | null) => {
  switch (iconName) {
    case 'heart':
      return <Heart className="w-6 h-6 text-red-500" />;
    case 'flower':
      return <Flower className="w-6 h-6 text-pink-500" />;
    case 'glass':
      return <Wine className="w-6 h-6 text-purple-500" />;
    case 'candy':
      return <Cookie className="w-6 h-6 text-yellow-500" />;
    case 'ring':
      return <CircleDot className="w-6 h-6 text-yellow-400" />;
    case 'star':
      return <Star className="w-6 h-6 text-yellow-400" />;
    case 'diamond':
      return <Diamond className="w-6 h-6 text-blue-400" />;
    case 'banknote':
      return <Banknote className="w-6 h-6 text-green-500" />;
    case 'heart-crack':
      return <HeartCrack className="w-6 h-6 text-red-500" />;
    case 'gift':
    default:
      return <Gift className="w-6 h-6 text-pink-500" />;
  }
};

function SendGiftDialog({ isOpen, onClose, receiverId, receiverName }: SendGiftDialogProps) {
  const { user } = useAuth();
  const { points: userPoints, deductPoints, refreshPoints } = useUserPoints();
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [selectedGift, setSelectedGift] = useState<string | null>(null);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [insufficientPoints, setInsufficientPoints] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchGifts();
      refreshPoints(); // Refresh points when dialog opens
    }
  }, [isOpen, refreshPoints]);



  const fetchGifts = async () => {
    try {
      setLoading(true);
      setError(null);
      setInsufficientPoints(false);

      const { data, error: fetchError } = await supabase
        .from('gifts')
        .select('*')
        .order('price', { ascending: true });

      if (fetchError) throw fetchError;

      setGifts(data || []);
    } catch (err) {
      console.error('Error fetching gifts:', err);
      setError('אירעה שגיאה בטעינת המתנות. אנא נסה שוב מאוחר יותר.');
    } finally {
      setLoading(false);
    }
  };

  const handleSendGift = async () => {
    if (!user?.id || !selectedGift) return;

    // Find the selected gift to get its price
    const selectedGiftObj = gifts.find(gift => gift.id === selectedGift);
    if (!selectedGiftObj) return;

    // Check if user has enough points
    if (userPoints < selectedGiftObj.price) {
      setInsufficientPoints(true);
      return;
    }

    try {
      setSending(true);
      setError(null);

      // Start a transaction to send gift and deduct points
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      // 1. Insert the gift
      const { error: sendError } = await supabase
        .from('user_gifts')
        .insert([{
          sender_id: user.id,
          receiver_id: receiverId,
          gift_id: selectedGift,
          message: message.trim() || null
        }]);

      if (sendError) throw sendError;

      // 2. Deduct points from sender using the hook
      const success = await deductPoints(selectedGiftObj.price, `שליחת מתנה: ${selectedGiftObj.name}`);

      if (!success) {
        throw new Error('Failed to deduct points');
      }

      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
        setSelectedGift(null);
        setMessage('');
      }, 2000);
    } catch (err) {
      console.error('Error sending gift:', err);
      setError('אירעה שגיאה בשליחת המתנה. אנא נסה שוב מאוחר יותר.');
    } finally {
      setSending(false);
    }
  };

  const handleAddPoints = () => {
    // Here you would redirect to a page to purchase points
    // For now, we'll just close the dialog
    onClose();
    // Redirect to points purchase page
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'points' } }));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={(e) => {
            // Close dialog when clicking outside
            if (e.target === e.currentTarget) {
              onClose();
            }
          }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="bg-white rounded-xl shadow-xl w-full max-w-md overflow-hidden max-h-[90vh]"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between bg-gradient-to-r from-purple-600 to-pink-600 p-3 text-white">
              <div className="flex items-center gap-2">
                <Gift className="w-4 h-4" />
                <h3 className="text-base font-semibold">שלח מתנה ל{receiverName}</h3>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4 overflow-y-auto max-h-[70vh]">
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="w-6 h-6 text-pink-600 animate-spin" />
                </div>
              ) : error ? (
                <div className="bg-red-50 text-red-700 p-3 rounded-lg flex items-center gap-2 mb-3 text-sm">
                  <AlertTriangle className="w-4 h-4 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              ) : success ? (
                <div className="flex flex-col items-center justify-center h-32 text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                    <Gift className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">המתנה נשלחה בהצלחה!</h4>
                  <p className="text-gray-600 text-sm">{receiverName} יקבל את המתנה שלך בקרוב.</p>
                </div>
              ) : insufficientPoints ? (
                <div className="text-center py-2">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Info className="w-6 h-6 text-yellow-600" />
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">אין מספיק נקודות</h4>
                  <p className="text-gray-600 text-sm mb-4">
                    יש לך {userPoints} נקודות, אבל המתנה עולה {gifts.find(g => g.id === selectedGift)?.price} נקודות.
                  </p>
                  <div className="flex flex-col gap-2">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleAddPoints}
                      className="flex items-center justify-center gap-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm"
                    >
                      <CreditCard className="w-4 h-4" />
                      <span>טען נקודות</span>
                    </motion.button>
                    <button
                      onClick={() => setInsufficientPoints(false)}
                      className="text-gray-600 hover:text-gray-800 transition-colors text-sm"
                    >
                      חזור לבחירת מתנה
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-3">
                    <p className="text-gray-600 text-sm">
                      בחר מתנה לשלוח ל{receiverName}
                    </p>
                    <div className="flex items-center gap-1 bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs">
                      <CreditCard className="w-3 h-3" />
                      <span>{userPoints} נקודות</span>
                    </div>
                  </div>

                  {/* Gift Selection */}
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {gifts.map((gift) => (
                      <motion.div
                        key={gift.id}
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => setSelectedGift(gift.id)}
                        className={`border rounded-lg overflow-hidden cursor-pointer transition-all ${
                          selectedGift === gift.id
                            ? 'border-pink-500 ring-1 ring-pink-500 ring-opacity-50'
                            : 'border-gray-200 hover:border-pink-200'
                        }`}
                      >
                        <div className="aspect-square bg-gray-100 flex items-center justify-center">
                          <div className="w-full h-full flex items-center justify-center">
                            {getGiftIcon(gift.image_url)}
                          </div>
                        </div>
                        <div className="p-1 text-center">
                          <div className="font-medium text-xs text-gray-900">{gift.name}</div>
                          <div className={`text-xs ${userPoints >= gift.price ? 'text-green-600' : 'text-red-600'}`}>
                            {gift.price}
                            {userPoints < gift.price && ' ❌'}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Message Input */}
                  <div className="mb-4">
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      הוסף הודעה (אופציונלי)
                    </label>
                    <textarea
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500 resize-none text-sm"
                      rows={2}
                      placeholder="כתוב הודעה אישית..."
                      maxLength={100}
                    />
                    <div className="text-xs text-gray-500 text-left mt-1">
                      {message.length}/100
                    </div>
                  </div>

                  {/* Send Button */}
                  <div className="flex justify-between items-center">
                    <div className="text-xs">
                      {(() => {
                        const selectedGiftObj = gifts.find(g => g.id === selectedGift);
                        if (selectedGiftObj) {
                          return (
                            <div className="flex items-center gap-1">
                              <span className="text-gray-600">מחיר:</span>
                              <span className={`font-medium ${userPoints >= selectedGiftObj.price ? 'text-green-600' : 'text-red-600'}`}>
                                {selectedGiftObj.price} נקודות
                              </span>
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={handleSendGift}
                      disabled={!selectedGift || sending}
                      className={`flex items-center gap-2 px-4 py-1.5 rounded-lg text-sm ${
                        !selectedGift || sending
                          ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                          : 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                      }`}
                    >
                      {sending ? (
                        <>
                          <Loader2 className="w-3 h-3 animate-spin" />
                          <span>שולח...</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-3 h-3" />
                          <span>שלח מתנה</span>
                        </>
                      )}
                    </motion.button>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default SendGiftDialog;
