-- Add image support to messages table
-- Add image_url column if it doesn't exist
ALTER TABLE messages ADD COLUMN IF NOT EXISTS image_url text;

-- Create index for better performance when filtering by image messages
CREATE INDEX IF NOT EXISTS idx_messages_image_url ON messages(image_url) WHERE image_url IS NOT NULL;

-- Create storage bucket for chat images if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('chat-images', 'chat-images', true)
ON CONFLICT (id) DO NOTHING;

-- Set up RLS policies for chat-images bucket
CREATE POLICY "Users can upload chat images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'chat-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view chat images" ON storage.objects
FOR SELECT USING (bucket_id = 'chat-images');

CREATE POLICY "Users can delete their own chat images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'chat-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Create new function to send message with image support
CREATE OR REPLACE FUNCTION send_message_with_image(
  p_chat_id uuid,
  p_sender_id uuid,
  p_content text DEFAULT '',
  p_image_url text DEFAULT NULL,
  p_reply_to_id uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  v_message_id uuid;
  v_other_user_id uuid;
BEGIN
  -- Validate that the sender is part of the chat
  IF NOT EXISTS (
    SELECT 1 FROM chat_participants 
    WHERE chat_id = p_chat_id AND user_id = p_sender_id
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this chat';
  END IF;

  -- Validate that we have either content or image
  IF (p_content IS NULL OR trim(p_content) = '') AND p_image_url IS NULL THEN
    RAISE EXCEPTION 'Message must have either content or image';
  END IF;

  -- Insert the message
  INSERT INTO messages (chat_id, sender_id, content, image_url, reply_to_id)
  VALUES (p_chat_id, p_sender_id, COALESCE(p_content, ''), p_image_url, p_reply_to_id)
  RETURNING id INTO v_message_id;

  -- Update chat's last message
  UPDATE chats 
  SET 
    last_message = CASE 
      WHEN p_image_url IS NOT NULL AND (p_content IS NULL OR trim(p_content) = '') THEN 'שלח תמונה'
      ELSE COALESCE(p_content, 'שלח תמונה')
    END,
    last_message_at = now(),
    updated_at = now()
  WHERE id = p_chat_id;

  -- Get the other user in the chat for notification
  SELECT user_id INTO v_other_user_id
  FROM chat_participants 
  WHERE chat_id = p_chat_id AND user_id != p_sender_id
  LIMIT 1;

  -- Mark message as unread for the other user
  IF v_other_user_id IS NOT NULL THEN
    UPDATE messages 
    SET read = false 
    WHERE id = v_message_id;
  END IF;

  RETURN v_message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION send_message_with_image TO authenticated;

-- Update existing messages to have empty string instead of null for content
UPDATE messages SET content = '' WHERE content IS NULL;

-- Add constraint to ensure messages have either content or image
ALTER TABLE messages ADD CONSTRAINT messages_content_or_image_check 
CHECK (
  (content IS NOT NULL AND trim(content) != '') 
  OR 
  (image_url IS NOT NULL AND trim(image_url) != '')
);
