import React from 'react';
import { motion } from 'framer-motion';

interface SkeletonCardProps {
  variant?: 'mini' | 'compact';
}

function SkeletonCard({ variant = 'mini' }: SkeletonCardProps) {
  if (variant === 'mini') {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
      >
        {/* Image Skeleton */}
        <div className="relative w-full h-32 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse">
          {/* VIP Badge Skeleton */}
          <div className="absolute top-2 left-2">
            <div className="bg-gray-300 rounded-full w-12 h-5 animate-pulse"></div>
          </div>
          {/* Online Status Skeleton */}
          <div className="absolute top-2 right-2">
            <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
          </div>
          {/* Bottom Info Skeleton */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="bg-gray-300 rounded w-16 h-3 animate-pulse"></div>
                <div className="bg-gray-300 rounded w-8 h-2 animate-pulse"></div>
              </div>
              <div className="bg-gray-300 rounded-full w-6 h-6 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Bottom Content Skeleton */}
        <div className="p-3 space-y-2">
          <div className="flex items-center gap-1">
            <div className="bg-gray-300 rounded w-3 h-3 animate-pulse"></div>
            <div className="bg-gray-300 rounded w-16 h-3 animate-pulse"></div>
          </div>
          <div className="bg-amber-100 rounded-full w-20 h-5 animate-pulse"></div>
          <div className="flex items-center justify-between">
            <div className="bg-gray-300 rounded w-12 h-3 animate-pulse"></div>
            <div className="bg-gray-300 rounded w-16 h-3 animate-pulse"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Compact variant
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
    >
      <div className="flex">
        {/* Image Skeleton */}
        <div className="relative w-24 h-24 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse">
          <div className="absolute top-1 left-1">
            <div className="bg-gray-300 rounded-full w-8 h-4 animate-pulse"></div>
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="flex-1 p-4 space-y-2">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="bg-gray-300 rounded w-20 h-4 animate-pulse"></div>
              <div className="bg-gray-300 rounded w-16 h-3 animate-pulse"></div>
            </div>
            <div className="bg-gray-300 rounded-full w-8 h-8 animate-pulse"></div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="bg-gray-300 rounded w-3 h-3 animate-pulse"></div>
            <div className="bg-gray-300 rounded w-24 h-3 animate-pulse"></div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="bg-amber-100 rounded-full w-16 h-5 animate-pulse"></div>
            <div className="bg-green-100 rounded-full w-12 h-5 animate-pulse"></div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default SkeletonCard;
