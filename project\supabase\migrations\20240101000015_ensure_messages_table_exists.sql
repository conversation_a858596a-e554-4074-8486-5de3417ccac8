-- Ensure messages table exists
-- This migration ensures that the messages table exists before other migrations try to modify it

-- Create chats table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'chats') THEN
    CREATE TABLE chats (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now(),
      last_message text,
      last_message_at timestamptz DEFAULT now()
    );

    ALTER TABLE chats ENABLE ROW LEVEL SECURITY;

    -- Add basic policy for chat creation
    CREATE POLICY "Users can create chats"
      ON chats
      FOR INSERT
      TO authenticated
      WITH CHECK (true);

    RAISE NOTICE 'Created chats table';
  END IF;
END
$$;

-- Create chat_participants table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'chat_participants') THEN
    CREATE TABLE chat_participants (
      chat_id uuid REFERENCES chats(id) ON DELETE CASCADE,
      user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
      created_at timestamptz DEFAULT now(),
      PRIMARY KEY (chat_id, user_id)
    );

    ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;

    -- Add basic policies
    CREATE POLICY "Users can view chat participants"
      ON chat_participants
      FOR SELECT
      TO authenticated
      USING (
        user_id = auth.uid() OR
        chat_id IN (
          SELECT chat_id
          FROM chat_participants
          WHERE user_id = auth.uid()
        )
      );

    CREATE POLICY "Allow chat participant creation"
      ON chat_participants
      FOR INSERT
      TO authenticated
      WITH CHECK (auth.uid() IS NOT NULL);

    RAISE NOTICE 'Created chat_participants table';
  END IF;
END
$$;

-- Add remaining policies for chats table now that chat_participants exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'chats') AND
     EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'chat_participants') THEN

    -- Check if policy exists before creating it
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chats' AND policyname = 'Users can view their chats') THEN
      CREATE POLICY "Users can view their chats"
        ON chats
        FOR SELECT
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM chat_participants
            WHERE chat_participants.chat_id = id
            AND chat_participants.user_id = auth.uid()
          )
        );
    END IF;

    -- Check if policy exists before creating it
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chats' AND policyname = 'Chat participants can update chats') THEN
      CREATE POLICY "Chat participants can update chats"
        ON chats
        FOR UPDATE
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM chat_participants
            WHERE chat_participants.chat_id = id
            AND chat_participants.user_id = auth.uid()
          )
        );
    END IF;

    -- Check if policy exists before creating it
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chats' AND policyname = 'Users can delete their chats') THEN
      CREATE POLICY "Users can delete their chats"
        ON chats
        FOR DELETE
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM chat_participants
            WHERE chat_participants.chat_id = id
            AND chat_participants.user_id = auth.uid()
          )
        );
    END IF;

    RAISE NOTICE 'Added policies to chats table';
  END IF;
END
$$;

-- Create messages table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'messages') THEN
    CREATE TABLE messages (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      chat_id uuid REFERENCES chats(id) ON DELETE CASCADE,
      sender_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
      content text NOT NULL,
      created_at timestamptz DEFAULT now(),
      read boolean DEFAULT false
    );

    ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

    -- Add basic policies
    CREATE POLICY "Users can view messages in their chats"
      ON messages
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1
          FROM chat_participants
          WHERE chat_participants.chat_id = messages.chat_id
          AND chat_participants.user_id = auth.uid()
        )
      );

    CREATE POLICY "Users can send messages to their chats"
      ON messages
      FOR INSERT
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1
          FROM chat_participants
          WHERE chat_participants.chat_id = messages.chat_id
          AND chat_participants.user_id = auth.uid()
        )
        AND sender_id = auth.uid()
      );

    -- Add VIP policy
    CREATE POLICY "VIP users can send messages"
      ON messages
      FOR INSERT
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1
          FROM chat_participants
          WHERE chat_participants.chat_id = messages.chat_id
          AND chat_participants.user_id = auth.uid()
        )
        AND sender_id = auth.uid()
        AND EXISTS (
          SELECT 1
          FROM profiles
          WHERE id = auth.uid()
          AND is_vip = TRUE
        )
      );

    RAISE NOTICE 'Created messages table';
  END IF;
END
$$;

-- Add read field to messages table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'read'
  ) THEN
    ALTER TABLE messages ADD COLUMN read BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'Added read column to messages table';
  END IF;
END
$$;

-- Drop trigger if exists
DROP TRIGGER IF EXISTS update_chat_last_message_trigger ON messages;

-- Create function to update chat's last message
CREATE OR REPLACE FUNCTION update_chat_last_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE chats
  SET last_message = NEW.content,
      last_message_at = NEW.created_at,
      updated_at = now()
  WHERE id = NEW.chat_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER update_chat_last_message_trigger
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION update_chat_last_message();
