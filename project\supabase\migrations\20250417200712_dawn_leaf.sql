/*
  # Fix chat_participants policy recursion

  1. Changes
    - Drop the recursive policy on chat_participants table
    - Create a new non-recursive policy that allows users to view their own chat participants

  2. Security
    - Maintains RLS security by ensuring users can only see chat participants for chats they are part of
    - Uses a direct comparison instead of a recursive subquery
*/

-- Drop the existing policy that's causing recursion
DROP POLICY IF EXISTS "Users can view chat participants" ON chat_participants;

-- Create new non-recursive policy
CREATE POLICY "Users can view chat participants"
ON chat_participants
FOR SELECT
TO public
USING (
  user_id = auth.uid() OR
  chat_id IN (
    SELECT chat_id 
    FROM chat_participants 
    WHERE user_id = auth.uid()
  )
);