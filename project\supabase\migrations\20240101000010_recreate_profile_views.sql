-- Recreate profile views functionality from scratch
-- This migration drops and recreates the profile_views table, policies, and functions

-- 1. Drop existing triggers first
DROP TRIGGER IF EXISTS on_profile_view_change ON profile_views;

-- 2. Then drop existing functions
DROP FUNCTION IF EXISTS get_profiles_who_viewed_me(uuid);
DROP FUNCTION IF EXISTS get_profiles_i_viewed(uuid);
DROP FUNCTION IF EXISTS count_profiles_who_viewed_me(uuid);
DROP FUNCTION IF EXISTS count_profiles_i_viewed(uuid);
DROP FUNCTION IF EXISTS record_profile_view(uuid, uuid);
DROP FUNCTION IF EXISTS insert_profile_view_bypass_rls(uuid, uuid);
DROP FUNCTION IF EXISTS refresh_profile_views();

-- 2. Drop existing table (this will also drop all policies)
DROP TABLE IF EXISTS profile_views;

-- 3. Create the table
CREATE TABLE profile_views (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  viewer_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  viewed_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE (viewer_id, viewed_id)
);

-- 4. Enable RLS
ALTER TABLE profile_views ENABLE ROW LEVEL SECURITY;

-- 5. Create policies
-- Policy for SELECT: Users can view their own profile views (as viewer or viewed)
CREATE POLICY "Users can view their own profile views"
  ON profile_views
  FOR SELECT
  TO authenticated
  USING (auth.uid() = viewer_id OR auth.uid() = viewed_id);

-- Policy for INSERT: Users can insert profile views where they are the viewer
CREATE POLICY "Users can insert profile views"
  ON profile_views
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = viewer_id);

-- Policy for UPDATE: Users can update their own profile views
CREATE POLICY "Users can update their own profile views"
  ON profile_views
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = viewer_id)
  WITH CHECK (auth.uid() = viewer_id);

-- Policy for DELETE: Users can delete their own profile views
CREATE POLICY "Users can delete their own profile views"
  ON profile_views
  FOR DELETE
  TO authenticated
  USING (auth.uid() = viewer_id);

-- Policy for admins: Admins can do anything with profile views
CREATE POLICY "Admins can manage all profile views"
  ON profile_views
  FOR ALL
  TO authenticated
  USING (auth.email() = '<EMAIL>')
  WITH CHECK (auth.email() = '<EMAIL>');

-- 6. Create function to record profile view (with SECURITY DEFINER to bypass RLS)
CREATE OR REPLACE FUNCTION record_profile_view(viewer uuid, viewed uuid)
RETURNS uuid AS $$
DECLARE
  view_id uuid;
BEGIN
  -- Don't record if viewing own profile
  IF viewer = viewed THEN
    RETURN NULL;
  END IF;

  -- Insert or update the profile view
  INSERT INTO profile_views (viewer_id, viewed_id, created_at)
  VALUES (viewer, viewed, now())
  ON CONFLICT (viewer_id, viewed_id)
  DO UPDATE SET created_at = now()
  RETURNING id INTO view_id;

  -- Log the view for debugging
  RAISE NOTICE 'Profile view recorded: viewer % viewed % (id: %)', viewer, viewed, view_id;

  RETURN view_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to get profiles that viewed a user
CREATE OR REPLACE FUNCTION get_profiles_who_viewed_me(user_id uuid)
RETURNS TABLE (
  id uuid,
  username text,
  gender text,
  birth_date date,
  partner_birth_date date,
  city text,
  area text,
  phone text,
  height numeric,
  weight numeric,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children text,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text,
  meeting_times text,
  created_at timestamptz,
  updated_at timestamptz,
  user_metadata jsonb,
  profile_data jsonb,
  points integer,
  is_online boolean,
  view_date timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.*,
    pv.created_at as view_date
  FROM profiles p
  JOIN profile_views pv ON p.id = pv.viewer_id
  WHERE pv.viewed_id = user_id
  ORDER BY pv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to get profiles that a user viewed
CREATE OR REPLACE FUNCTION get_profiles_i_viewed(user_id uuid)
RETURNS TABLE (
  id uuid,
  username text,
  gender text,
  birth_date date,
  partner_birth_date date,
  city text,
  area text,
  phone text,
  height numeric,
  weight numeric,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children text,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text,
  meeting_times text,
  created_at timestamptz,
  updated_at timestamptz,
  user_metadata jsonb,
  profile_data jsonb,
  points integer,
  is_online boolean,
  view_date timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.*,
    pv.created_at as view_date
  FROM profiles p
  JOIN profile_views pv ON p.id = pv.viewed_id
  WHERE pv.viewer_id = user_id
  ORDER BY pv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create function to count profiles that viewed a user
CREATE OR REPLACE FUNCTION count_profiles_who_viewed_me(user_id uuid)
RETURNS integer AS $$
DECLARE
  view_count integer;
BEGIN
  SELECT COUNT(*)
  INTO view_count
  FROM profile_views
  WHERE viewed_id = user_id;

  RETURN view_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Create function to count profiles that a user viewed
CREATE OR REPLACE FUNCTION count_profiles_i_viewed(user_id uuid)
RETURNS integer AS $$
DECLARE
  view_count integer;
BEGIN
  SELECT COUNT(*)
  INTO view_count
  FROM profile_views
  WHERE viewer_id = user_id;

  RETURN view_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Create function for realtime notifications
CREATE OR REPLACE FUNCTION refresh_profile_views()
RETURNS TRIGGER AS $$
BEGIN
  -- Notify clients about the change
  PERFORM pg_notify(
    'profile_views_changes',
    json_build_object(
      'table', 'profile_views',
      'type', TG_OP,
      'id', NEW.id,
      'viewer_id', NEW.viewer_id,
      'viewed_id', NEW.viewed_id,
      'created_at', NEW.created_at,
      'record', row_to_json(NEW)
    )::text
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Create trigger for realtime notifications
CREATE TRIGGER on_profile_view_change
AFTER INSERT OR UPDATE ON profile_views
FOR EACH ROW
EXECUTE FUNCTION refresh_profile_views();
