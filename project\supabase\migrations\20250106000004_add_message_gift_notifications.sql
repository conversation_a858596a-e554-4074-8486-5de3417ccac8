/*
  # Add Automatic Notifications for Messages and Gifts
  
  1. Purpose
    - <PERSON>reate triggers to automatically send notifications when:
      - Someone receives a new message
      - Someone receives a gift
    
  2. Implementation
    - Trigger functions for messages and gifts
    - Automatic notification creation
    
  3. Security
    - Only notify the recipient, not the sender
*/

-- Function to create notification for new messages
CREATE OR REPLACE FUNCTION notify_new_message()
RETURNS TRIGGER AS $$
DECLARE
    sender_username text;
    recipient_id uuid;
BEGIN
    -- Get sender's username
    SELECT username INTO sender_username
    FROM profiles
    WHERE id = NEW.sender_id;
    
    -- Get recipient ID (the other participant in the chat)
    SELECT user_id INTO recipient_id
    FROM chat_participants
    WHERE chat_id = NEW.chat_id
    AND user_id != NEW.sender_id
    LIMIT 1;
    
    -- Only create notification if we found a recipient
    IF recipient_id IS NOT NULL THEN
        -- Create notification for the recipient
        PERFORM create_notification(
            recipient_id,
            'message',
            'הודעה חדשה',
            'קיבלת הודעה חדשה מ-' || COALESCE(sender_username, 'משתמש'),
            jsonb_build_object(
                'message_id', NEW.id,
                'chat_id', NEW.chat_id,
                'sender_id', NEW.sender_id,
                'sender_username', sender_username
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create notification for new gifts
CREATE OR REPLACE FUNCTION notify_new_gift()
RETURNS TRIGGER AS $$
DECLARE
    sender_username text;
    gift_name text;
BEGIN
    -- Get sender's username
    SELECT username INTO sender_username
    FROM profiles
    WHERE id = NEW.sender_id;
    
    -- Get gift name
    SELECT name INTO gift_name
    FROM gifts
    WHERE id = NEW.gift_id;
    
    -- Create notification for the recipient
    PERFORM create_notification(
        NEW.recipient_id,
        'gift',
        'מתנה חדשה!',
        'קיבלת ' || COALESCE(gift_name, 'מתנה') || ' מ-' || COALESCE(sender_username, 'משתמש'),
        jsonb_build_object(
            'gift_id', NEW.gift_id,
            'sender_id', NEW.sender_id,
            'sender_username', sender_username,
            'gift_name', gift_name
        )
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for messages (only if messages table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'messages') THEN
        -- Drop trigger if exists
        DROP TRIGGER IF EXISTS trigger_notify_new_message ON messages;
        
        -- Create trigger for new messages
        CREATE TRIGGER trigger_notify_new_message
            AFTER INSERT ON messages
            FOR EACH ROW
            EXECUTE FUNCTION notify_new_message();
            
        RAISE NOTICE 'Created message notification trigger';
    END IF;
END $$;

-- Create triggers for gifts (only if user_gifts table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_gifts') THEN
        -- Drop trigger if exists
        DROP TRIGGER IF EXISTS trigger_notify_new_gift ON user_gifts;
        
        -- Create trigger for new gifts
        CREATE TRIGGER trigger_notify_new_gift
            AFTER INSERT ON user_gifts
            FOR EACH ROW
            EXECUTE FUNCTION notify_new_gift();
            
        RAISE NOTICE 'Created gift notification trigger';
    END IF;
END $$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION notify_new_message() TO authenticated;
GRANT EXECUTE ON FUNCTION notify_new_gift() TO authenticated;
