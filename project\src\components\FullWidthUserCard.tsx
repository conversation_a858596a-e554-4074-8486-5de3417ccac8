import React, { useState } from 'react';
import ReactDOM from 'react-dom';
import { motion } from 'framer-motion';
import { MapPin, Gift, Crown, Users, MessageSquare, Calendar, Clock, Heart, Star, Shield, Eye } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';
import { useProfileImage } from '../hooks/useProfileImage';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import SendGiftDialog from './SendGiftDialog';

interface FullWidthUserCardProps {
  profile: Profile;
}

function FullWidthUserCard({ profile }: FullWidthUserCardProps) {
  const { navigateToProfile } = useNavigate();
  const [isGiftDialogOpen, setIsGiftDialogOpen] = useState(false);

  // Get the current profile image (will update automatically when changed)
  const currentProfileImage = useProfileImage(profile.id, profile);

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  const getGenderIcon = () => {
    switch (profile.gender) {
      case 'male':
        return <FontAwesomeIcon icon={faMars} className="text-xl text-blue-600" />;
      case 'female':
        return <FontAwesomeIcon icon={faVenus} className="text-xl text-pink-600" />;
      case 'couple':
        return <Users className="h-5 w-5 text-purple-600" />;
      default:
        return null;
    }
  };

  const getGenderText = () => {
    switch (profile.gender) {
      case 'male':
        return 'גבר';
      case 'female':
        return 'אישה';
      case 'couple':
        return 'זוג';
      default:
        return '';
    }
  };

  const { navigateToChat } = useNavigate();

  const handleChat = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    // Navigate to chat with this user
    navigateToChat(profile.id);
  };

  const handleGift = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsGiftDialogOpen(true);
  };

  const getSexualPreferenceTranslation = (preference: string) => {
    const translations: Record<string, string> = {
      straight: 'סטרייט',
      bisexual: 'דו מיני/ת',
      gay: 'הומו',
      lesbian: 'לסבית'
    };
    return translations[preference] || preference;
  };

  // Determine if the profile is from "Viewed Me" or "I Viewed" page
  const isViewedMe = window.location.href.includes('viewed-me');

  // Get appropriate colors based on the page
  const cardBorderColor = isViewedMe ? 'border-pink-200' : 'border-blue-200';
  const primaryColor = isViewedMe ? 'from-pink-500 to-pink-600' : 'from-blue-500 to-blue-600';
  const secondaryColor = isViewedMe ? 'from-purple-500 to-purple-600' : 'from-indigo-500 to-indigo-600';
  const badgeColor = isViewedMe ? 'bg-pink-100 text-pink-700' : 'bg-blue-100 text-blue-700';
  const iconColor = isViewedMe ? 'text-pink-500' : 'text-blue-500';
  const viewIcon = isViewedMe ? <Eye className="w-3.5 h-3.5 text-pink-500" /> : <Eye className="w-3.5 h-3.5 text-blue-500" />;

  // Format last activity time
  const getLastActive = () => {
    // This would normally come from the profile data
    // For now, we'll just return a placeholder
    return 'לפני 2 שעות';
  };

  // Function to handle profile navigation with complete data
  const handleProfileClick = async () => {
    try {
      console.log('Fetching complete profile data for:', profile.id);

      // Try to get complete profile data using RPC
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('get_complete_profile', { profile_id: profile.id });

        if (!rpcError && rpcData) {
          console.log('Complete profile data fetched with RPC:', rpcData);

          // Make sure we have user_metadata
          if (!rpcData.user_metadata) {
            rpcData.user_metadata = {};
          }

          // Make sure we have profile_data in user_metadata
          if (!rpcData.user_metadata.profile_data) {
            rpcData.user_metadata.profile_data = {};
          }

          // If we have profile_data directly on the profile, copy it to user_metadata
          if (rpcData.profile_data && Object.keys(rpcData.profile_data).length > 0) {
            console.log('Copying profile_data to user_metadata.profile_data');
            rpcData.user_metadata.profile_data = {
              ...rpcData.user_metadata.profile_data,
              ...rpcData.profile_data
            };
          }

          // Navigate with the complete profile data
          navigateToProfile(rpcData);
          return;
        }
      } catch (rpcErr) {
        console.error('Error fetching with RPC:', rpcErr);
      }

      // Fallback to regular query
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profile.id)
        .single();

      if (error) {
        console.error('Error fetching complete profile data:', error);
        // Fall back to using the profile we have
        navigateToProfile(profile);
        return;
      }

      if (data) {
        console.log('Complete profile data fetched:', data);

        // Make sure we have user_metadata
        if (!data.user_metadata) {
          data.user_metadata = {};
        }

        // Make sure we have profile_data in user_metadata
        if (!data.user_metadata.profile_data) {
          data.user_metadata.profile_data = {};
        }

        // If we have profile_data directly on the profile, copy it to user_metadata
        if (data.profile_data && Object.keys(data.profile_data).length > 0) {
          console.log('Copying profile_data to user_metadata.profile_data');
          data.user_metadata.profile_data = {
            ...data.user_metadata.profile_data,
            ...data.profile_data
          };
        }

        // Navigate with the complete profile data
        navigateToProfile(data);
      } else {
        // Fall back to using the profile we have
        navigateToProfile(profile);
      }
    } catch (err) {
      console.error('Error in handleProfileClick:', err);
      // Fall back to using the profile we have
      navigateToProfile(profile);
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.01 }}
      onClick={handleProfileClick}
      className={`bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-all cursor-pointer w-full max-w-4xl mx-auto mb-4 border ${cardBorderColor}`}
    >
      <div className="flex flex-row">
        {/* Left side - Profile Image with gradient overlay */}
        <div className="relative w-28 h-28 md:w-36 md:h-36 overflow-hidden flex-shrink-0">
          {currentProfileImage || profile.user_metadata?.profile_image_url ? (
            <img
              src={currentProfileImage || profile.user_metadata?.profile_image_url}
              alt={profile.username}
              className="w-full h-full object-cover"
            />
          ) : profile.photos && profile.photos.length > 0 ? (
            <img
              src={profile.photos[0]}
              alt={profile.username}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <Users className="w-8 h-8 text-gray-400" />
            </div>
          )}

          {/* Image overlay with gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-60"></div>

          {/* View indicator */}
          <div className="absolute bottom-2 left-2 bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-sm">
            {viewIcon}
          </div>

          {/* VIP Badge */}
          {profile.is_vip && (
            <div className="absolute top-2 right-2 bg-gradient-to-r from-yellow-500 to-amber-500 text-white px-2 py-0.5 rounded-full flex items-center gap-1 shadow-sm text-xs">
              <Crown className="w-3 h-3" />
              <span className="font-medium">VIP</span>
            </div>
          )}
        </div>

        {/* Right side - Profile Info */}
        <div className="p-4 md:p-5 flex-1 flex flex-col justify-between">
          <div>
            {/* Header with name and badges */}
            <div className="flex flex-wrap items-center justify-between gap-2 mb-3">
              <div className="flex items-center gap-2">
                <h3 className="text-lg md:text-xl font-bold text-gray-800">
                  {profile.username}
                </h3>

                {/* Online status */}
                {profile.is_online && (
                  <div className="flex items-center gap-1 text-green-600 text-sm">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    <span>מחובר</span>
                  </div>
                )}
              </div>

              {/* Free Today Badge */}
              {profile.free_today && (
                <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-white px-2 py-1 rounded-full flex items-center gap-1 shadow-sm text-xs">
                  <Calendar className="w-3 h-3" />
                  <span className="font-medium">פנוי היום</span>
                </div>
              )}
            </div>

            {/* User details */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-3">
              {/* Location */}
              <div className="flex items-center gap-1.5">
                <div className={`p-1.5 rounded-full ${badgeColor}`}>
                  <MapPin className="w-3.5 h-3.5" />
                </div>
                <span className="text-sm text-gray-700">{profile.city}</span>
              </div>

              {/* Gender and Age */}
              <div className="flex items-center gap-1.5">
                <div className={`p-1.5 rounded-full ${badgeColor}`}>
                  {getGenderIcon()}
                </div>
                <span className="text-sm text-gray-700">
                  {getGenderText()}, {profile.gender === 'couple' ? (
                    <>
                      {calculateAge(profile.birth_date)}/{calculateAge(profile.partner_birth_date || '')}
                    </>
                  ) : (
                    calculateAge(profile.birth_date)
                  )}
                </span>
              </div>

              {/* Last active */}
              <div className="flex items-center gap-1.5">
                <div className={`p-1.5 rounded-full ${badgeColor}`}>
                  <Clock className="w-3.5 h-3.5" />
                </div>
                <span className="text-sm text-gray-700">{getLastActive()}</span>
              </div>
            </div>

            {/* Tags and preferences */}
            <div className="flex flex-wrap gap-2 mb-4">
              <div className={`px-2.5 py-1 rounded-full text-xs font-medium ${badgeColor} border border-opacity-20`}>
                {getSexualPreferenceTranslation(profile.sexual_preference)}
              </div>

              {/* Additional tags could be added here */}
              {profile.profile_data?.interests && profile.profile_data.interests.slice(0, 2).map((interest: string, index: number) => (
                <div key={index} className={`px-2.5 py-1 rounded-full text-xs font-medium ${badgeColor} border border-opacity-20`}>
                  {interest}
                </div>
              ))}
            </div>

            {/* Short bio or description */}
            {profile.profile_data?.aboutUs && (
              <div className="mb-3">
                <p className="text-sm text-gray-600 line-clamp-2">{profile.profile_data.aboutUs}</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={handleChat}
              className={`flex-1 max-w-xs flex items-center justify-center gap-1.5 bg-gradient-to-r ${primaryColor} text-white py-2 rounded-lg hover:opacity-90 transition-all text-sm shadow-sm`}
            >
              <MessageSquare className="w-4 h-4" />
              <span>צ'אט</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={handleGift}
              className={`w-12 flex items-center justify-center p-2 bg-gradient-to-r ${secondaryColor} text-white rounded-lg hover:opacity-90 transition-all shadow-sm`}
            >
              <Gift className="w-4 h-4" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="w-12 flex items-center justify-center p-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all"
            >
              <Heart className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Gift Dialog - Render outside the card to prevent event bubbling issues */}
      {isGiftDialogOpen && ReactDOM.createPortal(
        <SendGiftDialog
          isOpen={isGiftDialogOpen}
          onClose={() => setIsGiftDialogOpen(false)}
          receiverId={profile.id}
          receiverName={profile.username}
        />,
        document.body
      )}
    </motion.div>
  );
}

export default FullWidthUserCard;
