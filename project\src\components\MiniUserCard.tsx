import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Gift, Crown, Users, Calendar, Heart, MessageCircle } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';
import { useSimpleProfileImage } from '../hooks/useSimpleProfileImage';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import SendGiftDialog from './SendGiftDialog';

interface MiniUserCardProps {
  profile: Profile;
}

function MiniUserCard({ profile }: MiniUserCardProps) {
  const { navigateToProfile, navigateToChat } = useNavigate();
  const { profileImage: currentProfileImage } = useSimpleProfileImage(profile.id);
  const [showGiftDialog, setShowGiftDialog] = useState(false);
  const [isNavigatingToChat, setIsNavigatingToChat] = useState(false);

  // Fallback to profile photos if no profile image is set
  const displayImage = currentProfileImage ||
    (profile.user_metadata?.profile_image_url) ||
    (profile.photos && profile.photos.length > 0 ? profile.photos[0] : null);



  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const handleCardClick = async () => {
    try {
      await navigateToProfile(profile);
    } catch (error) {
      console.error('Error navigating to profile:', error);
    }
  };

  const handleGiftClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('🎁 Gift button clicked for:', profile.username);
    setShowGiftDialog(true);
  };

  const handleMessageClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('💬 Message button clicked for:', profile.username);

    setIsNavigatingToChat(true);

    try {
      // Navigate to chat with this user
      await navigateToChat(profile.id);
      console.log('✅ Successfully navigated to chat with:', profile.username);
    } catch (error) {
      console.error('❌ Error navigating to chat:', error);
      alert('שגיאה בפתיחת הצ\'אט. נסה שוב.');
      setIsNavigatingToChat(false);
    }
  };

  return (
    <>
      <div
        onClick={handleCardClick}
        className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden cursor-pointer hover:shadow-md hover:scale-[1.02] transition-all duration-200"
      >
        {/* Header with Image and Basic Info */}
        <div className="relative">
          {/* Profile Image */}
          <div className="relative w-full h-32 bg-gradient-to-br from-gray-100 to-gray-200">
            {displayImage ? (
              <img
                src={displayImage}
                alt={profile.username}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Users className="w-8 h-8 text-gray-400" />
              </div>
            )}
            
            {/* VIP Badge */}
            {profile.is_vip && (
              <div className="absolute top-2 left-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                  <Crown className="w-3 h-3" />
                  VIP
                </div>
              </div>
            )}

            {/* Online Status */}
            {profile.is_online && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
            )}
          </div>

          {/* Quick Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
            <div className="text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-bold text-sm truncate">{profile.username}</h3>
                  <div className="flex items-center gap-2 text-xs opacity-90">
                    {profile.birth_date && (
                      <span>{calculateAge(profile.birth_date)}</span>
                    )}
                    {profile.gender && (
                      <FontAwesomeIcon 
                        icon={profile.gender === 'male' ? faMars : faVenus} 
                        className="w-3 h-3"
                      />
                    )}
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="flex gap-1">
                  <button
                    onClick={handleMessageClick}
                    disabled={isNavigatingToChat}
                    className={`bg-blue-500 hover:bg-blue-600 hover:scale-110 text-white p-1.5 rounded-full transition-all disabled:opacity-50 disabled:cursor-not-allowed ${
                      isNavigatingToChat ? 'animate-pulse' : ''
                    }`}
                    title={isNavigatingToChat ? 'פותח צ\'אט...' : 'שלח הודעה'}
                  >
                    <MessageCircle className="w-3 h-3" />
                  </button>
                  <button
                    onClick={handleGiftClick}
                    className="bg-pink-500 hover:bg-pink-600 hover:scale-110 text-white p-1.5 rounded-full transition-all"
                    title="שלח מתנה"
                  >
                    <Gift className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Info */}
        <div className="p-3 space-y-2">
          {/* Location */}
          {profile.city && (
            <div className="flex items-center gap-1 text-gray-600 text-xs">
              <MapPin className="w-3 h-3" />
              <span className="truncate">{profile.city}</span>
            </div>
          )}

          {/* What we're looking for */}
          {(() => {
            // Try multiple sources for looking_for data
            const lookingFor = profile.looking_for ||
                              profile.user_metadata?.profile_data?.lookingFor ||
                              profile.profile_data?.lookingFor;

            console.log('🔍 MiniUserCard looking_for debug:', {
              username: profile.username,
              direct: profile.looking_for,
              metadata: profile.user_metadata?.profile_data?.lookingFor,
              profile_data: profile.profile_data?.lookingFor,
              final: lookingFor
            });

            if (!lookingFor) return null;

            const displayText = lookingFor.length > 80 ? `${lookingFor.substring(0, 80)}...` : lookingFor;

            return (
              <div className="text-xs text-gray-700 bg-blue-50 px-2 py-1 rounded border border-blue-200">
                <div className="font-medium text-blue-800 mb-1 flex items-center gap-1">
                  🔍 מה אנחנו מחפשים:
                </div>
                <div className="text-blue-700 leading-relaxed">
                  {displayText}
                </div>
              </div>
            );
          })()}

          {/* Free Today Badge */}
          {profile.free_today && (
            <div className="flex items-center gap-1">
              <div className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                פנוי היום
              </div>
            </div>
          )}

          {/* Quick Stats */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-3">
              {profile.photos && profile.photos.length > 0 && (
                <span>{profile.photos.length} תמונות</span>
              )}
              {profile.is_online && (
                <span className="text-green-600 font-medium">מחובר</span>
              )}
            </div>

            {profile.last_seen && !profile.is_online && (
              <span className="text-gray-400">
                {new Date(profile.last_seen).toLocaleDateString('he-IL')}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Gift Dialog */}
      {showGiftDialog && (
        <SendGiftDialog
          isOpen={showGiftDialog}
          onClose={() => setShowGiftDialog(false)}
          receiverId={profile.id}
          receiverName={profile.username}
        />
      )}
    </>
  );
}

export default MiniUserCard;
