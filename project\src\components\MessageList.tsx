import React from 'react';
import { MessageSquare, Circle } from 'lucide-react';

interface Message {
  id: string;
  sender: {
    name: string;
    avatar: string;
    isOnline: boolean;
  };
  lastMessage: string;
  timestamp: string;
  unread: boolean;
}

function MessageList() {
  // Placeholder data - in a real app, this would come from your Supabase database
  const messages: Message[] = [
    {
      id: '1',
      sender: {
        name: 'דניאל כהן',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=100&q=80',
        isOnline: true,
      },
      lastMessage: 'היי, מה שלומך?',
      timestamp: 'לפני 5 דקות',
      unread: true,
    },
    {
      id: '2',
      sender: {
        name: 'רותם לוי',
        avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=100&q=80',
        isOnline: false,
      },
      lastMessage: 'תודה על ההודעה',
      timestamp: 'אתמול',
      unread: false,
    },
    {
      id: '3',
      sender: {
        name: 'מיכל ודני',
        avatar: 'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=100&q=80',
        isOnline: true,
      },
      lastMessage: 'נשמח להכיר!',
      timestamp: 'לפני שעתיים',
      unread: true,
    },
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm">
      <div className="p-4 border-b border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-pink-500" />
          הודעות
        </h2>
      </div>
      
      <div className="divide-y divide-gray-100">
        {messages.map((message) => (
          <div
            key={message.id}
            className="p-4 hover:bg-gray-50 transition-colors cursor-pointer flex items-center gap-4"
          >
            <div className="relative">
              <img
                src={message.sender.avatar}
                alt={message.sender.name}
                className="w-12 h-12 rounded-full object-cover"
              />
              {message.sender.isOnline && (
                <Circle className="w-3 h-3 text-green-500 fill-current absolute bottom-0 right-0" />
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900 truncate">
                  {message.sender.name}
                </h3>
                <span className="text-sm text-gray-500">
                  {message.timestamp}
                </span>
              </div>
              
              <p className={`text-sm truncate ${
                message.unread ? 'text-gray-900 font-medium' : 'text-gray-500'
              }`}>
                {message.lastMessage}
              </p>
            </div>
            
            {message.unread && (
              <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default MessageList;