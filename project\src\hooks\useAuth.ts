import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { User } from '@supabase/supabase-js';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Function to activate daily points for user
  const activateDailyPoints = async (userId: string) => {
    try {
      // Check if user already received daily points today
      const today = new Date().toISOString().split('T')[0];

      const { data: existingTransaction, error: checkError } = await supabase
        .from('points_transactions')
        .select('id')
        .eq('user_id', userId)
        .eq('description', 'נקודות יומיות')
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lt('created_at', `${today}T23:59:59.999Z`)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected if no daily points yet
        console.error('Error checking daily points:', checkError);
        return;
      }

      // If user already received daily points today, skip
      if (existingTransaction) {
        console.log('User already received daily points today');
        return;
      }

      // Give user daily points (e.g., 10 points per day)
      const dailyPoints = 10;

      const { error: transactionError } = await supabase
        .from('points_transactions')
        .insert([{
          user_id: userId,
          amount: dailyPoints,
          description: 'נקודות יומיות'
        }]);

      if (transactionError) {
        console.error('Error adding daily points:', transactionError);
        return;
      }

      console.log(`Added ${dailyPoints} daily points for user ${userId}`);
    } catch (error) {
      console.error('Error in activateDailyPoints:', error);
    }
  };

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);

      // Activate daily points for logged in user
      if (session?.user?.id) {
        activateDailyPoints(session.user.id);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
      setLoading(false);

      // Activate daily points for newly logged in user
      if (session?.user?.id) {
        activateDailyPoints(session.user.id);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (email: string, password: string, metadata: any) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) throw error;

      // Return success with user data
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Return success with user data
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error) {
      return { error };
    }
  };

  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      // First verify the current password by trying to sign in
      if (!user?.email) {
        throw new Error('User email not found');
      }

      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: currentPassword,
      });

      if (signInError) {
        throw new Error('Current password is incorrect');
      }

      // Then update the password
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;

      return { error: null };
    } catch (error: any) {
      return { error: error.message || 'Failed to update password' };
    }
  };

  return {
    user,
    loading,
    signUp,
    signIn,
    signOut,
    updatePassword,
  };
}