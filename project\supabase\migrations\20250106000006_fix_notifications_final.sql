/*
  # Fix Notifications System - Final Fix
  
  1. Problems Found:
    - user_gifts table uses receiver_id, not recipient_id
    - Triggers might not be created properly
    - Functions need to match actual table structure
    
  2. Solutions:
    - Fix the notify_new_gift function to use receiver_id
    - Ensure triggers are created properly
    - Add debugging to see what's happening
*/

-- First, let's fix the notify_new_gift function to use the correct column name
CREATE OR REPLACE FUNCTION notify_new_gift()
RETURNS TRIGGER AS $$
DECLARE
    sender_username text;
    gift_name text;
BEGIN
    -- Get sender's username
    SELECT username INTO sender_username
    FROM profiles
    WHERE id = NEW.sender_id;
    
    -- Get gift name
    SELECT name INTO gift_name
    FROM gifts
    WHERE id = NEW.gift_id;
    
    -- Create notification for the recipient (using receiver_id, not recipient_id)
    PERFORM create_notification(
        NEW.receiver_id,  -- Changed from recipient_id to receiver_id
        'gift',
        'מתנה חדשה!',
        'קיבלת ' || COALESCE(gift_name, 'מתנה') || ' מ-' || COALESCE(sender_username, 'משתמש'),
        jsonb_build_object(
            'gift_id', NEW.gift_id,
            'sender_id', NEW.sender_id,
            'receiver_id', NEW.receiver_id,  -- Added receiver_id
            'sender_username', sender_username,
            'gift_name', gift_name
        )
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Let's also add some debugging to the notify_new_message function
CREATE OR REPLACE FUNCTION notify_new_message()
RETURNS TRIGGER AS $$
DECLARE
    sender_username text;
    recipient_id uuid;
BEGIN
    -- Debug: Log that function was called
    RAISE NOTICE 'notify_new_message called for message_id: %, sender_id: %, chat_id: %', NEW.id, NEW.sender_id, NEW.chat_id;
    
    -- Get sender's username
    SELECT username INTO sender_username
    FROM profiles
    WHERE id = NEW.sender_id;
    
    RAISE NOTICE 'Sender username: %', sender_username;
    
    -- Get recipient ID (the other participant in the chat)
    SELECT user_id INTO recipient_id
    FROM chat_participants
    WHERE chat_id = NEW.chat_id
    AND user_id != NEW.sender_id
    LIMIT 1;
    
    RAISE NOTICE 'Recipient ID found: %', recipient_id;
    
    -- Only create notification if we found a recipient
    IF recipient_id IS NOT NULL THEN
        -- Create notification for the recipient
        PERFORM create_notification(
            recipient_id,
            'message',
            'הודעה חדשה',
            'קיבלת הודעה חדשה מ-' || COALESCE(sender_username, 'משתמש'),
            jsonb_build_object(
                'message_id', NEW.id,
                'chat_id', NEW.chat_id,
                'sender_id', NEW.sender_id,
                'sender_username', sender_username
            )
        );
        
        RAISE NOTICE 'Notification created for recipient: %', recipient_id;
    ELSE
        RAISE NOTICE 'No recipient found for chat_id: %', NEW.chat_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Now let's make sure the triggers are created properly
-- Drop existing triggers first
DROP TRIGGER IF EXISTS trigger_notify_new_message ON messages;
DROP TRIGGER IF EXISTS trigger_notify_new_gift ON user_gifts;

-- Create message notification trigger
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'messages') THEN
        CREATE TRIGGER trigger_notify_new_message
            AFTER INSERT ON messages
            FOR EACH ROW
            EXECUTE FUNCTION notify_new_message();
            
        RAISE NOTICE 'Created message notification trigger successfully';
    ELSE
        RAISE NOTICE 'Messages table does not exist';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error creating message trigger: %', SQLERRM;
END $$;

-- Create gift notification trigger
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_gifts') THEN
        CREATE TRIGGER trigger_notify_new_gift
            AFTER INSERT ON user_gifts
            FOR EACH ROW
            EXECUTE FUNCTION notify_new_gift();
            
        RAISE NOTICE 'Created gift notification trigger successfully';
    ELSE
        RAISE NOTICE 'User_gifts table does not exist';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error creating gift trigger: %', SQLERRM;
END $$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION notify_new_message() TO authenticated;
GRANT EXECUTE ON FUNCTION notify_new_gift() TO authenticated;

-- Let's also check what triggers exist now
SELECT 
    trigger_name, 
    event_object_table, 
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE event_object_table IN ('messages', 'user_gifts')
ORDER BY event_object_table, trigger_name;
