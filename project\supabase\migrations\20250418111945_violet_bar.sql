/*
  # Fix chat participants policy recursion

  1. Changes
    - Drop existing policy that causes recursion
    - Create new simplified policy for chat participants
    
  2. Security
    - Users can only view chat participants for chats they are part of
    - Policy uses a direct comparison instead of a subquery to avoid recursion
*/

-- Drop the existing policy that's causing recursion
DROP POLICY IF EXISTS "Users can view chat participants" ON chat_participants;

-- Create new simplified policy
CREATE POLICY "Users can view chat participants" 
ON chat_participants 
FOR SELECT 
TO public
USING (
  -- Direct check if the user is a participant in the chat
  user_id = auth.uid() OR
  chat_id IN (
    SELECT chat_id 
    FROM chat_participants 
    WHERE user_id = auth.uid()
  )
);