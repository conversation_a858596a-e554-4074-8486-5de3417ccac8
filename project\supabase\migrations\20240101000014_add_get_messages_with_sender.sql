-- Add function to get messages with sender details
-- This migration adds a function to get messages with sender details

-- Create function to get messages with sender details
CREATE OR REPLACE FUNCTION get_messages_with_sender(p_chat_id UUID, p_user_id UUID)
RETURNS TABLE (
  id UUID,
  chat_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  read BOOLEA<PERSON>,
  sender_username TEXT,
  sender_avatar TEXT,
  sender_is_online BOOLEAN
) AS $$
BEGIN
  -- Mark messages as read first
  PERFORM mark_messages_as_read(p_chat_id, p_user_id);
  
  -- Return messages with sender details
  RETURN QUERY
  SELECT 
    m.id,
    m.chat_id,
    m.sender_id,
    m.content,
    m.created_at,
    m.read,
    p.username AS sender_username,
    p.user_metadata->>'profile_image_url' AS sender_avatar,
    p.is_online AS sender_is_online
  FROM 
    messages m
  JOIN 
    profiles p ON m.sender_id = p.id
  WHERE 
    m.chat_id = p_chat_id
  ORDER BY 
    m.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get chat participants with details
CREATE OR REPLACE FUNCTION get_chat_participants_with_details(p_chat_id UUID, p_user_id UUID)
RETURNS TABLE (
  user_id UUID,
  username TEXT,
  avatar TEXT,
  is_online BOOLEAN,
  is_vip BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id AS user_id,
    p.username,
    p.user_metadata->>'profile_image_url' AS avatar,
    p.is_online,
    p.is_vip
  FROM 
    profiles p
  JOIN 
    chat_participants cp ON p.id = cp.user_id
  WHERE 
    cp.chat_id = p_chat_id
    AND p.id != p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get all user chats with details
CREATE OR REPLACE FUNCTION get_user_chats_with_details(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  unread_count BIGINT,
  other_user_id UUID,
  other_username TEXT,
  other_avatar TEXT,
  other_is_online BOOLEAN,
  other_is_vip BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS chat_id,
    c.last_message,
    c.last_message_at,
    c.updated_at,
    COUNT(m.id) FILTER (WHERE m.sender_id != p_user_id AND m.read = FALSE) AS unread_count,
    p.id AS other_user_id,
    p.username AS other_username,
    p.user_metadata->>'profile_image_url' AS other_avatar,
    p.is_online AS other_is_online,
    p.is_vip AS other_is_vip
  FROM 
    chats c
  JOIN 
    chat_participants cp1 ON c.id = cp1.chat_id AND cp1.user_id = p_user_id
  JOIN 
    chat_participants cp2 ON c.id = cp2.chat_id AND cp2.user_id != p_user_id
  JOIN 
    profiles p ON cp2.user_id = p.id
  LEFT JOIN 
    messages m ON c.id = m.chat_id
  GROUP BY 
    c.id, c.last_message, c.last_message_at, c.updated_at, p.id, p.username, p.user_metadata, p.is_online, p.is_vip
  ORDER BY 
    c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
