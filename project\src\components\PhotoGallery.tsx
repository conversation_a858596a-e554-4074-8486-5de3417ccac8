import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Camera, Trash2, Loader2, ChevronRight, ChevronLeft, Maximize2, Minimize2, Upload, UserCircle, Crown, Lock } from 'lucide-react';
import { usePhotos } from '../hooks/usePhotos';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from '../hooks/useNavigate';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';

interface PhotoGalleryProps {
  profile?: Profile;
}

function PhotoGallery({ profile }: PhotoGalleryProps) {
  const { user } = useAuth();
  const { navigateToVip } = useNavigate();
  const { photos, loading, error, uploadPhoto, deletePhoto, setAsProfileImage, profileImageUrl } = usePhotos({ profile });
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [settingProfileImage, setSettingProfileImage] = useState(false);
  const [profileImageError, setProfileImageError] = useState<string | null>(null);
  const [isUserVip, setIsUserVip] = useState(false);
  const [isProfileVip, setIsProfileVip] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if the current user is the owner of the profile
  // We need to compare the user ID with the profile ID
  const isOwner = user?.id === profile?.id;

  // Check if the current user is VIP
  useEffect(() => {
    const checkVipStatus = async () => {
      if (!user?.id) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_vip')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking VIP status:', error);
          return;
        }

        setIsUserVip(data?.is_vip || false);
      } catch (err) {
        console.error('Error checking VIP status:', err);
      }
    };

    // Check if the profile owner is VIP
    const checkProfileVipStatus = async () => {
      if (!profile?.id) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_vip')
          .eq('id', profile.id)
          .single();

        if (error) {
          console.error('Error checking profile VIP status:', error);
          return;
        }

        setIsProfileVip(data?.is_vip || false);
      } catch (err) {
        console.error('Error checking profile VIP status:', err);
      }
    };

    checkVipStatus();
    checkProfileVipStatus();
  }, [user?.id, profile?.id]);

  // Determine if photos should be blurred
  // Photos should be visible if:
  // 1. The user is the owner of the profile
  // 2. The user is VIP
  const shouldBlurPhotos = !isOwner && !isUserVip && photos.length > 0;



  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    setUploadError(null);

    try {
      const file = files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        throw new Error('ניתן להעלות קבצי תמונה בלבד');
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('גודל הקובץ חייב להיות קטן מ-5MB');
      }

      const { error: uploadError } = await uploadPhoto(file);
      if (uploadError) throw uploadError;

    } catch (err) {
      console.error('Upload error:', err);
      setUploadError(err instanceof Error ? err.message : 'אירעה שגיאה בהעלאת התמונה');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  if (loading) {
    return (
      <div className="relative h-[300px] md:h-[400px] bg-gray-100 flex items-center justify-center">
        <Loader2 className="w-8 h-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative h-[300px] md:h-[400px] bg-gray-100 flex items-center justify-center text-red-600 px-4 text-center">
        {error}
      </div>
    );
  }

  const nextSlide = () => {
    if (currentIndex < photos.length - 1) {
      setCurrentIndex(prev => prev + 1);
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleSetAsProfileImage = async () => {
    if (!isOwner || !photos[currentIndex]) return;

    setSettingProfileImage(true);
    setProfileImageError(null);

    try {
      const { error } = await setAsProfileImage(photos[currentIndex]);
      if (error) throw error;
    } catch (err) {
      console.error('Error setting profile image:', err);
      setProfileImageError('אירעה שגיאה בהגדרת תמונת הפרופיל');
    } finally {
      setSettingProfileImage(false);
    }
  };

  return (
    <div className={`relative bg-gray-100 ${isFullscreen ? 'fixed inset-0 z-50' : 'h-[300px] md:h-[400px]'}`}>
      {/* Upload Button - Always shown for profile owner */}
      {isOwner && (
        <div className="absolute top-2 md:top-4 left-2 md:left-4 z-20">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
            disabled={uploading}
          />
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="flex items-center gap-2 bg-pink-600 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50 text-sm md:text-base shadow-lg"
          >
            {uploading ? (
              <>
                <Loader2 className="w-3 h-3 md:w-4 md:h-4 animate-spin" />
                מעלה...
              </>
            ) : (
              <>
                <Upload className="w-3 h-3 md:w-4 md:h-4" />
                העלה תמונה לגלריה
              </>
            )}
          </motion.button>
        </div>
      )}

      {uploadError && (
        <div className="absolute top-12 md:top-16 left-2 md:left-4 z-10 bg-red-50 text-red-700 px-3 py-1.5 md:px-4 md:py-2 rounded-lg max-w-[calc(100%-1rem)] md:max-w-xs text-sm">
          {uploadError}
        </div>
      )}

      {profileImageError && (
        <div className="absolute top-12 md:top-16 right-2 md:right-4 z-10 bg-red-50 text-red-700 px-3 py-1.5 md:px-4 md:py-2 rounded-lg max-w-[calc(100%-1rem)] md:max-w-xs text-sm">
          {profileImageError}
        </div>
      )}

      {/* Main Gallery */}
      <div className="relative h-full flex items-center justify-center">
        <AnimatePresence mode="wait">
          {photos.length > 0 ? (
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="relative w-full h-full"
            >
              <div className="relative w-full h-full">
                <img
                  src={photos[currentIndex]}
                  alt={`תמונה ${currentIndex + 1}`}
                  className={`w-full h-full object-contain ${shouldBlurPhotos ? 'blur-xl' : ''}`}
                />

                {/* VIP Overlay */}
                {shouldBlurPhotos && (
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/30 text-white p-4 text-center">
                    <Lock className="w-12 h-12 mb-3 text-yellow-400" />
                    <h3 className="text-xl font-bold mb-2">תוכן VIP בלבד</h3>
                    <p className="mb-4 max-w-md">צפייה בגלריית התמונות זמינה למשתמשי VIP בלבד</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={navigateToVip}
                      className="flex items-center gap-2 bg-gradient-to-r from-yellow-500 to-amber-500 px-4 py-2 rounded-full font-medium cursor-pointer"
                    >
                      <Crown className="w-5 h-5" />
                      שדרג לחשבון VIP
                    </motion.button>
                  </div>
                )}
              </div>

              {/* Navigation Arrows */}
              {photos.length > 1 && (
                <>
                  <button
                    onClick={prevSlide}
                    className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-1.5 md:p-2 rounded-full hover:bg-black/70 transition-colors"
                  >
                    <ChevronLeft className="w-4 h-4 md:w-6 md:h-6" />
                  </button>
                  <button
                    onClick={nextSlide}
                    className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-1.5 md:p-2 rounded-full hover:bg-black/70 transition-colors"
                  >
                    <ChevronRight className="w-4 h-4 md:w-6 md:h-6" />
                  </button>
                </>
              )}

              {/* Delete & Fullscreen Buttons */}
              <div className="absolute top-2 md:top-4 right-2 md:right-4 flex gap-2">
                {/* Delete button - Only shown to profile owner */}
                {isOwner && (
                  <>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => deletePhoto(photos[currentIndex])}
                      className="bg-red-500 text-white p-1.5 md:p-2 rounded-full shadow-lg hover:bg-red-600 transition-colors"
                      title="מחק תמונה"
                    >
                      <Trash2 className="w-3 h-3 md:w-4 md:h-4" />
                    </motion.button>

                    {/* Set as profile image button */}
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={handleSetAsProfileImage}
                      disabled={settingProfileImage || profileImageUrl === photos[currentIndex]}
                      className={`p-1.5 md:p-2 rounded-full shadow-lg transition-colors border-2 border-white ${
                        profileImageUrl === photos[currentIndex]
                          ? 'bg-green-500 text-white cursor-default'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      } ${settingProfileImage ? 'opacity-50 cursor-wait' : ''}`}
                      title={profileImageUrl === photos[currentIndex] ? 'תמונת פרופיל נוכחית' : 'הגדר כתמונת פרופיל'}
                    >
                      {settingProfileImage ? (
                        <Loader2 className="w-3 h-3 md:w-4 md:h-4 animate-spin" />
                      ) : (
                        <UserCircle className="w-3 h-3 md:w-4 md:h-4" />
                      )}
                    </motion.button>
                  </>
                )}

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={toggleFullscreen}
                  className="bg-gray-800 text-white p-1.5 md:p-2 rounded-full shadow-lg hover:bg-gray-900 transition-colors"
                >
                  {isFullscreen ? (
                    <Minimize2 className="w-3 h-3 md:w-4 md:h-4" />
                  ) : (
                    <Maximize2 className="w-3 h-3 md:w-4 md:h-4" />
                  )}
                </motion.button>
              </div>
            </motion.div>
          ) : (
            <div className="flex flex-col items-center justify-center p-4 md:p-8 text-center">
              <Camera className="w-12 h-12 md:w-16 md:h-16 text-gray-300 mb-3 md:mb-4" />
              <span className="text-gray-400 text-lg font-medium mb-1">אין תמונות בגלריה</span>
              <span className="text-gray-400 text-sm mb-3">הוסף תמונות כדי לשפר את הפרופיל שלך</span>
              {/* Show upload button in empty state for profile owner */}
              {isOwner && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                  className="mt-3 md:mt-4 flex items-center gap-2 bg-pink-600 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50 text-sm md:text-base shadow-lg"
                >
                  <Upload className="w-3 h-3 md:w-4 md:h-4" />
                  העלה תמונה לגלריה
                </motion.button>
              )}
            </div>
          )}
        </AnimatePresence>
      </div>

      {/* VIP Upgrade Banner */}
      {shouldBlurPhotos && (
        <div className="mt-4 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-yellow-500 to-amber-500 p-2 rounded-full">
              <Crown className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">שדרג לחשבון VIP</h3>
              <p className="text-sm text-gray-600">צפייה בגלריית התמונות זמינה למשתמשי VIP בלבד</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={navigateToVip}
            className="bg-gradient-to-r from-yellow-500 to-amber-500 text-white px-4 py-2 rounded-lg font-medium"
          >
            שדרג עכשיו
          </motion.button>
        </div>
      )}

      {/* Thumbnail Strip */}
      {photos.length > 0 && (
        <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-2 md:p-4">
          <div className="flex gap-1 md:gap-2 justify-center">
            {photos.map((photo, index) => (
              <motion.button
                key={photo}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setCurrentIndex(index)}
                className={`relative w-12 h-12 md:w-16 md:h-16 rounded-lg overflow-hidden ${
                  index === currentIndex ? 'ring-2 ring-white' : ''
                }`}
              >
                <img
                  src={photo}
                  alt={`תמונה ${index + 1}`}
                  className={`w-full h-full object-cover ${shouldBlurPhotos ? 'blur-md' : ''}`}
                />
                {shouldBlurPhotos && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <Lock className="w-4 h-4 text-white" />
                  </div>
                )}
              </motion.button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default PhotoGallery;