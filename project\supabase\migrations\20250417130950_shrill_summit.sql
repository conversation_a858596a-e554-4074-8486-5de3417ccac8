/*
  # Initial Schema Setup

  1. New Tables
    - `profiles`
      - `id` (uuid, primary key, references auth.users)
      - `username` (text, unique)
      - `gender` (text)
      - `birth_date` (date)
      - `partner_birth_date` (date, nullable)
      - `city` (text)
      - `area` (text)
      - `phone` (text)
      - `height` (integer)
      - `weight` (integer)
      - `hair_color` (text)
      - `hair_style` (text)
      - `eye_color` (text)
      - `body_type` (text)
      - `marital_status` (text)
      - `children` (integer)
      - `ethnicity` (text)
      - `sexual_preference` (text)
      - `swinging_experience` (text)
      - `smoking_habits` (text)
      - `drinking_habits` (text)
      - `about_us` (text)
      - `looking_for` (text)
      - `seeking_gender` (text[])
      - `meeting_times` (text[])
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS on `profiles` table
    - Add policies for authenticated users to:
      - Read their own profile
      - Update their own profile
      - Read other profiles
*/

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  username text UNIQUE NOT NULL,
  gender text NOT NULL,
  birth_date date NOT NULL,
  partner_birth_date date,
  city text NOT NULL,
  area text NOT NULL,
  phone text NOT NULL,
  height integer,
  weight integer,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children integer,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text[],
  meeting_times text[],
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT username_length CHECK (char_length(username) >= 3)
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can view other profiles"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (true);

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, gender, birth_date, city, area, phone)
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.raw_user_meta_data->>'gender',
    (new.raw_user_meta_data->>'birth_date')::date,
    new.raw_user_meta_data->>'city',
    new.raw_user_meta_data->>'area',
    new.raw_user_meta_data->>'phone'
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();