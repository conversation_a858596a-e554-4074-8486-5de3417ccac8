-- Add chat functions
-- This migration adds improved functions for chat management

-- Function to create or get a chat between two users
CREATE OR REPLACE FUNCTION create_chat_between_users(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
  existing_chat_id UUID;
  new_chat_id UUID;
BEGIN
  -- Check if a chat already exists between these users
  SELECT cp1.chat_id INTO existing_chat_id
  FROM chat_participants cp1
  JOIN chat_participants cp2 ON cp1.chat_id = cp2.chat_id
  WHERE 
    cp1.user_id = user1_id AND cp2.user_id = user2_id
    AND cp1.is_active = TRUE AND cp2.is_active = TRUE
    AND NOT EXISTS (
      SELECT 1 FROM chats c 
      WHERE c.id = cp1.chat_id AND c.is_deleted = TRUE
    );
  
  IF existing_chat_id IS NOT NULL THEN
    RETURN existing_chat_id;
  END IF;
  
  -- Create a new chat
  INSERT INTO chats (created_at, updated_at)
  VALUES (NOW(), NOW())
  RETURNING id INTO new_chat_id;
  
  -- Add participants
  INSERT INTO chat_participants (chat_id, user_id, joined_at, is_active)
  VALUES 
    (new_chat_id, user1_id, NOW(), TRUE),
    (new_chat_id, user2_id, NOW(), TRUE);
  
  RETURN new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all chats for a user with details
CREATE OR REPLACE FUNCTION get_user_chats(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  other_user_id UUID,
  other_username TEXT,
  other_profile_image TEXT,
  other_is_online BOOLEAN,
  unread_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS chat_id,
    c.created_at,
    c.updated_at,
    c.last_message,
    c.last_message_at,
    p.id AS other_user_id,
    p.username AS other_username,
    p.profile_image_url AS other_profile_image,
    p.is_online AS other_is_online,
    COUNT(m.id) FILTER (
      WHERE m.sender_id != p_user_id 
      AND (m.read IS NULL OR m.read = FALSE)
      AND m.is_deleted = FALSE
    ) AS unread_count
  FROM 
    chats c
  JOIN 
    chat_participants cp1 ON c.id = cp1.chat_id 
    AND cp1.user_id = p_user_id
    AND cp1.is_active = TRUE
  JOIN 
    chat_participants cp2 ON c.id = cp2.chat_id 
    AND cp2.user_id != p_user_id
    AND cp2.is_active = TRUE
  JOIN 
    profiles p ON cp2.user_id = p.id
  LEFT JOIN 
    messages m ON c.id = m.chat_id AND m.is_deleted = FALSE
  WHERE
    c.is_deleted = FALSE
  GROUP BY 
    c.id, p.id
  ORDER BY 
    c.updated_at DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get messages for a chat
CREATE OR REPLACE FUNCTION get_chat_messages(p_chat_id UUID, p_user_id UUID)
RETURNS TABLE (
  id UUID,
  chat_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  is_edited BOOLEAN,
  is_deleted BOOLEAN,
  reply_to_id UUID,
  read BOOLEAN,
  sender_username TEXT,
  sender_profile_image TEXT,
  sender_is_online BOOLEAN,
  reply_message TEXT
) AS $$
BEGIN
  -- Mark messages as read
  UPDATE messages
  SET read = TRUE
  WHERE 
    chat_id = p_chat_id
    AND sender_id != p_user_id
    AND (read IS NULL OR read = FALSE)
    AND is_deleted = FALSE;
    
  -- Update last_read_at for the user
  UPDATE chat_participants
  SET last_read_at = NOW()
  WHERE 
    chat_id = p_chat_id
    AND user_id = p_user_id;
  
  -- Return messages with sender details
  RETURN QUERY
  SELECT 
    m.id,
    m.chat_id,
    m.sender_id,
    m.content,
    m.created_at,
    m.updated_at,
    m.is_edited,
    m.is_deleted,
    m.reply_to_id,
    COALESCE(m.read, FALSE),
    p.username AS sender_username,
    p.profile_image_url AS sender_profile_image,
    p.is_online AS sender_is_online,
    rm.content AS reply_message
  FROM 
    messages m
  JOIN 
    profiles p ON m.sender_id = p.id
  LEFT JOIN
    messages rm ON m.reply_to_id = rm.id
  WHERE 
    m.chat_id = p_chat_id
    AND m.is_deleted = FALSE
  ORDER BY 
    m.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to delete a message (soft delete)
CREATE OR REPLACE FUNCTION delete_message(p_message_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  affected_rows INTEGER;
BEGIN
  UPDATE messages
  SET 
    is_deleted = TRUE,
    deleted_at = NOW(),
    content = 'הודעה זו נמחקה'
  WHERE 
    id = p_message_id
    AND sender_id = p_user_id
    AND is_deleted = FALSE
  RETURNING 1 INTO affected_rows;
  
  RETURN affected_rows = 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to delete a chat (soft delete)
CREATE OR REPLACE FUNCTION delete_chat(p_chat_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  affected_rows INTEGER;
BEGIN
  -- Check if user is a participant
  IF NOT EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_id = p_chat_id AND user_id = p_user_id AND is_active = TRUE
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Mark the participant as inactive
  UPDATE chat_participants
  SET 
    is_active = FALSE,
    left_at = NOW()
  WHERE 
    chat_id = p_chat_id
    AND user_id = p_user_id
  RETURNING 1 INTO affected_rows;
  
  -- If both participants are inactive, mark the chat as deleted
  IF NOT EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_id = p_chat_id AND is_active = TRUE
  ) THEN
    UPDATE chats
    SET 
      is_deleted = TRUE,
      deleted_at = NOW()
    WHERE id = p_chat_id;
  END IF;
  
  RETURN affected_rows = 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
