-- Fix get_user_chats_with_details function
-- This migration fixes the get_user_chats_with_details function to handle profile_image_url correctly

-- Drop the existing function
DROP FUNCTION IF EXISTS get_user_chats_with_details(UUID);

-- Create an improved version of the function
CREATE OR REPLACE FUNCTION get_user_chats_with_details(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  unread_count BIGINT,
  other_user_id UUID,
  other_username TEXT,
  other_avatar TEXT,
  other_is_online BOOLEAN,
  other_is_vip BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS chat_id,
    c.last_message,
    c.last_message_at,
    c.updated_at,
    COUNT(m.id) FILTER (WHERE m.sender_id != p_user_id AND (m.read IS NULL OR m.read = FALSE)) AS unread_count,
    p.id AS other_user_id,
    p.username AS other_username,
    COALESCE(p.profile_image_url, '') AS other_avatar,
    COALESCE(p.is_online, FALSE) AS other_is_online,
    COALESCE(p.is_vip, FALSE) AS other_is_vip
  FROM 
    chats c
  JOIN 
    chat_participants cp1 ON c.id = cp1.chat_id AND cp1.user_id = p_user_id
  JOIN 
    chat_participants cp2 ON c.id = cp2.chat_id AND cp2.user_id != p_user_id
  JOIN 
    profiles p ON cp2.user_id = p.id
  LEFT JOIN 
    messages m ON c.id = m.chat_id
  GROUP BY 
    c.id, c.last_message, c.last_message_at, c.updated_at, p.id, p.username, p.profile_image_url, p.is_online, p.is_vip
  ORDER BY 
    c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix get_messages_with_sender function
DROP FUNCTION IF EXISTS get_messages_with_sender(UUID, UUID);

-- Create an improved version of the function
CREATE OR REPLACE FUNCTION get_messages_with_sender(p_chat_id UUID, p_user_id UUID)
RETURNS TABLE (
  id UUID,
  chat_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  read BOOLEAN,
  sender_username TEXT,
  sender_avatar TEXT,
  sender_is_online BOOLEAN
) AS $$
BEGIN
  -- Mark messages as read first
  UPDATE messages
  SET read = TRUE
  WHERE chat_id = p_chat_id
  AND sender_id != p_user_id
  AND (read IS NULL OR read = FALSE);
  
  -- Return messages with sender details
  RETURN QUERY
  SELECT 
    m.id,
    m.chat_id,
    m.sender_id,
    m.content,
    m.created_at,
    COALESCE(m.read, FALSE),
    p.username AS sender_username,
    COALESCE(p.profile_image_url, '') AS sender_avatar,
    COALESCE(p.is_online, FALSE) AS sender_is_online
  FROM 
    messages m
  JOIN 
    profiles p ON m.sender_id = p.id
  WHERE 
    m.chat_id = p_chat_id
  ORDER BY 
    m.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
