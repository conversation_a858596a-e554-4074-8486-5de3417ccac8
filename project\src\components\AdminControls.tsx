import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trash2, Edit, X, AlertTriangle, Image, User, Database } from 'lucide-react';
import { useAdmin } from '../hooks/useAdmin';
import AdminEditModal from './AdminEditModal';
import { supabase } from '../lib/supabase';

interface AdminControlsProps {
  userId: string;
  username?: string;
  photos?: string[];
  profileData?: any;
  onProfileUpdate?: () => void;
}

export default function AdminControls({ userId, username, photos = [], profileData, onProfileUpdate }: AdminControlsProps) {
  const { isAdmin, deleteUserProfile, updateUserProfile, deleteUserPhoto, clearUserData } = useAdmin();
  const [showConfirm, setShowConfirm] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [userPhotos, setUserPhotos] = useState<string[]>([]);

  // Fetch user photos from multiple sources
  useEffect(() => {
    const fetchUserPhotos = async () => {
      try {
        const allPhotos: string[] = [];

        // Get photos from storage buckets
        const buckets = ['photos', 'profile-photos', 'photo'];

        for (const bucket of buckets) {
          try {
            const { data: files } = await supabase.storage
              .from(bucket)
              .list(userId);

            if (files && files.length > 0) {
              const bucketPhotos = files.map(file => {
                const { data } = supabase.storage
                  .from(bucket)
                  .getPublicUrl(`${userId}/${file.name}`);
                return data.publicUrl;
              });
              allPhotos.push(...bucketPhotos);
            }
          } catch (error) {
            console.error(`Error fetching from ${bucket}:`, error);
          }
        }

        // Get profile image URL
        if (profileData?.profile_image_url) {
          allPhotos.push(profileData.profile_image_url);
        }

        // Remove duplicates
        const uniquePhotos = [...new Set(allPhotos)];
        setUserPhotos(uniquePhotos);
      } catch (error) {
        console.error('Error fetching user photos:', error);
      }
    };

    if (userId) {
      fetchUserPhotos();
    }
  }, [userId, profileData]);

  if (!isAdmin) return null;

  const handleDeleteProfile = async () => {
    setLoading(true);
    try {
      const { error } = await deleteUserProfile(userId);
      if (!error) {
        alert('פרופיל נמחק בהצלחה');
        onProfileUpdate?.();
      } else {
        alert('שגיאה במחיקת הפרופיל');
      }
    } catch (error) {
      alert('שגיאה במחיקת הפרופיל');
    } finally {
      setLoading(false);
      setShowConfirm(null);
    }
  };

  const handleDeletePhoto = async (photoUrl: string) => {
    setLoading(true);
    try {
      const { error } = await deleteUserPhoto(userId, photoUrl);
      if (!error) {
        // Remove from local state immediately
        setUserPhotos(prev => prev.filter(photo => photo !== photoUrl));
        alert('תמונה נמחקה בהצלחה');
        onProfileUpdate?.();
      } else {
        alert('שגיאה במחיקת התמונה');
      }
    } catch (error) {
      alert('שגיאה במחיקת התמונה');
    } finally {
      setLoading(false);
    }
  };

  const handleClearData = async (dataType: 'photos' | 'profile_data' | 'all') => {
    setLoading(true);
    try {
      const { error } = await clearUserData(userId, dataType);
      if (!error) {
        // Clear photos from local state if photos were cleared
        if (dataType === 'photos' || dataType === 'all') {
          setUserPhotos([]);
        }

        const messages = {
          photos: 'כל התמונות נמחקו בהצלחה',
          profile_data: 'נתוני הפרופיל נוקו בהצלחה',
          all: 'כל הנתונים נוקו בהצלחה'
        };
        alert(messages[dataType]);
        onProfileUpdate?.();
      } else {
        alert('שגיאה בניקוי הנתונים');
      }
    } catch (error) {
      alert('שגיאה בניקוי הנתונים');
    } finally {
      setLoading(false);
      setShowConfirm(null);
    }
  };

  const ConfirmDialog = ({ action, onConfirm, onCancel, title, message }: {
    action: string;
    onConfirm: () => void;
    onCancel: () => void;
    title: string;
    message: string;
  }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-lg p-6 max-w-md mx-4"
      >
        <div className="flex items-center gap-3 mb-4">
          <AlertTriangle className="w-6 h-6 text-red-500" />
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="flex gap-3 justify-end">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            ביטול
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            {loading ? 'מוחק...' : 'אישור'}
          </button>
        </div>
      </motion.div>
    </div>
  );

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
      <div className="flex items-center gap-2 mb-3">
        <AlertTriangle className="w-5 h-5 text-red-600" />
        <h3 className="text-lg font-semibold text-red-800">בקרת אדמין</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {/* Edit Profile */}
        <button
          onClick={() => setShowEditModal(true)}
          disabled={loading}
          className="flex items-center gap-2 p-3 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 disabled:opacity-50"
        >
          <Edit className="w-4 h-4" />
          <span>ערוך פרופיל</span>
        </button>

        {/* Delete specific photos */}
        {userPhotos.length > 0 && (
          <div className="space-y-2 col-span-full">
            <h4 className="text-sm font-medium text-red-700">מחיקת תמונות ({userPhotos.length})</h4>
            <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 max-h-40 overflow-y-auto">
              {userPhotos.map((photo, index) => (
                <div key={index} className="relative">
                  <img
                    src={photo}
                    alt={`Photo ${index + 1}`}
                    className="w-full h-16 object-cover rounded"
                    onError={(e) => {
                      // Hide broken images
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  <button
                    onClick={() => handleDeletePhoto(photo)}
                    disabled={loading}
                    className="absolute top-1 right-1 bg-red-600 text-white rounded-full p-1 hover:bg-red-700 text-xs"
                  >
                    <X className="w-2 h-2" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Clear all photos */}
        <button
          onClick={() => setShowConfirm('photos')}
          disabled={loading}
          className="flex items-center gap-2 p-3 bg-orange-100 text-orange-800 rounded hover:bg-orange-200 disabled:opacity-50"
        >
          <Image className="w-4 h-4" />
          <span>מחק כל התמונות</span>
        </button>

        {/* Clear profile data */}
        <button
          onClick={() => setShowConfirm('profile_data')}
          disabled={loading}
          className="flex items-center gap-2 p-3 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 disabled:opacity-50"
        >
          <User className="w-4 h-4" />
          <span>נקה נתוני פרופיל</span>
        </button>

        {/* Clear all data */}
        <button
          onClick={() => setShowConfirm('all')}
          disabled={loading}
          className="flex items-center gap-2 p-3 bg-purple-100 text-purple-800 rounded hover:bg-purple-200 disabled:opacity-50"
        >
          <Database className="w-4 h-4" />
          <span>נקה כל הנתונים</span>
        </button>

        {/* Delete entire profile */}
        <button
          onClick={() => setShowConfirm('delete')}
          disabled={loading}
          className="flex items-center gap-2 p-3 bg-red-100 text-red-800 rounded hover:bg-red-200 disabled:opacity-50"
        >
          <Trash2 className="w-4 h-4" />
          <span>מחק פרופיל</span>
        </button>
      </div>

      {/* Confirmation dialogs */}
      {showConfirm === 'delete' && (
        <ConfirmDialog
          action="delete"
          title="מחיקת פרופיל"
          message={`האם אתה בטוח שברצונך למחוק את הפרופיל של ${username || 'משתמש זה'}? פעולה זו לא ניתנת לביטול.`}
          onConfirm={handleDeleteProfile}
          onCancel={() => setShowConfirm(null)}
        />
      )}

      {showConfirm === 'photos' && (
        <ConfirmDialog
          action="photos"
          title="מחיקת כל התמונות"
          message={`האם אתה בטוח שברצונך למחוק את כל התמונות של ${username || 'משתמש זה'}?`}
          onConfirm={() => handleClearData('photos')}
          onCancel={() => setShowConfirm(null)}
        />
      )}

      {showConfirm === 'profile_data' && (
        <ConfirmDialog
          action="profile_data"
          title="ניקוי נתוני פרופיל"
          message={`האם אתה בטוח שברצונך לנקות את כל נתוני הפרופיל של ${username || 'משתמש זה'}?`}
          onConfirm={() => handleClearData('profile_data')}
          onCancel={() => setShowConfirm(null)}
        />
      )}

      {showConfirm === 'all' && (
        <ConfirmDialog
          action="all"
          title="ניקוי כל הנתונים"
          message={`האם אתה בטוח שברצונך לנקות את כל הנתונים של ${username || 'משתמש זה'}? זה כולל תמונות ונתוני פרופיל.`}
          onConfirm={() => handleClearData('all')}
          onCancel={() => setShowConfirm(null)}
        />
      )}

      {/* Edit Modal */}
      <AdminEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        userId={userId}
        currentData={profileData}
        onUpdate={() => {
          onProfileUpdate?.();
          setShowEditModal(false);
        }}
      />
    </div>
  );
}
