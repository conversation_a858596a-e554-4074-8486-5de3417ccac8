/*
  # Create Gifts Feature

  1. New Tables
    - `gifts` table for storing gift options
      - `id` (uuid, primary key)
      - `name` (text)
      - `price` (integer) - cost in points
      - `image_url` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `user_gifts` table for tracking gifts sent between users
      - `id` (uuid, primary key)
      - `sender_id` (uuid, references profiles)
      - `receiver_id` (uuid, references profiles)
      - `gift_id` (uuid, references gifts)
      - `message` (text)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - All users can view gifts
      - Ad<PERSON> can manage gifts
      - Users can view gifts they've sent or received
      - Users can send gifts
*/

-- Create gifts table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'gifts') THEN
    CREATE TABLE gifts (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      name text NOT NULL,
      price integer NOT NULL,
      image_url text,
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now()
    );
  END IF;
END
$$;

-- Create user_gifts table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_gifts') THEN
    CREATE TABLE user_gifts (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      sender_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
      receiver_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
      gift_id uuid REFERENCES gifts(id) ON DELETE RESTRICT,
      message text,
      created_at timestamptz DEFAULT now()
    );
  END IF;
END
$$;

-- Enable RLS
ALTER TABLE gifts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_gifts ENABLE ROW LEVEL SECURITY;

-- Create policies for gifts using DO blocks to check if they exist
DO $$
BEGIN
  -- Check if "Users can view gifts" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'gifts' AND policyname = 'Users can view gifts'
  ) THEN
    CREATE POLICY "Users can view gifts"
      ON gifts
      FOR SELECT
      TO authenticated;
  END IF;

  -- Check if "Admins can manage gifts" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'gifts' AND policyname = 'Admins can manage gifts'
  ) THEN
    CREATE POLICY "Admins can manage gifts"
      ON gifts
      FOR ALL
      TO authenticated
      USING (true)
      WITH CHECK (true);
  END IF;
END
$$;

-- Create policies for user_gifts using DO blocks to check if they exist
DO $$
BEGIN
  -- Check if "Users can view their own gifts" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'user_gifts' AND policyname = 'Users can view their own gifts'
  ) THEN
    CREATE POLICY "Users can view their own gifts"
      ON user_gifts
      FOR SELECT
      TO authenticated
      USING (auth.uid() = sender_id OR auth.uid() = receiver_id);
  END IF;

  -- Check if "Users can send gifts" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'user_gifts' AND policyname = 'Users can send gifts'
  ) THEN
    CREATE POLICY "Users can send gifts"
      ON user_gifts
      FOR INSERT
      TO authenticated
      WITH CHECK (auth.uid() = sender_id);
  END IF;

  -- Check if "Admins can view all gifts" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'user_gifts' AND policyname = 'Admins can view all gifts'
  ) THEN
    CREATE POLICY "Admins can view all gifts"
      ON user_gifts
      FOR SELECT
      TO authenticated
      USING (auth.email() = '<EMAIL>');
  END IF;
END
$$;

-- Insert default gifts only if the table is empty
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM gifts LIMIT 1) THEN
    INSERT INTO gifts (name, price, image_url)
    VALUES
      ('לב', 50, 'https://example.com/gifts/heart.png'),
      ('פרח', 30, 'https://example.com/gifts/flower.png'),
      ('שמפניה', 100, 'https://example.com/gifts/champagne.png'),
      ('שוקולד', 40, 'https://example.com/gifts/chocolate.png'),
      ('טבעת', 200, 'https://example.com/gifts/ring.png');
  END IF;
END
$$;
