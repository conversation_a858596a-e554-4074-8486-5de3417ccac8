/*
  # Create Points Packages Table

  1. New Tables
    - `points_packages` table for storing points package options
      - `id` (uuid, primary key)
      - `name` (text)
      - `points` (integer)
      - `price` (integer)
      - `is_featured` (boolean)
      - `is_active` (boolean)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - <PERSON><PERSON> can manage packages
      - Authenticated users can view packages
*/

-- Create points_packages table
CREATE TABLE IF NOT EXISTS points_packages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  points integer NOT NULL,
  price integer NOT NULL,
  is_featured boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE points_packages ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "<PERSON><PERSON> can manage points packages"
  ON points_packages
  FOR ALL
  TO authenticated
  USING (auth.email() = '<EMAIL>')
  WITH CHECK (auth.email() = '<EMAIL>');

CREATE POLICY "Users can view points packages"
  ON points_packages
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Insert default packages
INSERT INTO points_packages (name, points, price, is_featured, is_active)
VALUES 
  ('חבילה בסיסית', 100, 29, false, true),
  ('חבילה פופולרית', 500, 99, true, true),
  ('חבילת פרימיום', 1000, 179, false, true)
ON CONFLICT (id) DO NOTHING;
