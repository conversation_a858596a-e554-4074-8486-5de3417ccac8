import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';

// Simple global store for profile images
const profileImageCache = new Map<string, string>();
const subscribers = new Set<(userId: string, imageUrl: string) => void>();

// Function to notify all subscribers about image changes
const notifySubscribers = (userId: string, imageUrl: string) => {
  profileImageCache.set(userId, imageUrl);
  subscribers.forEach(callback => callback(userId, imageUrl));
};

// Hook to get and manage profile images
export function useSimpleProfileImage(userId?: string) {
  const { user } = useAuth();
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const targetUserId = userId || user?.id;

  // Load profile image
  useEffect(() => {
    if (!targetUserId) return;

    const loadProfileImage = async () => {
      // Check cache first
      const cachedImage = profileImageCache.get(targetUserId);
      if (cachedImage) {
        setProfileImage(cachedImage);
        return;
      }

      setLoading(true);
      try {
        // Try to get from profile_images table
        const { data: profileImageData, error: profileImageError } = await supabase
          .from('profile_images')
          .select('image_url')
          .eq('user_id', targetUserId)
          .single();

        if (!profileImageError && profileImageData?.image_url) {
          const imageUrl = profileImageData.image_url;
          setProfileImage(imageUrl);
          profileImageCache.set(targetUserId, imageUrl);
          console.log('✅ Profile image loaded from profile_images table:', imageUrl);
          return;
        }

        // Fallback to auth metadata for current user
        if (user && targetUserId === user.id && user.user_metadata?.profile_image_url) {
          const authImage = user.user_metadata.profile_image_url;
          setProfileImage(authImage);
          profileImageCache.set(targetUserId, authImage);
          console.log('✅ Profile image loaded from auth metadata:', authImage);
          return;
        }

        // No profile image found
        setProfileImage(null);
        console.log('ℹ️ No profile image found for user:', targetUserId);

      } catch (error) {
        console.error('Error loading profile image:', error);
        setProfileImage(null);
      } finally {
        setLoading(false);
      }
    };

    loadProfileImage();
  }, [targetUserId, user]);

  // Subscribe to profile image changes
  useEffect(() => {
    if (!targetUserId) return;

    const handleImageChange = (changedUserId: string, newImageUrl: string) => {
      if (changedUserId === targetUserId) {
        console.log('🔄 Profile image updated for user:', changedUserId, 'new URL:', newImageUrl);
        setProfileImage(newImageUrl);
      }
    };

    subscribers.add(handleImageChange);

    return () => {
      subscribers.delete(handleImageChange);
    };
  }, [targetUserId]);

  return { profileImage, loading };
}

// Function to set profile image
export const setSimpleProfileImage = async (imageUrl: string) => {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  try {
    console.log('🔄 Setting profile image for user:', user.id, 'URL:', imageUrl);

    // Update auth metadata
    const { error: authError } = await supabase.auth.updateUser({
      data: { profile_image_url: imageUrl }
    });

    if (authError) {
      console.error('❌ Failed to update auth metadata:', authError);
      throw authError;
    }

    console.log('✅ Auth metadata updated successfully');

    // Update profile_images table using the upsert function
    const { error: upsertError } = await supabase.rpc('upsert_profile_image', {
      p_user_id: user.id,
      p_image_url: imageUrl
    });

    if (upsertError) {
      console.error('❌ Failed to update profile_images table:', upsertError);
      // Don't throw error - auth update is more important
    } else {
      console.log('✅ Profile image updated in database');
    }

    // Notify all subscribers
    notifySubscribers(user.id, imageUrl);
    console.log('📡 Notified all subscribers about image change');

    return { success: true };

  } catch (error) {
    console.error('❌ Error setting profile image:', error);
    throw error;
  }
};

// Function to get profile image URL for any user
export const getProfileImageUrl = async (userId: string): Promise<string | null> => {
  // Check cache first
  const cachedImage = profileImageCache.get(userId);
  if (cachedImage) {
    return cachedImage;
  }

  try {
    // Get from database
    const { data, error } = await supabase
      .from('profile_images')
      .select('image_url')
      .eq('user_id', userId)
      .single();

    if (!error && data?.image_url) {
      profileImageCache.set(userId, data.image_url);
      return data.image_url;
    }

    return null;
  } catch (error) {
    console.error('Error getting profile image URL:', error);
    return null;
  }
};
