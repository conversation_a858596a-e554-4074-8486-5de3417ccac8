import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, User, Loader2 } from 'lucide-react';
import { useAdmin } from '../hooks/useAdmin';

interface AdminEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  profileData: any;
  onUpdate: () => void;
}

export default function AdminEditModal({ isOpen, onClose, userId, profileData, onUpdate }: AdminEditModalProps) {
  const { executeAdminAction, actionLoading } = useAdmin();
  const [formData, setFormData] = useState({
    birth_date: '',
    partner_birth_date: '',
    name: '',
    height: '',
    city: '',
    profession: '',
    education: '',
    religion: '',
    religiosity: '',
    smoking: '',
    drinking: '',
    children: '',
    wants_children: '',
    relationship_status: '',
    looking_for: '',
    about: '',
    aboutUs: '',
    lookingFor: '',
    seekingGender: [] as string[],
    meetingTimes: [] as string[],
    // Couple fields
    '1name': '',
    '1birthDate': '',
    '1height': '',
    '2name': '',
    '2birthDate': '',
    '2height': '',
    name1: '',
    name2: '',
    height1: '',
    height2: ''
  });

  useEffect(() => {
    if (isOpen && profileData) {
      const metadata = profileData.user_metadata?.profile_data || {};
      setFormData({
        birth_date: profileData.birth_date || metadata.birth_date || '',
        partner_birth_date: profileData.partner_birth_date || metadata.partner_birth_date || '',
        name: metadata.name || '',
        height: metadata.height || '',
        city: metadata.city || '',
        profession: metadata.profession || '',
        education: metadata.education || '',
        religion: metadata.religion || '',
        religiosity: metadata.religiosity || '',
        smoking: metadata.smoking || '',
        drinking: metadata.drinking || '',
        children: metadata.children || '',
        wants_children: metadata.wants_children || '',
        relationship_status: metadata.relationship_status || '',
        looking_for: metadata.looking_for || '',
        about: metadata.about || '',
        aboutUs: metadata.aboutUs || '',
        lookingFor: metadata.lookingFor || '',
        seekingGender: metadata.seekingGender || [],
        meetingTimes: metadata.meetingTimes || [],
        // Couple fields
        '1name': metadata['1name'] || '',
        '1birthDate': metadata['1birthDate'] || '',
        '1height': metadata['1height'] || '',
        '2name': metadata['2name'] || '',
        '2birthDate': metadata['2birthDate'] || '',
        '2height': metadata['2height'] || '',
        name1: metadata.name1 || '',
        name2: metadata.name2 || '',
        height1: metadata.height1 || '',
        height2: metadata.height2 || ''
      });
    }
  }, [isOpen, profileData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (fieldName: string, value: string) => {
    setFormData(prev => {
      const currentValues = prev[fieldName as keyof typeof prev] as string[] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];

      return {
        ...prev,
        [fieldName]: newValues
      };
    });
  };

  const getSafeValue = (fieldName: string) => {
    return formData[fieldName as keyof typeof formData] ?? '';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Format data like EditProfileModal does
    const updateData = {
      birth_date: formData.birth_date,
      partner_birth_date: formData.partner_birth_date,
      user_metadata: {
        profile_data: {
          name: formData.name,
          height: formData.height,
          city: formData.city,
          profession: formData.profession,
          education: formData.education,
          religion: formData.religion,
          religiosity: formData.religiosity,
          smoking: formData.smoking,
          drinking: formData.drinking,
          children: formData.children,
          wants_children: formData.wants_children,
          relationship_status: formData.relationship_status,
          looking_for: formData.looking_for,
          about: formData.about,
          aboutUs: formData.aboutUs,
          lookingFor: formData.lookingFor,
          seekingGender: formData.seekingGender,
          meetingTimes: formData.meetingTimes,
          // Couple fields
          '1name': formData['1name'],
          '1birthDate': formData['1birthDate'],
          '1height': formData['1height'],
          '2name': formData['2name'],
          '2birthDate': formData['2birthDate'],
          '2height': formData['2height'],
          name1: formData.name1,
          name2: formData.name2,
          height1: formData.height1,
          height2: formData.height2
        }
      }
    };

    const result = await executeAdminAction({
      type: 'update_profile',
      userId,
      data: updateData
    });

    if (result.success) {
      onUpdate();
      onClose();
    }
  };

  if (!isOpen) return null;

  const formSections = [
    {
      title: 'פרטים בסיסיים',
      icon: User,
      fields: [
        { name: 'username', label: 'שם משתמש', type: 'text' },
        { name: 'gender', label: 'מין', type: 'select', options: [
          { value: '', label: 'בחר' },
          { value: 'male', label: 'גבר' },
          { value: 'female', label: 'אישה' },
          { value: 'couple', label: 'זוג' }
        ]},
        { name: 'birth_date', label: 'תאריך לידה', type: 'date' },
        { name: 'partner_birth_date', label: 'תאריך לידה בן/בת זוג', type: 'date' }
      ]
    },
    {
      title: 'מיקום וקשר',
      icon: MapPin,
      fields: [
        { name: 'city', label: 'עיר', type: 'text' },
        { name: 'area', label: 'אזור', type: 'text' },
        { name: 'phone', label: 'טלפון', type: 'text' }
      ]
    },
    {
      title: 'מראה חיצוני',
      icon: User,
      fields: [
        { name: 'height', label: 'גובה (ס"מ)', type: 'number' },
        { name: 'weight', label: 'משקל (ק"ג)', type: 'number' },
        { name: 'hair_color', label: 'צבע שיער', type: 'text' },
        { name: 'eye_color', label: 'צבע עיניים', type: 'text' },
        { name: 'body_type', label: 'מבנה גוף', type: 'text' },
        { name: 'ethnicity', label: 'מוצא', type: 'text' }
      ]
    },
    {
      title: 'העדפות והרגלים',
      icon: User,
      fields: [
        { name: 'sexual_preference', label: 'העדפה מינית', type: 'text' },
        { name: 'swinging_experience', label: 'ניסיון בסווינגינג', type: 'text' },
        { name: 'smoking_habits', label: 'עישון', type: 'text' },
        { name: 'drinking_habits', label: 'שתייה', type: 'text' },
        { name: 'marital_status', label: 'מצב משפחתי', type: 'text' },
        { name: 'children', label: 'ילדים', type: 'number' }
      ]
    }
  ];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">עריכת פרופיל - אדמין</h2>
                <p className="text-sm text-gray-600">עריכת פרטי {profileData?.username}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-6 h-6 text-gray-500" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            <div className="space-y-6">
              {/* Birth Dates for Couples */}
              {profileData?.gender === 'couple' && (
                <div className="space-y-4 bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-800">תאריכי לידה</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-700">תאריך לידה גבר</label>
                      <input
                        type="date"
                        name="birth_date"
                        value={getSafeValue('birth_date')}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-pink-700">תאריך לידה אישה</label>
                      <input
                        type="date"
                        name="partner_birth_date"
                        value={getSafeValue('partner_birth_date')}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Personal Fields for Couples */}
              {profileData?.gender === 'couple' ? (
                <>
                  {/* Men's Details */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">פרטי גבר</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">שם גבר</label>
                        <input
                          type="text"
                          name="name1"
                          value={getSafeValue('name1')}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">גובה גבר</label>
                        <input
                          type="number"
                          name="height1"
                          value={getSafeValue('height1')}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Women's Details */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">פרטי אישה</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">שם אישה</label>
                        <input
                          type="text"
                          name="name2"
                          value={getSafeValue('name2')}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">גובה אישה</label>
                        <input
                          type="number"
                          name="height2"
                          value={getSafeValue('height2')}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                /* Single Person Fields */
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">פרטים אישיים</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">תאריך לידה</label>
                      <input
                        type="date"
                        name="birth_date"
                        value={getSafeValue('birth_date')}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">שם</label>
                      <input
                        type="text"
                        name="name"
                        value={getSafeValue('name')}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">גובה</label>
                      <input
                        type="number"
                        name="height"
                        value={getSafeValue('height')}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">עיר</label>
                      <input
                        type="text"
                        name="city"
                        value={getSafeValue('city')}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Shared Fields */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">פרטים נוספים</h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700">קצת עלינו</label>
                  <textarea
                    name="aboutUs"
                    value={getSafeValue('aboutUs')}
                    onChange={handleInputChange}
                    rows={4}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">מה אנחנו מחפשים</label>
                  <textarea
                    name="lookingFor"
                    value={getSafeValue('lookingFor')}
                    onChange={handleInputChange}
                    rows={4}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">מחפשים</label>
                  <div className="flex gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.seekingGender?.includes('male')}
                        onChange={() => handleCheckboxChange('seekingGender', 'male')}
                        className="rounded text-pink-600 focus:ring-pink-500"
                      />
                      <span>גבר</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.seekingGender?.includes('female')}
                        onChange={() => handleCheckboxChange('seekingGender', 'female')}
                        className="rounded text-pink-600 focus:ring-pink-500"
                      />
                      <span>אישה</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.seekingGender?.includes('couple')}
                        onChange={() => handleCheckboxChange('seekingGender', 'couple')}
                        className="rounded text-pink-600 focus:ring-pink-500"
                      />
                      <span>זוג</span>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">שעות מפגש מועדפות</label>
                  <div className="flex gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.meetingTimes?.includes('morning')}
                        onChange={() => handleCheckboxChange('meetingTimes', 'morning')}
                        className="rounded text-pink-600 focus:ring-pink-500"
                      />
                      <span>בוקר</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.meetingTimes?.includes('afternoon')}
                        onChange={() => handleCheckboxChange('meetingTimes', 'afternoon')}
                        className="rounded text-pink-600 focus:ring-pink-500"
                      />
                      <span>אחר הצהריים</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.meetingTimes?.includes('evening')}
                        onChange={() => handleCheckboxChange('meetingTimes', 'evening')}
                        className="rounded text-pink-600 focus:ring-pink-500"
                      />
                      <span>ערב</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end gap-3 pt-6 border-t mt-8">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                ביטול
              </button>
              <button
                type="submit"
                disabled={actionLoading}
                className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {actionLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {actionLoading ? 'שומר...' : 'שמור שינויים'}
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
