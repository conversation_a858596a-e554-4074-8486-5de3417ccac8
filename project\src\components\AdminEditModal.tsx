import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, User, Calendar, MapPin, Phone, Loader2 } from 'lucide-react';
import { useAdmin } from '../hooks/useAdmin';

interface AdminEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  profileData: any;
  onUpdate: () => void;
}

export default function AdminEditModal({ isOpen, onClose, userId, profileData, onUpdate }: AdminEditModalProps) {
  const { executeAdminAction, actionLoading } = useAdmin();
  const [formData, setFormData] = useState({
    username: '',
    gender: '',
    birth_date: '',
    partner_birth_date: '',
    city: '',
    area: '',
    phone: '',
    height: '',
    weight: '',
    hair_color: '',
    eye_color: '',
    body_type: '',
    ethnicity: '',
    sexual_preference: '',
    swinging_experience: '',
    smoking_habits: '',
    drinking_habits: '',
    marital_status: '',
    children: '',
    about_us: '',
    looking_for: ''
  });

  useEffect(() => {
    if (isOpen && profileData) {
      setFormData({
        username: profileData.username || '',
        gender: profileData.gender || '',
        birth_date: profileData.birth_date || '',
        partner_birth_date: profileData.partner_birth_date || '',
        city: profileData.city || '',
        area: profileData.area || '',
        phone: profileData.phone || '',
        height: profileData.height?.toString() || '',
        weight: profileData.weight?.toString() || '',
        hair_color: profileData.hair_color || '',
        eye_color: profileData.eye_color || '',
        body_type: profileData.body_type || '',
        ethnicity: profileData.ethnicity || '',
        sexual_preference: profileData.sexual_preference || '',
        swinging_experience: profileData.swinging_experience || '',
        smoking_habits: profileData.smoking_habits || '',
        drinking_habits: profileData.drinking_habits || '',
        marital_status: profileData.marital_status || '',
        children: profileData.children?.toString() || '',
        about_us: profileData.about_us || '',
        looking_for: profileData.looking_for || ''
      });
    }
  }, [isOpen, profileData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await executeAdminAction({
      type: 'update_profile',
      userId,
      data: formData
    });

    if (result.success) {
      onUpdate();
      onClose();
    }
  };

  if (!isOpen) return null;

  const formSections = [
    {
      title: 'פרטים בסיסיים',
      icon: User,
      fields: [
        { name: 'username', label: 'שם משתמש', type: 'text' },
        { name: 'gender', label: 'מין', type: 'select', options: [
          { value: '', label: 'בחר' },
          { value: 'male', label: 'גבר' },
          { value: 'female', label: 'אישה' },
          { value: 'couple', label: 'זוג' }
        ]},
        { name: 'birth_date', label: 'תאריך לידה', type: 'date' },
        { name: 'partner_birth_date', label: 'תאריך לידה בן/בת זוג', type: 'date' }
      ]
    },
    {
      title: 'מיקום וקשר',
      icon: MapPin,
      fields: [
        { name: 'city', label: 'עיר', type: 'text' },
        { name: 'area', label: 'אזור', type: 'text' },
        { name: 'phone', label: 'טלפון', type: 'text' }
      ]
    },
    {
      title: 'מראה חיצוני',
      icon: User,
      fields: [
        { name: 'height', label: 'גובה (ס"מ)', type: 'number' },
        { name: 'weight', label: 'משקל (ק"ג)', type: 'number' },
        { name: 'hair_color', label: 'צבע שיער', type: 'text' },
        { name: 'eye_color', label: 'צבע עיניים', type: 'text' },
        { name: 'body_type', label: 'מבנה גוף', type: 'text' },
        { name: 'ethnicity', label: 'מוצא', type: 'text' }
      ]
    },
    {
      title: 'העדפות והרגלים',
      icon: User,
      fields: [
        { name: 'sexual_preference', label: 'העדפה מינית', type: 'text' },
        { name: 'swinging_experience', label: 'ניסיון בסווינגינג', type: 'text' },
        { name: 'smoking_habits', label: 'עישון', type: 'text' },
        { name: 'drinking_habits', label: 'שתייה', type: 'text' },
        { name: 'marital_status', label: 'מצב משפחתי', type: 'text' },
        { name: 'children', label: 'ילדים', type: 'number' }
      ]
    }
  ];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">עריכת פרופיל - אדמין</h2>
                <p className="text-sm text-gray-600">עריכת פרטי {profileData?.username}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-6 h-6 text-gray-500" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            <div className="space-y-8">
              {formSections.map((section, sectionIndex) => {
                const Icon = section.icon;
                return (
                  <div key={sectionIndex} className="space-y-4">
                    <div className="flex items-center gap-3 pb-2 border-b">
                      <Icon className="w-5 h-5 text-gray-600" />
                      <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {section.fields.map((field) => (
                        <div key={field.name}>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {field.label}
                          </label>
                          {field.type === 'select' ? (
                            <select
                              name={field.name}
                              value={formData[field.name as keyof typeof formData]}
                              onChange={handleInputChange}
                              className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                              {field.options?.map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          ) : field.type === 'textarea' ? (
                            <textarea
                              name={field.name}
                              value={formData[field.name as keyof typeof formData]}
                              onChange={handleInputChange}
                              rows={3}
                              className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                          ) : (
                            <input
                              type={field.type}
                              name={field.name}
                              value={formData[field.name as keyof typeof formData]}
                              onChange={handleInputChange}
                              className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}

              {/* Text Areas */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-3 pb-2 border-b">
                  <User className="w-5 h-5 text-gray-600" />
                  תיאורים
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">אודות</label>
                    <textarea
                      name="about_us"
                      value={formData.about_us}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">מה מחפש</label>
                    <textarea
                      name="looking_for"
                      value={formData.looking_for}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end gap-3 pt-6 border-t mt-8">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                ביטול
              </button>
              <button
                type="submit"
                disabled={actionLoading}
                className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {actionLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {actionLoading ? 'שומר...' : 'שמור שינויים'}
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
