import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Save } from 'lucide-react';
import { useAdmin } from '../hooks/useAdmin';

interface AdminEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  currentData: any;
  onUpdate: () => void;
}

export default function AdminEditModal({ isOpen, onClose, userId, currentData, onUpdate }: AdminEditModalProps) {
  const { updateUserProfile } = useAdmin();
  const [formData, setFormData] = useState({
    username: '',
    gender: '',
    birth_date: '',
    partner_birth_date: '',
    user_metadata: {
      profile_data: {}
    }
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && currentData) {
      setFormData({
        username: currentData.username || '',
        gender: currentData.gender || '',
        birth_date: currentData.birth_date || '',
        partner_birth_date: currentData.partner_birth_date || '',
        user_metadata: {
          profile_data: currentData.user_metadata?.profile_data || {}
        }
      });
    }
  }, [isOpen, currentData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('profile_')) {
      const fieldName = name.replace('profile_', '');
      setFormData(prev => ({
        ...prev,
        user_metadata: {
          ...prev.user_metadata,
          profile_data: {
            ...prev.user_metadata.profile_data,
            [fieldName]: value
          }
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await updateUserProfile(userId, formData);
      
      if (!error) {
        alert('פרופיל עודכן בהצלחה');
        onUpdate();
        onClose();
      } else {
        alert('שגיאה בעדכון הפרופיל');
      }
    } catch (error) {
      alert('שגיאה בעדכון הפרופיל');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">עריכת פרופיל - אדמין</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">שם משתמש</label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">מין</label>
              <select
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              >
                <option value="">בחר</option>
                <option value="male">גבר</option>
                <option value="female">אישה</option>
                <option value="couple">זוג</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">תאריך לידה</label>
              <input
                type="date"
                name="birth_date"
                value={formData.birth_date}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>

            {formData.gender === 'couple' && (
              <div>
                <label className="block text-sm font-medium text-gray-700">תאריך לידה בן/בת זוג</label>
                <input
                  type="date"
                  name="partner_birth_date"
                  value={formData.partner_birth_date}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>
            )}
          </div>

          {/* Profile Data */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">נתוני פרופיל</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">עיר</label>
                <input
                  type="text"
                  name="profile_city"
                  value={formData.user_metadata.profile_data.city || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">מקצוע</label>
                <input
                  type="text"
                  name="profile_profession"
                  value={formData.user_metadata.profile_data.profession || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">גובה</label>
                <input
                  type="number"
                  name="profile_height"
                  value={formData.user_metadata.profile_data.height || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">השכלה</label>
                <input
                  type="text"
                  name="profile_education"
                  value={formData.user_metadata.profile_data.education || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">אודות</label>
              <textarea
                name="profile_about"
                value={formData.user_metadata.profile_data.about || ''}
                onChange={handleInputChange}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              ביטול
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              {loading ? 'שומר...' : 'שמור שינויים'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
