import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Save } from 'lucide-react';
import { useAdmin } from '../hooks/useAdmin';

interface AdminEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  currentData: any;
  onUpdate: () => void;
}

export default function AdminEditModal({ isOpen, onClose, userId, currentData, onUpdate }: AdminEditModalProps) {
  const { updateUserProfile } = useAdmin();
  const [formData, setFormData] = useState({
    username: '',
    gender: '',
    birth_date: '',
    partner_birth_date: '',
    city: '',
    area: '',
    phone: '',
    user_metadata: {
      profile_data: {
        name: '',
        height: '',
        weight: '',
        profession: '',
        education: '',
        about: '',
        lookingFor: '',
        hairColor: '',
        eyeColor: '',
        bodyType: '',
        ethnicity: '',
        sexualPreference: '',
        swingingExperience: '',
        smokingHabits: '',
        drinkingHabits: '',
        maritalStatus: '',
        children: ''
      }
    }
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && currentData) {
      console.log('Loading current data:', currentData);
      setFormData({
        username: currentData.username || '',
        gender: currentData.gender || '',
        birth_date: currentData.birth_date || '',
        partner_birth_date: currentData.partner_birth_date || '',
        city: currentData.city || '',
        area: currentData.area || '',
        phone: currentData.phone || '',
        user_metadata: {
          profile_data: {
            name: currentData.name || '',
            height: currentData.height?.toString() || '',
            weight: currentData.weight?.toString() || '',
            profession: currentData.profession || '',
            education: currentData.education || '',
            about: currentData.about_us || '',
            lookingFor: currentData.looking_for || '',
            hairColor: currentData.hair_color || '',
            eyeColor: currentData.eye_color || '',
            bodyType: currentData.body_type || '',
            ethnicity: currentData.ethnicity || '',
            sexualPreference: currentData.sexual_preference || '',
            swingingExperience: currentData.swinging_experience || '',
            smokingHabits: currentData.smoking_habits || '',
            drinkingHabits: currentData.drinking_habits || '',
            maritalStatus: currentData.marital_status || '',
            children: currentData.children?.toString() || ''
          }
        }
      });
    }
  }, [isOpen, currentData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    console.log('Input change:', name, value);

    if (name.startsWith('profile_')) {
      const fieldName = name.replace('profile_', '');
      setFormData(prev => ({
        ...prev,
        user_metadata: {
          ...prev.user_metadata,
          profile_data: {
            ...prev.user_metadata.profile_data,
            [fieldName]: value
          }
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log('Submitting form data:', formData);

      const { error } = await updateUserProfile(userId, formData);

      if (!error) {
        alert('פרופיל עודכן בהצלחה');
        onUpdate();
        onClose();
      } else {
        console.error('Update error:', error);
        alert('שגיאה בעדכון הפרופיל: ' + (error.message || 'שגיאה לא ידועה'));
      }
    } catch (error) {
      console.error('Submit error:', error);
      alert('שגיאה בעדכון הפרופיל');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">עריכת פרופיל - אדמין</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">שם משתמש</label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">מין</label>
              <select
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              >
                <option value="">בחר</option>
                <option value="male">גבר</option>
                <option value="female">אישה</option>
                <option value="couple">זוג</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">תאריך לידה</label>
              <input
                type="date"
                name="birth_date"
                value={formData.birth_date}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>

            {formData.gender === 'couple' && (
              <div>
                <label className="block text-sm font-medium text-gray-700">תאריך לידה בן/בת זוג</label>
                <input
                  type="date"
                  name="partner_birth_date"
                  value={formData.partner_birth_date}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700">עיר</label>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">אזור</label>
              <input
                type="text"
                name="area"
                value={formData.area}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">טלפון</label>
              <input
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>
          </div>

          {/* Profile Data */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">נתוני פרופיל</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">שם</label>
                <input
                  type="text"
                  name="profile_name"
                  value={formData.user_metadata.profile_data.name || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">מקצוע</label>
                <input
                  type="text"
                  name="profile_profession"
                  value={formData.user_metadata.profile_data.profession || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">גובה (ס"מ)</label>
                <input
                  type="number"
                  name="profile_height"
                  value={formData.user_metadata.profile_data.height || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">משקל (ק"ג)</label>
                <input
                  type="number"
                  name="profile_weight"
                  value={formData.user_metadata.profile_data.weight || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">השכלה</label>
                <input
                  type="text"
                  name="profile_education"
                  value={formData.user_metadata.profile_data.education || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">צבע שיער</label>
                <input
                  type="text"
                  name="profile_hairColor"
                  value={formData.user_metadata.profile_data.hairColor || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">צבע עיניים</label>
                <input
                  type="text"
                  name="profile_eyeColor"
                  value={formData.user_metadata.profile_data.eyeColor || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">מבנה גוף</label>
                <input
                  type="text"
                  name="profile_bodyType"
                  value={formData.user_metadata.profile_data.bodyType || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">מוצא</label>
                <input
                  type="text"
                  name="profile_ethnicity"
                  value={formData.user_metadata.profile_data.ethnicity || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">העדפה מינית</label>
                <input
                  type="text"
                  name="profile_sexualPreference"
                  value={formData.user_metadata.profile_data.sexualPreference || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">ניסיון בסווינגינג</label>
                <input
                  type="text"
                  name="profile_swingingExperience"
                  value={formData.user_metadata.profile_data.swingingExperience || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">עישון</label>
                <input
                  type="text"
                  name="profile_smokingHabits"
                  value={formData.user_metadata.profile_data.smokingHabits || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">שתייה</label>
                <input
                  type="text"
                  name="profile_drinkingHabits"
                  value={formData.user_metadata.profile_data.drinkingHabits || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">מצב משפחתי</label>
                <input
                  type="text"
                  name="profile_maritalStatus"
                  value={formData.user_metadata.profile_data.maritalStatus || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">ילדים</label>
                <input
                  type="number"
                  name="profile_children"
                  value={formData.user_metadata.profile_data.children || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">אודות</label>
                <textarea
                  name="profile_about"
                  value={formData.user_metadata.profile_data.about || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">מה מחפש</label>
                <textarea
                  name="profile_lookingFor"
                  value={formData.user_metadata.profile_data.lookingFor || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              ביטול
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              {loading ? 'שומר...' : 'שמור שינויים'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
