import React from 'react';
import { motion } from 'framer-motion';
import { Gift } from 'lucide-react';
import FullWidthUserCard from './FullWidthUserCard';
import { Profile } from '../types/supabase';

interface GiftsReceivedPageProps {
  profiles: Profile[];
}

function GiftsReceivedPage({ profiles }: GiftsReceivedPageProps) {
  if (profiles.length === 0) {
    return (
      <div className="bg-white rounded-2xl shadow-sm p-6 text-center">
        <Gift className="w-12 h-12 text-purple-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">אין מתנות שקיבלת</h3>
        <p className="text-gray-600">כאשר משתמשים ישלחו לך מתנות, הם יופיעו כאן.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-sm p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <Gift className="w-6 h-6 text-purple-600" />
            <h2 className="text-xl font-semibold text-gray-900">מתנות שקיבלת</h2>
            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
              {profiles.length} מתנות
            </span>
          </div>
        </div>

        {/* Users List - Wider layout */}
        <div className="space-y-3 max-w-2xl mx-auto">
          {profiles.map((profile) => (
            <FullWidthUserCard key={profile.id} profile={profile} />
          ))}
        </div>
      </div>
    </div>
  );
}

export default GiftsReceivedPage;
