import React, { useState } from 'react';
import { Upload, Loader2, Trash2 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { usePhotos } from '../hooks/usePhotos';
import Header from './Header';

function SimpleProfilePage() {
  const { user } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { uploadPhoto, photos, deletePhoto } = usePhotos({ profile: user?.user_metadata });
  
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    setUploading(true);
    setError(null);
    
    try {
      const file = files[0];
      const { error: uploadError } = await uploadPhoto(file);
      
      if (uploadError) {
        console.error('Error uploading photo:', uploadError);
        setError('אירעה שגיאה בהעלאת התמונה. נסה שוב מאוחר יותר.');
      }
    } catch (err) {
      console.error('Error in upload handler:', err);
      setError('אירעה שגיאה לא צפויה. נסה שוב מאוחר יותר.');
    } finally {
      setUploading(false);
    }
  };
  
  const handleDeletePhoto = async (url: string) => {
    try {
      await deletePhoto(url);
    } catch (err) {
      console.error('Error deleting photo:', err);
      setError('אירעה שגיאה במחיקת התמונה. נסה שוב מאוחר יותר.');
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {error && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-2">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        </div>
      )}
      
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* Upload Button */}
          <div className="bg-gray-800 p-4 flex justify-center">
            <button
              onClick={() => document.getElementById('simple-photo-upload')?.click()}
              disabled={uploading}
              className="flex items-center gap-2 bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 transition-colors text-base border-4 border-yellow-300 animate-pulse shadow-lg disabled:opacity-50"
            >
              {uploading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  מעלה...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4" />
                  העלה תמונה לגלריה
                </>
              )}
            </button>
            <input
              id="simple-photo-upload"
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileUpload}
              disabled={uploading}
            />
          </div>
          
          {/* Photos Grid */}
          <div className="p-4">
            <h2 className="text-xl font-bold mb-4">הגלריה שלי</h2>
            
            {photos.length === 0 ? (
              <div className="bg-gray-100 p-8 rounded-lg text-center">
                <p className="text-gray-500 font-medium mb-2">אין תמונות בגלריה</p>
                <p className="text-gray-400 text-sm">העלה תמונות כדי לשפר את הפרופיל שלך</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {photos.map((photo) => (
                  <div key={photo} className="relative group">
                    <img 
                      src={photo} 
                      alt="תמונת גלריה" 
                      className="w-full h-40 object-cover rounded-lg"
                    />
                    <button
                      onClick={() => handleDeletePhoto(photo)}
                      className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full shadow-lg hover:bg-red-600 transition-colors border-4 border-yellow-300 opacity-0 group-hover:opacity-100"
                      title="מחק תמונה"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default SimpleProfilePage;
