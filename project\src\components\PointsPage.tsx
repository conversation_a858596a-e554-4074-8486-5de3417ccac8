import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Co<PERSON>,
  CreditCard,
  Check,
  AlertTriangle,
  Loader2,
  Sparkles,
  Gift,
  Zap,
  Plus,
  ArrowRight,
  ShoppingCart
} from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import { useUserPoints } from '../hooks/useUserPoints';
import Header from './Header';

interface PointsPackage {
  id: string;
  name: string;
  points: number;
  price: number;
  is_featured?: boolean;
  is_active: boolean;
}

function PointsPage() {
  const { user } = useAuth();
  const { points: userPoints, refreshPoints } = useUserPoints();
  const [packages, setPackages] = useState<PointsPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [purchaseSuccess, setPurchaseSuccess] = useState(false);
  const [purchasedPoints, setPurchasedPoints] = useState(0);

  useEffect(() => {
    fetchPackages();
    if (user?.id) {
      refreshPoints();
    }
  }, [user?.id]); // Remove refreshPoints from dependencies

  const fetchPackages = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('points_packages')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setPackages(data || []);
    } catch (err) {
      console.error('Error fetching points packages:', err);
      setError('אירעה שגיאה בטעינת חבילות הנקודות. אנא נסה שוב מאוחר יותר.');
    } finally {
      setLoading(false);
    }
  };



  const handleSelectPackage = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handlePurchase = async () => {
    if (!user?.id || !selectedPackage) return;

    try {
      setPaymentProcessing(true);
      setError(null);

      // In a real app, you would integrate with a payment processor here
      // For this demo, we'll simulate a successful payment

      const selectedPkg = packages.find(pkg => pkg.id === selectedPackage);
      if (!selectedPkg) {
        throw new Error('חבילה לא נמצאה');
      }

      // Add points to user's account
      const { data: userData, error: fetchError } = await supabase
        .from('profiles')
        .select('points')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      const currentPoints = userData?.points || 0;
      const newPoints = currentPoints + selectedPkg.points;

      // Record the transaction
      const { error: transactionError } = await supabase
        .from('points_transactions')
        .insert([{
          user_id: user.id,
          amount: selectedPkg.points,
          description: `רכישת ${selectedPkg.points} נקודות`,
          payment_id: `demo-${Date.now()}`, // In a real app, this would be the payment ID from your payment processor
          package_id: selectedPackage
        }]);

      if (transactionError) {
        throw transactionError;
      }

      // Refresh points to get updated value
      await refreshPoints();
      setPurchasedPoints(selectedPkg.points);
      setPurchaseSuccess(true);

      // Reset selected package
      setSelectedPackage(null);

    } catch (err) {
      console.error('Error purchasing points:', err);
      setError('אירעה שגיאה ברכישת הנקודות. אנא נסה שוב מאוחר יותר.');
    } finally {
      setPaymentProcessing(false);
    }
  };

  return (
    <div className="w-full">
      {/* Header */}
      <Header title="רכישת נקודות" />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-500 to-pink-500 text-white">
        <div className="max-w-7xl mx-auto px-4 py-16 md:py-20">
          <div className="flex flex-col items-center text-center mb-8">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <Coins className="w-16 h-16 md:w-20 md:h-20 text-yellow-300" />
            </motion.div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">רכישת נקודות</h1>
            <p className="text-lg max-w-2xl opacity-90">
              נקודות מאפשרות לך לשלוח מתנות למשתמשים אחרים ולהתבלט בקהילה
            </p>
          </div>

          {/* Current Points */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white/20 backdrop-blur-sm rounded-xl p-6 max-w-md mx-auto text-center"
          >
            <h2 className="text-xl font-semibold mb-2">יתרת הנקודות שלך</h2>
            <div className="text-4xl font-bold mb-2">{userPoints.toLocaleString()}</div>
            <p className="opacity-80">נקודות זמינות לשימוש</p>
          </motion.div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-12 h-12 text-pink-600 animate-spin" />
          </div>
        ) : error ? (
          <div className="bg-red-50 text-red-700 p-6 rounded-xl flex items-center gap-3 mb-8 max-w-2xl mx-auto">
            <AlertTriangle className="w-8 h-8 flex-shrink-0" />
            <span className="text-lg">{error}</span>
          </div>
        ) : purchaseSuccess ? (
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-green-50 rounded-2xl p-8 text-center max-w-2xl mx-auto mb-12"
          >
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Check className="w-10 h-10 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-green-800 mb-4">הרכישה הושלמה בהצלחה!</h2>
            <p className="text-green-700 text-lg mb-6">
              {purchasedPoints.toLocaleString()} נקודות נוספו לחשבונך. יתרתך הנוכחית היא {userPoints.toLocaleString()} נקודות.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setPurchaseSuccess(false)}
                className="flex items-center justify-center gap-2 bg-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-purple-700 transition-colors"
              >
                <ShoppingCart className="w-5 h-5" />
                <span>רכוש עוד נקודות</span>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'home' } }))}
                className="flex items-center justify-center gap-2 bg-white text-purple-600 border border-purple-600 px-6 py-3 rounded-xl font-medium hover:bg-purple-50 transition-colors"
              >
                <ArrowRight className="w-5 h-5" />
                <span>חזור לדף הבית</span>
              </motion.button>
            </div>
          </motion.div>
        ) : (
          <>
            {/* Available Packages */}
            <div className="mb-16">
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">בחר חבילת נקודות</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  ככל שתרכוש יותר נקודות, כך תקבל יותר ערך לכסף שלך. הנקודות אינן פגות תוקף.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                {packages.map((pkg) => (
                  <motion.div
                    key={pkg.id}
                    whileHover={{ y: -5 }}
                    className={`rounded-xl overflow-hidden shadow-lg transition-all duration-300 ${
                      selectedPackage === pkg.id
                        ? 'ring-4 ring-purple-500 transform scale-105'
                        : 'border border-gray-200'
                    } ${pkg.is_featured ? 'relative' : ''}`}
                  >
                    {pkg.is_featured && (
                      <div className="absolute top-0 right-0 bg-gradient-to-r from-yellow-400 to-amber-500 text-white px-4 py-1 text-sm font-medium">
                        הכי פופולרי
                      </div>
                    )}
                    <div className={`p-6 ${
                      pkg.points < 500 ? 'bg-gradient-to-r from-blue-500 to-cyan-500' :
                      pkg.points < 1000 ? 'bg-gradient-to-r from-purple-500 to-pink-500' :
                      'bg-gradient-to-r from-yellow-500 to-amber-500'
                    } text-white`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-xl font-bold mb-1">{pkg.name}</h3>
                          <div className="text-3xl font-bold">{pkg.points.toLocaleString()}</div>
                          <p className="opacity-90">נקודות</p>
                        </div>
                        <div className={`p-2 rounded-full ${
                          pkg.points < 500 ? 'bg-blue-400/30' :
                          pkg.points < 1000 ? 'bg-purple-400/30' :
                          'bg-yellow-400/30'
                        }`}>
                          <Coins className="w-6 h-6" />
                        </div>
                      </div>

                      <div className="mt-6 flex items-baseline">
                        <span className="text-3xl font-bold">₪{pkg.price}</span>
                        {pkg.points >= 500 && (
                          <span className="ml-2 bg-white/20 px-2 py-1 rounded text-xs">
                            חיסכון של {Math.round((1 - (pkg.price / pkg.points) / (packages[0].price / packages[0].points)) * 100)}%
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="p-6 bg-white">
                      <ul className="space-y-3 mb-6">
                        <li className="flex items-center gap-3 text-gray-700">
                          <span className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                            pkg.points < 500 ? 'bg-blue-100 text-blue-600' :
                            pkg.points < 1000 ? 'bg-purple-100 text-purple-600' :
                            'bg-amber-100 text-amber-600'
                          }`}>
                            <Check className="w-3 h-3" />
                          </span>
                          <span>שליחת מתנות למשתמשים</span>
                        </li>
                        <li className="flex items-center gap-3 text-gray-700">
                          <span className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                            pkg.points < 500 ? 'bg-blue-100 text-blue-600' :
                            pkg.points < 1000 ? 'bg-purple-100 text-purple-600' :
                            'bg-amber-100 text-amber-600'
                          }`}>
                            <Check className="w-3 h-3" />
                          </span>
                          <span>הבלטת הפרופיל שלך</span>
                        </li>
                        {pkg.points >= 500 && (
                          <li className="flex items-center gap-3 text-gray-700">
                            <span className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                              pkg.points < 1000 ? 'bg-purple-100 text-purple-600' :
                              'bg-amber-100 text-amber-600'
                            }`}>
                              <Check className="w-3 h-3" />
                            </span>
                            <span>נקודות בונוס {pkg.points >= 1000 ? 'מוגדלות' : ''}</span>
                          </li>
                        )}
                      </ul>

                      <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => handleSelectPackage(pkg.id)}
                        className={`w-full py-3 rounded-xl font-medium transition-all duration-300 ${
                          selectedPackage === pkg.id
                            ? pkg.points < 500 ? 'bg-blue-600 text-white' :
                              pkg.points < 1000 ? 'bg-purple-600 text-white' :
                              'bg-amber-600 text-white'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {selectedPackage === pkg.id ? 'נבחר ✓' : 'בחר חבילה'}
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Purchase Button */}
              {selectedPackage && (
                <div className="flex justify-center">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handlePurchase}
                    disabled={paymentProcessing}
                    className="flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-10 py-4 rounded-xl font-medium text-lg shadow-xl hover:from-purple-700 hover:to-pink-700 transition-colors disabled:opacity-70"
                  >
                    {paymentProcessing ? (
                      <>
                        <Loader2 className="w-6 h-6 animate-spin" />
                        <span>מעבד תשלום...</span>
                      </>
                    ) : (
                      <>
                        <CreditCard className="w-6 h-6" />
                        <span>רכוש נקודות עכשיו</span>
                      </>
                    )}
                  </motion.button>
                </div>
              )}
            </div>

            {/* How to use points */}
            <div className="max-w-4xl mx-auto bg-gray-50 rounded-2xl p-8 mb-12">
              <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
                <Sparkles className="w-6 h-6 text-purple-500" />
                <span>איך להשתמש בנקודות?</span>
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                    <Gift className="w-6 h-6 text-pink-600" />
                  </div>
                  <h4 className="font-semibold text-lg mb-2">שליחת מתנות</h4>
                  <p className="text-gray-600">שלח מתנות וירטואליות למשתמשים אחרים כדי להביע עניין ולהתבלט.</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <Zap className="w-6 h-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold text-lg mb-2">הבלטת פרופיל</h4>
                  <p className="text-gray-600">השתמש בנקודות כדי להבליט את הפרופיל שלך בתוצאות החיפוש.</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Plus className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-lg mb-2">פיצ'רים מיוחדים</h4>
                  <p className="text-gray-600">שדרג את החוויה שלך עם פיצ'רים מיוחדים הזמינים רק למשתמשים עם נקודות.</p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default PointsPage;
