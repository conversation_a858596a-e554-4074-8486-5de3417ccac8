-- Fix create_chat_between_users function
-- This migration fixes the create_chat_between_users function to properly check for existing chats

-- Drop the existing function
DROP FUNCTION IF EXISTS create_chat_between_users(UUID, UUID);

-- Create an improved version of the function
CREATE OR REPLACE FUNCTION create_chat_between_users(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
  new_chat_id UUID;
  existing_chat_id UUID;
BEGIN
  -- Check if a chat already exists between these users
  SELECT c.id
  INTO existing_chat_id
  FROM chats c
  JOIN chat_participants cp1 ON c.id = cp1.chat_id AND cp1.user_id = user1_id
  JOIN chat_participants cp2 ON c.id = cp2.chat_id AND cp2.user_id = user2_id
  LIMIT 1;
  
  IF existing_chat_id IS NOT NULL THEN
    RETURN existing_chat_id;
  END IF;
  
  -- Create a new chat
  INSERT INTO chats (created_at, updated_at)
  VALUES (now(), now())
  RETURNING id INTO new_chat_id;
  
  -- Add participants
  INSERT INTO chat_participants (chat_id, user_id)
  VALUES (new_chat_id, user1_id), (new_chat_id, user2_id);
  
  RETURN new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user can send messages
CREATE OR REPLACE FUNCTION can_user_send_messages(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_allowed BOOLEAN;
BEGIN
  -- Check if user is VIP
  SELECT is_vip
  INTO is_allowed
  FROM profiles
  WHERE id = user_id;
  
  RETURN COALESCE(is_allowed, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
