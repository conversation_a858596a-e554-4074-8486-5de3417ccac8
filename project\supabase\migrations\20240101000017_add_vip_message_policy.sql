-- Add VIP message policy
-- This migration adds a policy to allow only VIP users to send messages

-- Drop the policy if it exists
DROP POLICY IF EXISTS "VIP users can send messages" ON messages;

-- Create policy to allow VIP users to send messages
CREATE POLICY "VIP users can send messages"
ON messages
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
  AND sender_id = auth.uid()
  AND EXISTS (
    SELECT 1
    FROM profiles
    WHERE id = auth.uid()
    AND is_vip = TRUE
  )
);
