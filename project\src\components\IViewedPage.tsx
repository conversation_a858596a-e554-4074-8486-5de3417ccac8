import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Eye, Loader2, Clock, Search, Filter, ChevronDown, Users, MapPin, X, Info, Heart } from 'lucide-react';
import FullWidthUserCard from './FullWidthUserCard';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';

interface IViewedPageProps {
  profiles?: Profile[];
}

function IViewedPage({ profiles: initialProfiles }: IViewedPageProps) {
  const { user } = useAuth();
  const [profiles, setProfiles] = useState<Profile[]>(initialProfiles || []);
  const [loading, setLoading] = useState<boolean>(!initialProfiles);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCity, setSelectedCity] = useState('');
  const [showInfoCard, setShowInfoCard] = useState(true);

  // Create a ref to the component
  const pageRef = useRef<HTMLDivElement>(null);

  const cities = [...new Set(profiles.map(profile => profile.city))].sort();

  const filteredProfiles = profiles.filter(profile => {
    const matchesSearch = searchTerm === '' ||
      profile.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.city.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCity = selectedCity === '' || profile.city === selectedCity;

    return matchesSearch && matchesCity;
  });

  // Add a refresh handler
  const handleRefresh = useCallback(() => {
    console.log('IViewedPage received refresh event');
    if (user?.id) {
      fetchIViewedProfiles();
    }
  }, [user?.id]);

  useEffect(() => {
    if (initialProfiles) {
      setProfiles(initialProfiles);
    } else if (user?.id) {
      fetchIViewedProfiles();

      // Set up event listener for the refresh event on the component
      if (pageRef.current) {
        pageRef.current.addEventListener('refresh', handleRefresh);
      }

      // Set up multiple subscriptions for better reliability

      // Method 1: Subscribe to postgres changes on profile_views table
      const profileViewsSubscription = supabase
        .channel('profile_views_changes_i_viewed')
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profile_views',
            filter: `viewer_id=eq.${user.id}`
          },
          (payload) => {
            console.log('I viewed a profile (postgres_changes):', payload);
            fetchIViewedProfiles();
          }
        )
        .subscribe();

      console.log('Subscribed to profile_views_changes_i_viewed channel');

      // Method 2: Subscribe to general profile_views_changes channel
      const generalViewsSubscription = supabase
        .channel('general_profile_views_changes_i_viewed')
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profile_views'
          },
          (payload) => {
            console.log('Profile view changed (general):', payload);
            // Check if this view is relevant to this user
            if (payload.new && payload.new.viewer_id === user.id) {
              console.log('This user viewed someone, refreshing profiles');
              fetchIViewedProfiles();
            }
          }
        )
        .subscribe();

      console.log('Subscribed to general_profile_views_changes_i_viewed channel');

      // Method 3: Also subscribe to profiles table changes to get updated profile data
      const profilesSubscription = supabase
        .channel('profiles_changes_i_viewed')
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'profiles'
          },
          (payload) => {
            console.log('Profile updated (realtime):', payload);
            // Refresh the profiles to get updated data
            fetchIViewedProfiles();
          }
        )
        .subscribe();

      console.log('Subscribed to profiles_changes_i_viewed channel');

      // Method 4: Also listen for the custom event as a backup
      const handleProfileViewRecorded = (event: any) => {
        const { viewerId, viewedId } = event.detail;
        console.log('Profile view recorded (custom event):', viewerId, viewedId);

        // Only refresh if this user is the viewer
        if (viewerId === user.id) {
          console.log('This user viewed someone, refreshing profiles');
          fetchIViewedProfiles();
        }
      };

      // Method 5: Listen for force refresh event
      const handleForceRefresh = () => {
        console.log('Force refresh requested for I Viewed page');
        fetchIViewedProfiles();
      };

      window.addEventListener('profile-view-recorded', handleProfileViewRecorded);
      window.addEventListener('force-refresh-profile-views', handleForceRefresh);

      // Set up a periodic refresh as a last resort
      const periodicRefresh = setInterval(() => {
        console.log('Periodic refresh of I Viewed page');
        fetchIViewedProfiles();
      }, 30000); // Refresh every 30 seconds

      // Clean up the subscriptions
      return () => {
        supabase.removeChannel(profileViewsSubscription);
        supabase.removeChannel(generalViewsSubscription);
        supabase.removeChannel(profilesSubscription);
        window.removeEventListener('profile-view-recorded', handleProfileViewRecorded);
        window.removeEventListener('force-refresh-profile-views', handleForceRefresh);
        clearInterval(periodicRefresh);

        // Remove the refresh event listener
        if (pageRef.current) {
          pageRef.current.removeEventListener('refresh', handleRefresh);
        }
      };
    }
  }, [user?.id, initialProfiles]);

  const fetchIViewedProfiles = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      console.log('Fetching profiles I viewed for user:', user.id);

      // Debug: First check if there are any profile views in the database
      try {
        const { data: directData, error: directError } = await supabase
          .from('profile_views')
          .select('viewed_id, created_at')
          .eq('viewer_id', user.id)
          .order('created_at', { ascending: false });

        if (directError) {
          console.error('Error with direct query:', directError);
        } else {
          console.log('Direct query results:', directData?.length || 0, 'profile views found');

          // If no profile views found, return empty array early
          if (!directData || directData.length === 0) {
            console.log('No profile views found, returning empty array');
            setProfiles([]);
            setLoading(false);
            return;
          }
        }
      } catch (directErr) {
        console.error('Exception in direct query:', directErr);
      }

      // Try multiple methods to get the profiles
      let data = null;

      // Method 1: Try RPC first
      try {
        console.log('Trying RPC method');
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_profiles_i_viewed',
          { user_id: user.id }
        );

        if (rpcError) {
          console.error('Error fetching profiles I viewed with RPC:', rpcError);
          throw rpcError;
        }

        data = rpcData;
        console.log('RPC successful, found', data?.length || 0, 'profiles');
      } catch (rpcErr) {
        console.error('RPC failed:', rpcErr);

        // Method 2: Fallback to direct query
        try {
          console.log('Trying direct query method');
          const { data: viewedData, error: viewedError } = await supabase
            .from('profile_views')
            .select('viewed_id, created_at')
            .eq('viewer_id', user.id)
            .order('created_at', { ascending: false });

          if (viewedError) {
            console.error('Error fetching viewed profiles:', viewedError);
            throw viewedError;
          }

          if (!viewedData || viewedData.length === 0) {
            console.log('No viewed profiles found');
            setProfiles([]);
            setLoading(false);
            return;
          }

          const viewedIds = viewedData.map(item => item.viewed_id);
          console.log('Viewed IDs:', viewedIds);

          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('*')
            .in('id', viewedIds);

          if (profilesError) {
            console.error('Error fetching profiles:', profilesError);
            throw profilesError;
          }

          // Add view_date to each profile
          data = profilesData.map(profile => {
            const viewData = viewedData.find(v => v.viewed_id === profile.id);
            return {
              ...profile,
              view_date: viewData ? viewData.created_at : null
            };
          });

          console.log('Direct query successful, found', data?.length || 0, 'profiles');
        } catch (directQueryErr) {
          console.error('Direct query failed:', directQueryErr);
          setError('אירעה שגיאה בטעינת הפרופילים. אנא נסה שוב מאוחר יותר.');
          setLoading(false);
          return;
        }
      }

      // If we still have no data, show error
      if (!data || data.length === 0) {
        console.log('No profiles found');
        setProfiles([]);
        setLoading(false);
        return;
      }

      console.log('Processing profiles with photos');
      const profilesWithPhotos = await Promise.all((data || []).map(async (profile) => {
        try {
          if (profile.user_metadata?.profile_image_url) {
            return { ...profile, photos: [profile.user_metadata.profile_image_url] };
          }

          const { data: photos, error: photosError } = await supabase.storage
            .from('photos')
            .list(profile.id + '/', {
              limit: 1,
              sortBy: { column: 'name', order: 'asc' },
            });

          if (photosError) {
            console.error('Error fetching photos for profile:', photosError);
            return { ...profile, photos: [] };
          }

          if (!photos || photos.length === 0) {
            return { ...profile, photos: [] };
          }

          const { data: { publicUrl } } = supabase.storage
            .from('photos')
            .getPublicUrl(`${profile.id}/${photos[0].name}`);

          return { ...profile, photos: [publicUrl] };
        } catch (error) {
          console.error('Error fetching photos for profile:', error);
          return { ...profile, photos: [] };
        }
      }));

      console.log('Setting profiles:', profilesWithPhotos.length);
      setProfiles(profilesWithPhotos);
    } catch (err) {
      console.error('Error fetching profiles I viewed:', err);
      setError('אירעה שגיאה לא צפויה. אנא נסה שוב מאוחר יותר.');
    } finally {
      setLoading(false);
    }
  };
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-2xl mx-auto">
        <div className="bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <Loader2 className="w-10 h-10 text-blue-500 animate-spin" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">טוען משתמשים...</h3>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">אנא המתן בזמן שאנו טוענים את המשתמשים שצפית בפרופיל שלהם.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-2xl mx-auto">
        <div className="bg-red-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <Eye className="w-10 h-10 text-red-500" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">שגיאה בטעינת המשתמשים</h3>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">{error}</p>
      </div>
    );
  }

  if (profiles.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-2xl mx-auto">
        <div className="bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <Eye className="w-10 h-10 text-blue-500" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">אין משתמשים שצפית בהם</h3>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">כאשר תצפה בפרופילים של משתמשים, הם יופיעו כאן. גלה משתמשים חדשים בדף הבית!</p>
      </div>
    );
  }

  return (
    <div ref={pageRef} id="i-viewed-page" className="space-y-6 max-w-6xl mx-auto">
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
        <div className="absolute inset-0 bg-pattern opacity-10"></div>

        <div className="relative z-10 p-8 md:p-10">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">צפיתי בהם</h1>
              <p className="text-blue-100 text-sm md:text-base max-w-xl">
                כאן תוכל לראות את כל המשתמשים שצפית בפרופיל שלהם לאחרונה.
              </p>
            </div>

            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center">
              <div className="text-3xl font-bold mb-1">{profiles.length}</div>
              <div className="text-sm">משתמשים שצפית בהם</div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path fill="#ffffff" fillOpacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
          </svg>
        </div>
      </div>



      <div className="bg-white rounded-xl shadow-md p-5 border border-gray-100">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="חיפוש לפי שם או עיר..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-200 rounded-xl focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center gap-2 px-4 py-3 bg-blue-50 text-blue-700 rounded-xl border border-blue-200 hover:bg-blue-100 transition-colors"
          >
            <Filter className="w-5 h-5" />
            <span>סינון</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ blockSize: 0, opacity: 0 }}
              animate={{ blockSize: 'auto', opacity: 1 }}
              exit={{ blockSize: 0, opacity: 0 }}
              className="overflow-hidden mt-4"
            >
              <div className="pt-4 border-t border-gray-100">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">עיר</label>
                  <select
                    value={selectedCity}
                    onChange={(e) => setSelectedCity(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="">כל הערים</option>
                    {cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="bg-blue-100 rounded-full p-1.5">
            <Users className="w-4 h-4 text-blue-700" />
          </div>
          <span className="text-gray-700">
            <span className="font-semibold">{filteredProfiles.length}</span> משתמשים שצפית בהם
          </span>
        </div>

        <div className="text-sm text-gray-500 flex items-center gap-1">
          <Clock className="w-4 h-4" />
          <span>עודכן: {new Date().toLocaleDateString('he-IL')}</span>
        </div>
      </div>

      <div className="space-y-4">
        {filteredProfiles.length > 0 ? (
          filteredProfiles.map((profile) => (
            <FullWidthUserCard key={profile.id} profile={profile} />
          ))
        ) : (
          <div className="bg-gray-50 rounded-xl p-8 text-center">
            <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">לא נמצאו תוצאות</h3>
            <p className="text-gray-500">נסה לשנות את הגדרות החיפוש או הסינון שלך</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default IViewedPage;
