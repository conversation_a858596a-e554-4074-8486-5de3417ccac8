-- Drop existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- Create or replace function to handle user creation
CREATE OR REPLACE FUNCTION handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    username,
    gender,
    birth_date,
    partner_birth_date,
    city,
    area,
    phone
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.raw_user_meta_data->>'gender',
    (new.raw_user_meta_data->>'birth_date')::date,
    CASE 
      WHEN new.raw_user_meta_data->>'partner_birth_date' IS NOT NULL 
      THEN (new.raw_user_meta_data->>'partner_birth_date')::date
      ELSE NULL
    END,
    new.raw_user_meta_data->>'city',
    new.raw_user_meta_data->>'area',
    new.raw_user_meta_data->>'phone'
  );
  RETUR<PERSON> new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create new trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();