-- Add trigger for profile views to enable realtime updates
-- This migration adds a trigger that will notify clients when a profile view is added or updated

-- First, drop the existing trigger if it exists
DROP TRIGGER IF EXISTS on_profile_view_change ON profile_views;
DROP FUNCTION IF EXISTS refresh_profile_views();

-- Create a function that will be called by the trigger
CREATE OR REPLACE FUNCTION refresh_profile_views()
RETURNS TRIGGER AS $$
BEGIN
  -- Notify clients about the change
  PERFORM pg_notify(
    'profile_views_changes',
    json_build_object(
      'table', 'profile_views',
      'type', TG_OP,
      'id', NEW.id,
      'viewer_id', NEW.viewer_id,
      'viewed_id', NEW.viewed_id,
      'created_at', NEW.created_at,
      'record', row_to_json(NEW)
    )::text
  );
  
  -- Also send specific notifications for the viewer and viewed users
  PERFORM pg_notify(
    'i_viewed_changes',
    json_build_object(
      'table', 'profile_views',
      'type', TG_OP,
      'id', NEW.id,
      'viewer_id', NEW.viewer_id,
      'viewed_id', NEW.viewed_id,
      'created_at', NEW.created_at,
      'record', row_to_json(NEW)
    )::text
  );
  
  PERFORM pg_notify(
    'viewed_me_changes',
    json_build_object(
      'table', 'profile_views',
      'type', TG_OP,
      'id', NEW.id,
      'viewer_id', NEW.viewer_id,
      'viewed_id', NEW.viewed_id,
      'created_at', NEW.created_at,
      'record', row_to_json(NEW)
    )::text
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER on_profile_view_change
AFTER INSERT OR UPDATE ON profile_views
FOR EACH ROW
EXECUTE FUNCTION refresh_profile_views();

-- Modify the record_profile_view function to ensure it triggers the notification
CREATE OR REPLACE FUNCTION record_profile_view(viewer uuid, viewed uuid)
RETURNS void AS $$
DECLARE
  view_id uuid;
BEGIN
  -- Don't record if viewing own profile
  IF viewer = viewed THEN
    RETURN;
  END IF;

  -- Insert or update the profile view
  -- The ON CONFLICT clause ensures we don't get duplicate entries
  -- and updates the timestamp when a user views the same profile again
  INSERT INTO profile_views (viewer_id, viewed_id, created_at)
  VALUES (viewer, viewed, now())
  ON CONFLICT (viewer_id, viewed_id)
  DO UPDATE SET created_at = now()
  RETURNING id INTO view_id;
  
  -- Force a notification by explicitly updating the record
  -- This ensures the trigger is fired even on conflict
  UPDATE profile_views
  SET created_at = now()
  WHERE id = view_id;
  
  -- Log the view for debugging
  RAISE NOTICE 'Profile view recorded and notified: viewer % viewed % (id: %)', viewer, viewed, view_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
