import { useState } from 'react';
import { Profile } from '../types/supabase';

export function useNavigate() {
  const [currentPage, setCurrentPage] = useState<'home' | 'profile' | 'admin' | 'vip' | 'points'>('home');

  const navigateToHome = () => {
    setCurrentPage('home');
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'home' } }));
  };

  const navigateToProfile = (profile?: Profile | null) => {
    setCurrentPage('profile');
    window.dispatchEvent(new CustomEvent('navigation', {
      detail: {
        page: 'profile',
        // If profile is null or undefined, we want to view the current user's profile
        profile: profile === null ? null : profile
      }
    }));
  };

  const navigateToAdmin = () => {
    setCurrentPage('admin');
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'admin' } }));
  };

  const navigateToVip = () => {
    setCurrentPage('vip');
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'vip' } }));
  };

  const navigateToPoints = () => {
    setCurrentPage('points');
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'points' } }));
  };

  const navigateToChat = (profileId?: string) => {
    // Navigate to home page with messages tab active
    setCurrentPage('home');

    // If profileId is provided, we'll create a chat with this user
    if (profileId) {
      // Store the profileId in sessionStorage to be used when the home page loads
      sessionStorage.setItem('chat_with_profile_id', profileId);
    }

    // Navigate to home page with messages tab
    window.location.href = '/?tab=הודעות';
  };

  return {
    currentPage,
    navigateToHome,
    navigateToProfile,
    navigateToAdmin,
    navigateToVip,
    navigateToPoints,
    navigateToChat
  };
}