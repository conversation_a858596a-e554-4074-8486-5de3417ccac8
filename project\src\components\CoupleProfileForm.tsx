import React, { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';

interface ProfileData {
  // Partner 1
  name1: string;
  height1: string;
  weight1: string;
  hairColor1: string;
  hairStyle1: string;
  eyeColor1: string;
  bodyType1: string;
  birthCountry1: string;
  ethnicity1: string;
  sexualPreference1: string;
  swingingExperience1: string;
  smokingHabits1: string;
  drinkingHabits1: string;
  area1: string;
  
  // Partner 2
  name2: string;
  height2: string;
  weight2: string;
  hairColor2: string;
  hairStyle2: string;
  eyeColor2: string;
  bodyType2: string;
  birthCountry2: string;
  ethnicity2: string;
  sexualPreference2: string;
  swingingExperience2: string;
  smokingHabits2: string;
  drinkingHabits2: string;
  area2: string;
  
  // Shared fields
  maritalStatus: string;
  children: string;
  aboutUs: string;
  lookingFor: string;
  seekingGender: string[];
  meetingTimes: string[];
}

interface CoupleProfileFormProps {
  onNext: (data: ProfileData) => void;
  onBack: () => void;
  initialData?: Partial<ProfileData>;
}

function CoupleProfileForm({ onNext, onBack, initialData = {} }: CoupleProfileFormProps) {
  const [profileData, setProfileData] = useState<ProfileData>({
    // Partner 1
    name1: initialData.name1 || '',
    height1: initialData.height1 || '',
    weight1: initialData.weight1 || '',
    hairColor1: initialData.hairColor1 || '',
    hairStyle1: initialData.hairStyle1 || '',
    eyeColor1: initialData.eyeColor1 || '',
    bodyType1: initialData.bodyType1 || '',
    birthCountry1: initialData.birthCountry1 || 'ישראל',
    ethnicity1: initialData.ethnicity1 || '',
    sexualPreference1: initialData.sexualPreference1 || '',
    swingingExperience1: initialData.swingingExperience1 || '',
    smokingHabits1: initialData.smokingHabits1 || '',
    drinkingHabits1: initialData.drinkingHabits1 || '',
    area1: initialData.area1 || '',
    
    // Partner 2
    name2: initialData.name2 || '',
    height2: initialData.height2 || '',
    weight2: initialData.weight2 || '',
    hairColor2: initialData.hairColor2 || '',
    hairStyle2: initialData.hairStyle2 || '',
    eyeColor2: initialData.eyeColor2 || '',
    bodyType2: initialData.bodyType2 || '',
    birthCountry2: initialData.birthCountry2 || 'ישראל',
    ethnicity2: initialData.ethnicity2 || '',
    sexualPreference2: initialData.sexualPreference2 || '',
    swingingExperience2: initialData.swingingExperience2 || '',
    smokingHabits2: initialData.smokingHabits2 || '',
    drinkingHabits2: initialData.drinkingHabits2 || '',
    area2: initialData.area2 || '',

    // Shared fields
    maritalStatus: initialData.maritalStatus || '',
    children: initialData.children || '',
    aboutUs: initialData.aboutUs || '',
    lookingFor: initialData.lookingFor || '',
    seekingGender: initialData.seekingGender || [],
    meetingTimes: initialData.meetingTimes || []
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value,
      // Reset children if marital status is single
      ...(name === 'maritalStatus' && value === 'single' ? { children: '' } : {})
    }));
  };

  const handleCheckboxChange = (field: 'seekingGender' | 'meetingTimes', value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext(profileData);
  };

  const renderPartnerFields = (partnerNum: 1 | 2) => {
    const suffix = partnerNum.toString();
    const title = partnerNum === 1 ? 'פרטי גבר' : 'פרטי אישה';
    const nameLabel = partnerNum === 1 ? 'שם גבר*' : 'שם אישה*';

    return (
      <div className="space-y-4 border-b pb-6 mb-6">
        <h2 className="text-xl font-semibold text-black">{title}</h2>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">{nameLabel}</label>
            <input
              type="text"
              name={`name${suffix}`}
              value={profileData[`name${suffix}` as keyof ProfileData] as string}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            />
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">גובה*</label>
            <select
              name={`height${suffix}`}
              value={profileData[`height${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              {Array.from({ length: 61 }, (_, i) => i + 140).map(height => (
                <option key={height} value={height}>{height} ס"מ</option>
              ))}
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">משקל</label>
            <select
              name={`weight${suffix}`}
              value={profileData[`weight${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
            >
              <option value="">בחר</option>
              {Array.from({ length: 151 }, (_, i) => i + 40).map(weight => (
                <option key={weight} value={weight}>{weight} ק"ג</option>
              ))}
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">צבע השער*</label>
            <select
              name={`hairColor${suffix}`}
              value={profileData[`hairColor${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="black">שחור</option>
              <option value="brown">חום</option>
              <option value="blonde">בלונדיני</option>
              <option value="red">ג'ינג'י</option>
              <option value="gray">אפור</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">תסרוקת*</label>
            <select
              name={`hairStyle${suffix}`}
              value={profileData[`hairStyle${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="long">ארוך</option>
              <option value="short">קצר</option>
              <option value="bald">קרח</option>
              <option value="medium">בינוני</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">צבע עיניים*</label>
            <select
              name={`eyeColor${suffix}`}
              value={profileData[`eyeColor${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="brown">חום</option>
              <option value="blue">כחול</option>
              <option value="green">ירוק</option>
              <option value="hazel">דבש</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">מבנה גוף*</label>
            <select
              name={`bodyType${suffix}`}
              value={profileData[`bodyType${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="slim">רזה</option>
              <option value="athletic">אתלטי</option>
              <option value="average">ממוצע</option>
              <option value="curvy">מלא</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">מוצא*</label>
            <select
              name={`ethnicity${suffix}`}
              value={profileData[`ethnicity${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="ashkenazi">אשכנזי</option>
              <option value="sephardi">ספרדי</option>
              <option value="mixed">מעורב</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">העדפה מינית*</label>
            <select
              name={`sexualPreference${suffix}`}
              value={profileData[`sexualPreference${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="straight">סטרייט</option>
              <option value="bisexual">דו מיני/ת</option>
              <option value="gay">הומו/לסבית</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">ניסיון בחילופי זוגות*</label>
            <select
              name={`swingingExperience${suffix}`}
              value={profileData[`swingingExperience${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="none">ללא ניסיון</option>
              <option value="some">מעט ניסיון</option>
              <option value="experienced">מנוסה</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">הרגלי עישון*</label>
            <select
              name={`smokingHabits${suffix}`}
              value={profileData[`smokingHabits${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="non-smoker">לא מעשן</option>
              <option value="occasional">מעשן לעיתים</option>
              <option value="regular">מעשן קבוע</option>
            </select>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-medium text-black mb-1">הרגלי שתיה*</label>
            <select
              name={`drinkingHabits${suffix}`}
              value={profileData[`drinkingHabits${suffix}` as keyof ProfileData]}
              onChange={handleInputChange}
              className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              required
            >
              <option value="">בחר</option>
              <option value="non-drinker">לא שותה</option>
              <option value="social">שותה חברתי</option>
              <option value="regular">שותה באופן קבוע</option>
            </select>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white rounded-2xl shadow-[0_20px_50px_rgba(255,192,203,0.3)] p-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="text-right mb-8">
            <h1 className="text-2xl font-bold text-black">פרטים נוספים</h1>
          </div>

          <div className="space-y-4 border-b pb-6 mb-6">
            <h2 className="text-xl font-semibold text-black">פרטים משותפים</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col">
                <label className="text-sm font-medium text-black mb-1">מצב משפחתי*</label>
                <select
                  name="maritalStatus"
                  value={profileData.maritalStatus}
                  onChange={handleInputChange}
                  className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  required
                >
                  <option value="">בחר</option>
                  <option value="single">רווק/ה</option>
                  <option value="married">נשוי/אה</option>
                  <option value="divorced">גרוש/ה</option>
                </select>
              </div>

              {profileData.maritalStatus !== 'single' && (
                <div className="flex flex-col">
                  <label className="text-sm font-medium text-black mb-1">מס' ילדים*</label>
                  <select
                    name="children"
                    value={profileData.children}
                    onChange={handleInputChange}
                    className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                    required
                  >
                    <option value="">בחר</option>
                    {Array.from({ length: 11 }, (_, i) => (
                      <option key={i} value={i}>{i}</option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </div>

          {renderPartnerFields(1)}
          {renderPartnerFields(2)}

          <div className="space-y-4">
            <div className="flex flex-col">
              <label className="text-sm font-medium text-black mb-1">מעט עלינו (לפחות 10 תווים)*</label>
              <textarea
                name="aboutUs"
                value={profileData.aboutUs}
                onChange={handleInputChange}
                className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500 min-h-[100px]"
                minLength={10}
                required
              />
            </div>

            <div className="flex flex-col">
              <label className="text-sm font-medium text-black mb-1">מה אני/אנחנו מחפש/ים (לפחות 10 תווים)*</label>
              <textarea
                name="lookingFor"
                value={profileData.lookingFor}
                onChange={handleInputChange}
                className="p-2 border border-gray-300 rounded-md focus:border-pink-500 focus:ring-1 focus:ring-pink-500 min-h-[100px]"
                minLength={10}
                required
              />
            </div>

            <div className="flex flex-col">
              <label className="text-sm font-medium text-black mb-1">את מי אני/אנחנו מחפש/ים*</label>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={profileData.seekingGender.includes('male')}
                    onChange={() => handleCheckboxChange('seekingGender', 'male')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>גבר</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={profileData.seekingGender.includes('couple')}
                    onChange={() => handleCheckboxChange('seekingGender', 'couple')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>זוג</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={profileData.seekingGender.includes('female')}
                    onChange={() => handleCheckboxChange('seekingGender', 'female')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>אישה</span>
                </label>
              </div>
            </div>

            <div className="flex flex-col">
              <label className="text-sm font-medium text-black mb-1">שעות מפגש מועדפות*</label>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={profileData.meetingTimes.includes('morning')}
                    onChange={() => handleCheckboxChange('meetingTimes', 'morning')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>בוקר</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={profileData.meetingTimes.includes('afternoon')}
                    onChange={() => handleCheckboxChange('meetingTimes', 'afternoon')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>צהריים</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={profileData.meetingTimes.includes('evening')}
                    onChange={() => handleCheckboxChange('meetingTimes', 'evening')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>ערב</span>
                </label>
              </div>
            </div>
          </div>

          <div className="flex justify-between gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={onBack}
              className="flex items-center justify-center gap-2 px-6 py-3 border-2 border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <ArrowRight className="w-5 h-5" />
              חזור
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              className="flex-1 bg-pink-600 text-white py-3 px-6 rounded-xl font-medium hover:bg-pink-700 transition-colors duration-300"
            >
              המשך
            </motion.button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default CoupleProfileForm;