/*
  # Create site assets storage bucket

  1. New Storage Bucket
    - Creates a new public bucket for storing site assets like logos
    - Sets appropriate security policies
  
  2. Security
    - Enable public access for viewing assets
    - Restrict uploads to authenticated users only
*/

-- Create the site-assets bucket if it doesn't exist
DO $$
BEGIN
  INSERT INTO storage.buckets (id, name, public)
  VALUES ('site-assets', 'site-assets', true)
  ON CONFLICT (id) DO NOTHING;
END $$;

-- Set up storage policy for public access
CREATE POLICY "Give public access to site assets"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'site-assets');

-- Set up storage policy for authenticated uploads
CREATE POLICY "Enable authenticated uploads to site assets"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'site-assets');

-- Set up storage policy for authenticated updates
CREATE POLICY "Enable authenticated updates to site assets"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'site-assets')
WITH CHECK (bucket_id = 'site-assets');