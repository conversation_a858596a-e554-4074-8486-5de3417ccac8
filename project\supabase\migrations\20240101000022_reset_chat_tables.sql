-- Reset chat tables
-- This migration resets the chat tables to fix issues

-- Drop all existing functions
DROP FUNCTION IF EXISTS create_chat_between_users(UUID, UUID);
DROP FUNCTION IF EXISTS get_messages_with_sender(UUI<PERSON>, UUID);
DROP FUNCTION IF EXISTS get_user_chats_with_details(UUID);
DROP FUNCTION IF EXISTS get_chat_participants_with_details(UUID, UUID);
DROP FUNCTION IF EXISTS get_user_profile_image(UUID);
DROP FUNCTION IF EXISTS get_user_metadata(UUID);
DROP FUNCTION IF EXISTS get_user_chats_simple(UUID);

-- Create a simpler function to create chat between users
CREATE OR REPLACE FUNCTION create_chat_between_users(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
  existing_chat_id UUID;
  new_chat_id UUID;
BEGIN
  -- Check if a chat already exists between these users
  SELECT cp1.chat_id INTO existing_chat_id
  FROM chat_participants cp1
  JOIN chat_participants cp2 ON cp1.chat_id = cp2.chat_id
  WHERE cp1.user_id = user1_id AND cp2.user_id = user2_id;

  IF existing_chat_id IS NOT NULL THEN
    RETURN existing_chat_id;
  END IF;

  -- Create a new chat
  INSERT INTO chats (created_at, updated_at)
  VALUES (NOW(), NOW())
  RETURNING id INTO new_chat_id;

  -- Add participants
  INSERT INTO chat_participants (chat_id, user_id)
  VALUES (new_chat_id, user1_id), (new_chat_id, user2_id);

  RETURN new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simple function to get messages for a chat
CREATE OR REPLACE FUNCTION get_chat_messages(p_chat_id UUID)
RETURNS TABLE (
  id UUID,
  chat_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  read BOOLEAN,
  sender_username TEXT,
  sender_profile_image TEXT,
  sender_is_online BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.chat_id,
    m.sender_id,
    m.content,
    m.created_at,
    COALESCE(m.read, FALSE),
    p.username,
    p.profile_image_url,
    p.is_online
  FROM
    messages m
  JOIN
    profiles p ON m.sender_id = p.id
  WHERE
    m.chat_id = p_chat_id
  ORDER BY
    m.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simple function to get user's chats
CREATE OR REPLACE FUNCTION get_user_chats(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  other_user_id UUID,
  other_username TEXT,
  other_profile_image TEXT,
  other_is_online BOOLEAN,
  last_message TEXT,
  last_message_time TIMESTAMPTZ,
  unread_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id AS chat_id,
    p.id AS other_user_id,
    p.username AS other_username,
    p.profile_image_url AS other_profile_image,
    p.is_online AS other_is_online,
    c.last_message,
    c.last_message_at AS last_message_time,
    COUNT(m.id) FILTER (WHERE m.sender_id != p_user_id AND (m.read IS NULL OR m.read = FALSE)) AS unread_count
  FROM
    chats c
  JOIN
    chat_participants cp1 ON c.id = cp1.chat_id AND cp1.user_id = p_user_id
  JOIN
    chat_participants cp2 ON c.id = cp2.chat_id AND cp2.user_id != p_user_id
  JOIN
    profiles p ON cp2.user_id = p.id
  LEFT JOIN
    messages m ON c.id = m.chat_id
  GROUP BY
    c.id, p.id, p.username, p.profile_image_url, p.is_online, c.last_message, c.last_message_at
  ORDER BY
    c.updated_at DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
