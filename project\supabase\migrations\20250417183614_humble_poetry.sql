/*
  # Add Stories Feature

  1. New Tables
    - `stories` table for storing story data
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `url` (text)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - Users can view all stories
      - Users can create their own stories
      - Stories auto-delete after 24 hours
*/

-- Create stories table
CREATE TABLE IF NOT EXISTS stories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  url text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE stories ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view all stories"
  ON stories
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create their own stories"
  ON stories
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create storage bucket for stories
INSERT INTO storage.buckets (id, name, public)
VALUES ('stories', 'stories', true)
ON CONFLICT (id) DO NOTHING;

-- Set bucket configuration
UPDATE storage.buckets
SET file_size_limit = 5242880, -- 5MB in bytes
    allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
WHERE id = 'stories';

-- Create storage policies
CREATE POLICY "Users can upload their own stories"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (
    bucket_id = 'stories' AND
    (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Anyone can view stories"
  ON storage.objects
  FOR SELECT
  TO authenticated
  USING (bucket_id = 'stories');

-- Function to clean up old stories
CREATE OR REPLACE FUNCTION cleanup_old_stories()
RETURNS void AS $$
DECLARE
  old_story RECORD;
BEGIN
  -- Find stories older than 24 hours
  FOR old_story IN
    SELECT id, url
    FROM stories
    WHERE created_at < now() - interval '24 hours'
  LOOP
    -- Delete the storage object
    BEGIN
      PERFORM storage.delete(
        'stories',
        (regexp_match(old_story.url, '/([^/]+)$'))[1]
      );
    EXCEPTION WHEN OTHERS THEN
      -- Log error but continue with story deletion
      RAISE NOTICE 'Failed to delete storage object for story %', old_story.id;
    END;
    
    -- Delete the story record
    DELETE FROM stories WHERE id = old_story.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;