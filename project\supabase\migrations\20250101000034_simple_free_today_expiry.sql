-- Simple migration to add expiry to free_today table
-- Add expires_at column if it doesn't exist
ALTER TABLE free_today ADD COLUMN IF NOT EXISTS expires_at timestamptz;

-- Update existing records to have expiry time (24 hours from created_at)
UPDATE free_today 
SET expires_at = created_at + INTERVAL '24 hours'
WHERE expires_at IS NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_free_today_expires_at ON free_today(expires_at);

-- Create simple function to add user to free_today with expiry
CREATE OR REPLACE FUNCTION add_user_to_free_today(p_user_id uuid)
RETURNS void AS $$
BEGIN
  -- Delete any existing expired entries for this user
  DELETE FROM free_today 
  WHERE user_id = p_user_id AND expires_at < now();
  
  -- Insert or update the user's free_today status
  INSERT INTO free_today (user_id, expires_at, created_at, updated_at)
  VALUES (p_user_id, now() + INTERVAL '24 hours', now(), now())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    expires_at = now() + INTERVAL '24 hours',
    updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Create simple function to remove user from free_today
CREATE OR REPLACE FUNCTION remove_user_from_free_today(p_user_id uuid)
RETURNS void AS $$
BEGIN
  DELETE FROM free_today WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Create simple function to check if user is free today (not expired)
CREATE OR REPLACE FUNCTION is_user_free_today(p_user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM free_today 
    WHERE user_id = p_user_id 
    AND expires_at > now()
  );
END;
$$ LANGUAGE plpgsql;
