import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { Users, Edit, MessageSquare, Gift, Ban, Settings, Calendar, Upload, Loader2, Flag, AlertTriangle, Check } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from '../hooks/useNavigate';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import { usePhotos } from '../hooks/usePhotos';
import PhotoGallery from './PhotoGallery';
import EditProfileModal from './EditProfileModal';
import SettingsModal from './SettingsModal';
import SendGiftDialog from './SendGiftDialog';
import Header from './Header';
import { formatAge } from '../utils/dateUtils';
import { RefreshCw } from 'lucide-react';

interface ProfilePageProps {
  profile?: Profile | null;
}

function ProfilePage({ profile: selectedProfile }: ProfilePageProps) {
  const { user } = useAuth();
  const { navigateToChat } = useNavigate();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isGiftDialogOpen, setIsGiftDialogOpen] = useState(false);
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
  const [isBlockConfirmOpen, setIsBlockConfirmOpen] = useState(false);
  const [isFreeToday, setIsFreeToday] = useState(false);
  const [isVip, setIsVip] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [loadingProfile, setLoadingProfile] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // State to store the latest profile data
  const [loadedProfile, setLoadedProfile] = useState<Profile | null>(null);
  const [profileData, setProfileData] = useState<any>({});
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());

  // Define isOwnProfile first
  const isOwnProfile = !selectedProfile;

  // Manual refresh function
  const handleManualRefresh = async () => {
    if (refreshing) return;

    setRefreshing(true);
    try {
      console.log('Starting manual refresh...');

      // For own profile, get the latest auth user data first
      if (isOwnProfile && user) {
        console.log('Refreshing own profile - getting latest auth data');
        const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error fetching current user data:', userError);
        } else if (currentUser) {
          console.log('Got latest user data:', currentUser.user_metadata);

          // Update profile data with the latest from auth
          if (currentUser.user_metadata?.profile_data) {
            setProfileData(currentUser.user_metadata.profile_data);
            console.log('Updated profileData with latest auth data:', currentUser.user_metadata.profile_data);
            console.log('Birth date in updated profileData:', currentUser.user_metadata.profile_data.birth_date);
          }

          // Update the loaded profile as well
          const updatedProfile = {
            ...profile,
            user_metadata: currentUser.user_metadata
          };
          setLoadedProfile(updatedProfile);
          setLastUpdateTime(new Date());
        }
      }

      // Note: We don't need to fetch from database since auth metadata is the source of truth
      // await fetchLatestProfileData(profile.id);

      // Refresh VIP status and free today status
      if (isOwnProfile) {
        await checkVipStatus();
        await checkFreeToday();
      } else {
        await checkProfileFreeToday(profile.id);
      }

      console.log('Profile refreshed manually successfully');
    } catch (error) {
      console.error('Error refreshing profile:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Create a reference to the profile, preferring loaded data
  const profile = loadedProfile || selectedProfile || {
    ...user?.user_metadata,
    id: user?.id // Make sure the ID is set correctly
  };

  // Then use them in hooks
  const { uploadPhoto } = usePhotos({ profile });

  // Function to fetch the latest profile data
  const fetchLatestProfileData = async (profileId: string) => {
    try {
      setLoadingProfile(true);
      console.log('Fetching latest profile data for:', profileId, 'isOwnProfile:', isOwnProfile);

      // First, get the latest auth user data which has the most up-to-date user_metadata
      let userData = null;

      // If viewing own profile, get current user data
      if (isOwnProfile && user) {
        console.log('Fetching current user auth data for own profile');
        const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error fetching current user data:', userError);
        } else if (currentUser) {
          console.log('Current user data fetched:', currentUser);
          console.log('Current user metadata:', currentUser.user_metadata);
          userData = currentUser;
        }
      }

      // Fetch the profile data from the profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profileId)
        .single();

      if (profileError) {
        console.error('Error fetching profile data:', profileError);
        return;
      }

      if (!profileData) {
        console.error('No profile data found');
        return;
      }

      console.log('Profile data fetched:', profileData);

      // Add user_metadata from auth user if available
      let data = profileData;
      if (userData) {
        data.user_metadata = userData.user_metadata;
        console.log('Added user_metadata from auth:', userData.user_metadata);
      }

      if (data) {
        // Make sure we have user_metadata
        if (!data.user_metadata) {
          data.user_metadata = {};
        }

        // Make sure we have profile_data in user_metadata
        if (!data.user_metadata.profile_data) {
          data.user_metadata.profile_data = {};
        }

        // If we have profile_data directly on the profile, copy it to user_metadata
        if (data.profile_data && Object.keys(data.profile_data).length > 0) {
          console.log('Copying profile_data to user_metadata.profile_data');
          data.user_metadata.profile_data = {
            ...data.user_metadata.profile_data,
            ...data.profile_data
          };
        }

        // If we have user data from auth, use its metadata (it's more up-to-date)
        if (userData && isOwnProfile) {
          console.log('Using user_metadata from auth user for own profile');

          // For own profile, prioritize the database data but merge with auth metadata
          const mergedUserMetadata = {
            ...userData.user_metadata,
            ...data.user_metadata,
            profile_data: {
              ...(userData.user_metadata?.profile_data || {}),
              ...(data.user_metadata?.profile_data || {})
            }
          };

          const mergedData = {
            ...data,
            ...userData.user_metadata, // Include basic info from auth
            user_metadata: mergedUserMetadata,
            // Ensure we have the basic profile fields
            username: data.username || userData.user_metadata?.username,
            city: data.city || userData.user_metadata?.city,
            area: data.area || userData.user_metadata?.area,
            birth_date: data.birth_date || userData.user_metadata?.birth_date,
            partner_birth_date: data.partner_birth_date || userData.user_metadata?.partner_birth_date,
            gender: data.gender || userData.user_metadata?.gender
          };

          setLoadedProfile(mergedData);

          // Merge profile_data with top-level metadata for couples
          const enhancedProfileData = {
            ...(mergedUserMetadata.profile_data || {}),
            // Add top-level metadata to profile_data for easier access
            birth_date: mergedUserMetadata.profile_data?.birth_date || userData.user_metadata?.birth_date,
            partner_birth_date: mergedUserMetadata.profile_data?.partner_birth_date || userData.user_metadata?.partner_birth_date
          };

          console.log('=== PROFILE DATA DEBUG ===');
          console.log('userData.user_metadata:', userData.user_metadata);
          console.log('mergedUserMetadata:', mergedUserMetadata);
          console.log('enhancedProfileData:', enhancedProfileData);
          console.log('birth_date sources:', {
            from_profile_data: mergedUserMetadata.profile_data?.birth_date,
            from_user_metadata: userData.user_metadata?.birth_date,
            final: enhancedProfileData.birth_date
          });
          console.log('partner_birth_date sources:', {
            from_profile_data: mergedUserMetadata.profile_data?.partner_birth_date,
            from_user_metadata: userData.user_metadata?.partner_birth_date,
            final: enhancedProfileData.partner_birth_date
          });

          setProfileData(enhancedProfileData);

          console.log('Merged profile data for own profile:', mergedUserMetadata.profile_data);
          console.log('Enhanced profile data with birth dates:', enhancedProfileData);
          console.log('Final merged profile object:', mergedData);
        } else {
          // For other users' profiles, use the database data
          console.log('Using profile data for other user:', data.user_metadata);

          setLoadedProfile(data);

          // Merge profile_data with top-level metadata for couples
          const enhancedProfileData = {
            ...(data.user_metadata?.profile_data || {}),
            // Add top-level metadata to profile_data for easier access
            birth_date: data.user_metadata?.profile_data?.birth_date || data.user_metadata?.birth_date,
            partner_birth_date: data.user_metadata?.profile_data?.partner_birth_date || data.user_metadata?.partner_birth_date
          };

          console.log('=== OTHER PROFILE DATA DEBUG ===');
          console.log('data.user_metadata:', data.user_metadata);
          console.log('enhancedProfileData:', enhancedProfileData);
          console.log('partner_birth_date sources:', {
            from_profile_data: data.user_metadata?.profile_data?.partner_birth_date,
            from_user_metadata: data.user_metadata?.partner_birth_date,
            final: enhancedProfileData.partner_birth_date
          });

          setProfileData(enhancedProfileData);
          console.log('Profile data set from profiles table:', data.user_metadata?.profile_data);
          console.log('Enhanced profile data with birth dates:', enhancedProfileData);
        }

        // Force update the profiles table with the latest user_metadata if needed
        if (userData && isOwnProfile) {
          // Create properly merged metadata to preserve all data
          const mergedUserMetadata = {
            ...data.user_metadata,
            profile_data: {
              ...(data.user_metadata?.profile_data || {}),
              ...(userData.user_metadata?.profile_data || {})
            }
          };

          // Only update if there are actual differences
          if (JSON.stringify(mergedUserMetadata) !== JSON.stringify(data.user_metadata)) {
            console.log('Updating profiles table with merged user_metadata');
            const { error: updateError } = await supabase
              .from('profiles')
              .update({
                user_metadata: mergedUserMetadata,
                updated_at: new Date().toISOString()
              })
              .eq('id', profileId);

            if (updateError) {
              console.error('Error updating profile with merged metadata:', updateError);
            } else {
              console.log('Successfully updated profiles table with merged metadata');
            }
          }
        }

        setLastUpdateTime(new Date());
      }
    } catch (err) {
      console.error('Error in fetchLatestProfileData:', err);
    } finally {
      setLoadingProfile(false);
    }
  };

  // Effect to fetch the latest profile data on mount and when profile ID changes
  useEffect(() => {
    if (!profile?.id) return;

    console.log('ProfilePage: Initial profile data:', profile);

    // Always fetch the latest profile data for own profile to ensure we have complete data
    if (isOwnProfile) {
      console.log('ProfilePage: Fetching complete profile data for own profile');
      fetchLatestProfileData(profile.id);
    } else {
      // For other profiles, check if we have complete profile data
      if (profile.user_metadata && profile.user_metadata.profile_data) {
        console.log('ProfilePage: Using provided profile data for other user');
        setLoadedProfile(profile);
        setProfileData(profile.user_metadata.profile_data || {});
      } else {
        console.log('ProfilePage: Fetching complete profile data for other user');
        fetchLatestProfileData(profile.id);
      }
    }
  }, [profile?.id, isOwnProfile]);

  // Effect to update profileData when user metadata changes
  useEffect(() => {
    if (isOwnProfile && user?.user_metadata?.profile_data) {
      console.log('User metadata changed, updating profileData:', user.user_metadata.profile_data);
      console.log('Birth date in user metadata:', user.user_metadata.profile_data.birth_date);
      setProfileData(user.user_metadata.profile_data);
      setLastUpdateTime(new Date());
    }
  }, [user?.user_metadata?.profile_data, isOwnProfile]);

  // Listen for profile update events
  useEffect(() => {
    const handleProfileUpdate = (event: CustomEvent) => {
      const { userId, profileData: newProfileData } = event.detail;

      if (isOwnProfile && userId === user?.id) {
        console.log('Profile update event received, updating profileData:', newProfileData);
        console.log('Birth date in new profile data:', newProfileData?.birth_date);
        console.log('Partner birth date in new profile data:', newProfileData?.partner_birth_date);
        setProfileData(newProfileData || {});
        setLastUpdateTime(new Date());
      }
    };

    window.addEventListener('profileUpdated', handleProfileUpdate as EventListener);

    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate as EventListener);
    };
  }, [isOwnProfile, user?.id]);

  // Effect to handle initial setup and profile view recording
  useEffect(() => {
    if (user?.id && isOwnProfile) {
      checkVipStatus();
      checkFreeToday();
    } else if (selectedProfile && user?.id) {
      // Check if the viewed profile is free today
      checkProfileFreeToday(selectedProfile.id);

      // Check if the profile is blocked
      checkIfBlocked(selectedProfile.id);

      // Record profile view
      recordProfileView(user.id, selectedProfile.id);
    }
  }, [user?.id, isOwnProfile, selectedProfile]);

  // Function to check if the profile is blocked
  const checkIfBlocked = async (profileId: string) => {
    if (!user?.id) return;

    try {
      const { data, error } = await supabase
        .from('blocked_users')
        .select('*')
        .eq('blocker_id', user.id)
        .eq('blocked_id', profileId)
        .maybeSingle();

      if (error) {
        if (error.code === '42P01') {
          // Table doesn't exist - skip blocking check for now
          console.log('Blocked users table not found, skipping block check');
          setIsBlocked(false);
        } else {
          console.error('Error checking if profile is blocked:', error);
        }
        return;
      }

      setIsBlocked(!!data);
    } catch (err) {
      console.error('Error checking if profile is blocked:', err);
      setIsBlocked(false);
    }
  };

  // Function to record profile view
  const recordProfileView = async (viewerId: string, viewedId: string) => {
    try {
      // Don't record if viewing own profile
      if (viewerId === viewedId) {
        console.log('Not recording view of own profile');
        return;
      }

      console.log(`Recording profile view: ${viewerId} viewed ${viewedId}`);

      // Use the new simplified record_profile_view function
      try {
        console.log('Calling record_profile_view RPC function');
        const { data: viewId, error: rpcError } = await supabase.rpc('record_profile_view', {
          viewer: viewerId,
          viewed: viewedId
        });

        if (rpcError) {
          console.error('Error recording profile view with RPC:', rpcError);
          console.error('Error details:', JSON.stringify(rpcError));

          // Try direct insert as fallback
          console.log('Attempting direct insert to profile_views table');
          const { data: insertData, error: insertError } = await supabase
            .from('profile_views')
            .upsert(
              {
                viewer_id: viewerId,
                viewed_id: viewedId,
                created_at: new Date().toISOString()
              },
              {
                onConflict: 'viewer_id,viewed_id',
                ignoreDuplicates: false
              }
            );

          if (insertError) {
            console.error('Error with direct profile view insert:', insertError);
            console.error('Error details:', JSON.stringify(insertError));
          } else {
            console.log('Direct insert successful');
          }
        } else {
          console.log('RPC record_profile_view successful, view ID:', viewId);
        }

        // Always dispatch events to update UI, even if there was an error
        // This ensures the UI stays responsive even if the database operation failed
        window.dispatchEvent(new CustomEvent('profile-view-recorded', {
          detail: {
            viewerId,
            viewedId,
            timestamp: new Date().toISOString()
          }
        }));

        window.dispatchEvent(new CustomEvent('force-refresh-profile-views'));

        // Force refresh the profile views pages
        if (typeof window !== 'undefined') {
          const viewedMePage = document.getElementById('viewed-me-page');
          const iViewedPage = document.getElementById('i-viewed-page');

          if (viewedMePage) {
            console.log('Forcing refresh of Viewed Me page');
            viewedMePage.dispatchEvent(new Event('refresh'));
          }

          if (iViewedPage) {
            console.log('Forcing refresh of I Viewed page');
            iViewedPage.dispatchEvent(new Event('refresh'));
          }
        }
      } catch (err) {
        console.error('Exception in profile view recording:', err);

        // Still dispatch events to update UI as a last resort
        window.dispatchEvent(new CustomEvent('profile-view-recorded', {
          detail: {
            viewerId,
            viewedId,
            timestamp: new Date().toISOString()
          }
        }));

        window.dispatchEvent(new CustomEvent('force-refresh-profile-views'));
      }
    } catch (err) {
      console.error('Error recording profile view:', err);
    }
  };

  const checkProfileFreeToday = async (profileId: string) => {
    try {
      const { data, error: supabaseError } = await supabase
        .from('free_today')
        .select('id')
        .eq('user_id', profileId)
        .maybeSingle();

      if (supabaseError) {
        console.error('Error checking profile free today status:', supabaseError);
        return;
      }

      setIsFreeToday(!!data);
    } catch (err) {
      console.error('Error checking profile free today status:', err);
    }
  };

  const checkVipStatus = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const { data, error: supabaseError } = await supabase
        .from('profiles')
        .select('is_vip')
        .eq('id', user.id)
        .single();

      if (supabaseError) {
        console.error('Supabase error checking VIP status:', supabaseError);
        return;
      }

      setIsVip(data?.is_vip ?? false);
    } catch (err) {
      console.error('Error checking VIP status:', err);
    }
  };

  const checkFreeToday = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const { data, error: supabaseError } = await supabase
        .from('free_today')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (supabaseError) {
        console.error('Error checking free today status:', supabaseError);
        return;
      }

      setIsFreeToday(!!data);
    } catch (err) {
      console.error('Error checking free today status:', err);
    }
  };

  const handleFreeToday = async () => {
    if (!user?.id || !isVip) return;

    try {
      setError(null);
      if (isFreeToday) {
        const { error: deleteError } = await supabase
          .from('free_today')
          .delete()
          .eq('user_id', user.id);

        if (deleteError) {
          console.error('Error removing free today status:', deleteError);
          setError('נכשל בהסרת סטטוס פנוי היום. נסה שוב מאוחר יותר.');
          return;
        }
        setIsFreeToday(false);
      } else {
        const { error: insertError } = await supabase
          .from('free_today')
          .insert([{ user_id: user.id }]);

        if (insertError) {
          console.error('Error setting free today status:', insertError);
          setError('נכשל בהגדרת סטטוס פנוי היום. נסה שוב מאוחר יותר.');
          return;
        }
        setIsFreeToday(true);
      }
    } catch (err) {
      console.error('Error updating free today status:', err);
      setError('אירעה שגיאה לא צפויה. נסה שוב מאוחר יותר.');
    }
  };

  const getGenderIcon = () => {
    switch (profile?.gender) {
      case 'male':
        return <FontAwesomeIcon icon={faMars} className="text-3xl text-blue-600" />;
      case 'female':
        return <FontAwesomeIcon icon={faVenus} className="text-3xl text-pink-600" />;
      case 'couple':
        return <Users className="h-8 w-8 text-pink-600" />;
      default:
        return null;
    }
  };

  const handleChat = () => {
    if (!isOwnProfile && selectedProfile?.id) {
      // Navigate to chat with this user
      navigateToChat(selectedProfile.id);
    }
  };

  const handleGift = (e: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    setIsGiftDialogOpen(true);
  };

  const handleBlock = () => {
    // Open block confirmation dialog
    setIsBlockConfirmOpen(true);
  };

  const confirmBlock = async () => {
    if (!user?.id || !selectedProfile?.id) return;

    try {
      setError(null);
      setSuccess(null);

      // If already blocked, unblock the user
      if (isBlocked) {
        const { error } = await supabase
          .from('blocked_users')
          .delete()
          .eq('blocker_id', user.id)
          .eq('blocked_id', selectedProfile.id);

        if (error) {
          if (error.code === '42P01') {
            console.log('Blocked users table not found, cannot unblock');
            setError('פונקציית החסימה אינה זמינה כרגע');
            return;
          }
          console.error('Error unblocking user:', error);
          setError('אירעה שגיאה בהסרת החסימה. נסה שוב מאוחר יותר.');
          return;
        }

        setIsBlocked(false);
        setSuccess('המשתמש הוסר מרשימת החסומים בהצלחה.');
      }
      // Otherwise, block the user
      else {
        const { error } = await supabase
          .from('blocked_users')
          .insert([{
            blocker_id: user.id,
            blocked_id: selectedProfile.id,
            blocked_username: selectedProfile.username
          }]);

        if (error) {
          if (error.code === '42P01') {
            console.log('Blocked users table not found, cannot block');
            setError('פונקציית החסימה אינה זמינה כרגע');
            return;
          }
          console.error('Error blocking user:', error);
          setError('אירעה שגיאה בחסימת המשתמש. נסה שוב מאוחר יותר.');
          return;
        }

        setIsBlocked(true);
        setSuccess('המשתמש נחסם בהצלחה.');
      }
    } catch (err) {
      console.error('Error in block/unblock operation:', err);
      setError('אירעה שגיאה לא צפויה. נסה שוב מאוחר יותר.');
    } finally {
      setIsBlockConfirmOpen(false);
    }
  };

  const handleReport = () => {
    // Open report dialog
    setIsReportDialogOpen(true);
  };

  const submitReport = async (reason: string) => {
    if (!user?.id || !selectedProfile?.id || !reason) return;

    try {
      setError(null);
      setSuccess(null);

      const { error } = await supabase
        .from('reports')
        .insert([{
          reporter_id: user.id,
          reported_id: selectedProfile.id,
          reason: reason,
          status: 'pending'
        }]);

      if (error) {
        console.error('Error reporting profile:', error);
        setError('אירעה שגיאה בדיווח על הפרופיל. נסה שוב מאוחר יותר.');
        return;
      }

      setSuccess('הדיווח נשלח בהצלחה. תודה על עזרתך בשמירה על קהילה בטוחה.');
    } catch (err) {
      console.error('Error reporting profile:', err);
      setError('אירעה שגיאה לא צפויה. נסה שוב מאוחר יותר.');
    } finally {
      setIsReportDialogOpen(false);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    setError(null);

    try {
      const file = files[0];
      console.log('Uploading file:', file.name, file.size, file.type);

      const { error: uploadError } = await uploadPhoto(file);

      if (uploadError) {
        console.error('Error uploading photo:', uploadError);
        setError('אירעה שגיאה בהעלאת התמונה. נסה שוב מאוחר יותר.');
      } else {
        console.log('File uploaded successfully');

        // Fetch the latest profile data instead of reloading the page
        if (profile?.id) {
          await fetchLatestProfileData(profile.id);
          setSuccess('התמונה הועלתה בהצלחה');
        }
      }
    } catch (err) {
      console.error('Error in upload handler:', err);
      setError('אירעה שגיאה לא צפויה. נסה שוב מאוחר יותר.');
    } finally {
      setUploading(false);
    }
  };

  // Realtime subscription disabled to prevent interference with editing
  // Users can manually refresh if needed
  /*
  useEffect(() => {
    if (!profile?.id) return;

    console.log('Setting up realtime subscription for profile:', profile.id);

    const profileSubscription = supabase
      .channel(`profile-${profile.id}`)
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${profile.id}`
        },
        (payload) => {
          console.log('Profile updated in realtime:', payload);
          fetchLatestProfileData(profile.id);

          if (isOwnProfile) {
            checkVipStatus();
            checkFreeToday();
          } else {
            checkProfileFreeToday(profile.id);
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Unsubscribing from profile changes for:', profile.id);
      profileSubscription.unsubscribe();
    };
  }, [profile?.id, isOwnProfile]);
  */

  const getTranslatedValue = (value: string): string => {
    const translations: Record<string, string> = {
      // Gender
      male: 'גבר',
      female: 'אישה',
      couple: 'זוג',

      // Areas
      north: 'צפון',
      haifa: 'חיפה',
      jerusalem: 'ירושלים',
      'tel-aviv': 'תל אביב',
      'judea-samaria': 'יהודה ושומרון',
      'sharon-rehovot': 'השרון רחובות',
      ashdod: 'אשדוד',
      ashkelon: 'אשקלון',
      'beer-sheva': 'באר שבע',
      eilat: 'אילת',

      // Hair colors
      black: 'שחור',
      brown: 'חום',
      blonde: 'בלונדיני',
      red: 'ג\'ינג\'י',
      gray: 'אפור',

      // Hair styles
      long: 'ארוך',
      short: 'קצר',
      bald: 'קרח',
      medium: 'בינוני',

      // Eye colors
      blue: 'כחול',
      green: 'ירוק',
      hazel: 'דבש',

      // Body types
      slim: 'רזה',
      athletic: 'אתלטי',
      average: 'ממוצע',
      curvy: 'מלא',

      // Marital status
      single: 'רווק/ה',
      married: 'נשוי/אה',
      divorced: 'גרוש/ה',

      // Ethnicity
      ashkenazi: 'אשכנזי',
      sephardi: 'ספרדי',
      mixed: 'מעורב',

      // Sexual preferences
      straight: 'סטרייט',
      bisexual: 'דו מיני/ת',
      gay: 'הומו',
      lesbian: 'לסבית',

      // Experience
      none: 'ללא ניסיון',
      some: 'מעט ניסיון',
      experienced: 'מנוסה',

      // Smoking habits
      'non-smoker': 'לא מעשן',
      occasional: 'מעשן לעיתים',
      regular: 'מעשן קבוע',

      // Drinking habits
      'non-drinker': 'לא שותה',
      social: 'שותה חברתי',
      regular: 'שותה באופן קבוע',

      // Meeting times
      morning: 'בוקר',
      afternoon: 'צהריים',
      evening: 'ערב'
    };

    return translations[value] || value;
  };

  const renderDetailRow = (label: string, value: string | number | undefined) => {
    if (!value) return null;
    return (
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-3 border-b border-gray-100 gap-1 sm:gap-0">
        <span className="font-medium text-gray-700 text-sm sm:text-base">{label}</span>
        <span className="text-gray-900 text-sm sm:text-base break-words">{value}</span>
      </div>
    );
  };

  if (!profile || loadingProfile) {
    console.log('ProfilePage: showing loading state, profile:', !!profile, 'loadingProfile:', loadingProfile);
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto mb-4"></div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">טוען פרופיל...</h2>
        <p className="text-gray-600">אנא המתן בזמן שאנו טוענים את הפרופיל.</p>
      </div>
    </div>;
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      {error && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-2">
          <div className="bg-red-50 border border-red-200 text-red-700 px-3 sm:px-4 py-3 rounded-lg flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
            <span className="text-sm sm:text-base">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-2">
          <div className="bg-green-50 border border-green-200 text-green-700 px-3 sm:px-4 py-3 rounded-lg flex items-center gap-2">
            <Check className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
            <span className="text-sm sm:text-base">{success}</span>
          </div>
        </div>
      )}
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* Photo Gallery */}
          <PhotoGallery profile={profile} />

          <div className="p-4 sm:p-8 bg-white">
            {/* Header with Actions - Mobile Responsive */}
            <div className="mb-6 sm:mb-8">
              {/* Title and Gender Info */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <div className="flex items-center gap-3 mb-4 sm:mb-0">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                    {isOwnProfile ? 'הפרופיל שלי' : profile.username}
                  </h1>
                  <div className="flex items-center gap-2">
                    {getGenderIcon()}
                    <span className="text-lg sm:text-xl font-medium text-gray-700">
                      {profile.gender ? getTranslatedValue(profile.gender) : ''}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons - Mobile Responsive */}
              {isOwnProfile ? (
                <div className="flex flex-col sm:flex-row gap-3">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      setIsEditModalOpen(true);
                      setIsEditing(true);
                    }}
                    className="flex items-center justify-center gap-2 bg-pink-600 text-white px-4 py-3 rounded-lg hover:bg-pink-700 transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                    <span>ערוך פרופיל</span>
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleManualRefresh}
                    disabled={refreshing}
                    className="flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="רענן פרופיל"
                  >
                    <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                    <span>{refreshing ? 'מרענן...' : 'רענן'}</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setIsSettingsModalOpen(true)}
                    className="flex items-center justify-center gap-2 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    <span>הגדרות</span>
                  </motion.button>
                  {/* Free Today Button for Couple Users */}
                  {isVip && profile?.gender === 'couple' && (
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleFreeToday}
                      className={`flex items-center justify-center gap-2 px-4 py-3 rounded-lg transition-colors ${
                        isFreeToday
                          ? 'bg-amber-100 text-amber-800 border-2 border-amber-400'
                          : 'bg-amber-400 text-white hover:bg-amber-500'
                      }`}
                    >
                      <Calendar className="w-4 h-4" />
                      <span>{isFreeToday ? 'לא פנוי היום' : 'פנוי היום'}</span>
                    </motion.button>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Primary Actions Row */}
                  <div className="grid grid-cols-2 gap-3">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleChat}
                      className="flex items-center justify-center gap-2 bg-pink-600 text-white px-4 py-3 rounded-lg hover:bg-pink-700 transition-colors"
                    >
                      <MessageSquare className="w-4 h-4" />
                      <span>צ'אט</span>
                    </motion.button>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleGift}
                      className="flex items-center justify-center gap-2 bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors cursor-pointer"
                    >
                      <Gift className="w-4 h-4" />
                      <span>שלח מתנה</span>
                    </motion.div>
                  </div>

                  {/* Secondary Actions Row */}
                  <div className="grid grid-cols-2 gap-3">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleBlock}
                      className={`flex items-center justify-center gap-2 ${isBlocked ? 'bg-gray-600 hover:bg-gray-700' : 'bg-red-600 hover:bg-red-700'} text-white px-4 py-3 rounded-lg transition-colors`}
                    >
                      <Ban className="w-4 h-4" />
                      <span className="text-sm">{isBlocked ? 'הסר חסימה' : 'חסום'}</span>
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleReport}
                      className="flex items-center justify-center gap-2 bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 transition-colors"
                    >
                      <Flag className="w-4 h-4" />
                      <span>דווח</span>
                    </motion.button>
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
              {/* Basic Info */}
              <div className="space-y-6">
                <div>
                  <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">פרטים אישיים</h2>
                  {/* Free Today Badge */}
                  {profile.gender === 'couple' && isFreeToday && (
                    <div className="mb-4 flex items-center gap-2 bg-amber-100 text-amber-800 border-2 border-amber-400 px-3 py-2 rounded-lg">
                      <Calendar className="w-5 h-5" />
                      <span className="font-medium">פנוי היום</span>
                    </div>
                  )}
                  <div className="space-y-4">
                    {renderDetailRow('שם משתמש', profile.username)}
                    {renderDetailRow('עיר', profile.city)}
                    {renderDetailRow('אזור', getTranslatedValue(profile.area))}
                    {profile.gender === 'couple' ? (
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h4 className="text-sm font-semibold text-blue-800 mb-2">גילאים</h4>
                        <div className="grid grid-cols-2 gap-4">
                          {(() => {
                            const birthDate = profileData.birth_date || profile.birth_date;
                            console.log('Displaying male age - profileData.birth_date:', profileData.birth_date, 'profile.birth_date:', profile.birth_date, 'final:', birthDate);
                            return birthDate && (
                              <div className="text-center">
                                <div className="text-xs text-blue-600 font-medium">גבר</div>
                                <div className="text-lg font-bold text-blue-800">{formatAge(birthDate)}</div>
                              </div>
                            );
                          })()}
                          {(() => {
                            const partnerBirthDate = profileData.partner_birth_date || profile.partner_birth_date;
                            console.log('Displaying female age - profileData.partner_birth_date:', profileData.partner_birth_date, 'profile.partner_birth_date:', profile.partner_birth_date, 'final:', partnerBirthDate);
                            return partnerBirthDate && (
                              <div className="text-center">
                                <div className="text-xs text-pink-600 font-medium">אישה</div>
                                <div className="text-lg font-bold text-pink-800">{formatAge(partnerBirthDate)}</div>
                              </div>
                            );
                          })()}
                        </div>
                      </div>
                    ) : (
                      (() => {
                        const birthDate = profileData.birth_date || profile.birth_date;
                        console.log('Displaying age - profileData.birth_date:', profileData.birth_date, 'profile.birth_date:', profile.birth_date, 'final:', birthDate);
                        return birthDate && renderDetailRow('גיל', formatAge(birthDate));
                      })()
                    )}

                    {profile.gender === 'couple' ? (
                      <>
                        {/* Partner 1 Details */}
                        <div className="mt-6">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-3">פרטי גבר</h3>
                          {renderDetailRow('שם גבר', profileData.name1)}
                          {renderDetailRow('גובה', profileData.height1 ? `${profileData.height1} ס"מ` : undefined)}
                          {renderDetailRow('משקל', profileData.weight1 ? `${profileData.weight1} ק"ג` : undefined)}
                          {renderDetailRow('צבע השער', getTranslatedValue(profileData.hairColor1))}
                          {renderDetailRow('תסרוקת', getTranslatedValue(profileData.hairStyle1))}
                          {renderDetailRow('צבע עיניים', getTranslatedValue(profileData.eyeColor1))}
                          {renderDetailRow('מבנה גוף', getTranslatedValue(profileData.bodyType1))}
                          {renderDetailRow('מוצא', getTranslatedValue(profileData.ethnicity1))}
                          {renderDetailRow('העדפה מינית', getTranslatedValue(profileData.sexualPreference1))}
                          {renderDetailRow('ניסיון בחילופי זוגות', getTranslatedValue(profileData.swingingExperience1))}
                          {renderDetailRow('הרגלי עישון', getTranslatedValue(profileData.smokingHabits1))}
                          {renderDetailRow('הרגלי שתיה', getTranslatedValue(profileData.drinkingHabits1))}
                        </div>

                        {/* Partner 2 Details */}
                        <div className="mt-6">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-3">פרטי אישה</h3>
                          {renderDetailRow('שם אישה', profileData.name2)}
                          {renderDetailRow('גובה', profileData.height2 ? `${profileData.height2} ס"מ` : undefined)}
                          {renderDetailRow('משקל', profileData.weight2 ? `${profileData.weight2} ק"ג` : undefined)}
                          {renderDetailRow('צבע השער', getTranslatedValue(profileData.hairColor2))}
                          {renderDetailRow('תסרוקת', getTranslatedValue(profileData.hairStyle2))}
                          {renderDetailRow('צבע עיניים', getTranslatedValue(profileData.eyeColor2))}
                          {renderDetailRow('מבנה גוף', getTranslatedValue(profileData.bodyType2))}
                          {renderDetailRow('מוצא', getTranslatedValue(profileData.ethnicity2))}
                          {renderDetailRow('העדפה מינית', getTranslatedValue(profileData.sexualPreference2))}
                          {renderDetailRow('ניסיון בחילופי זוגות', getTranslatedValue(profileData.swingingExperience2))}
                          {renderDetailRow('הרגלי עישון', getTranslatedValue(profileData.smokingHabits2))}
                          {renderDetailRow('הרגלי שתיה', getTranslatedValue(profileData.drinkingHabits2))}
                        </div>
                      </>
                    ) : (
                      <>
                        {/* Single Person Details */}
                        {renderDetailRow('שם', profileData.name)}
                        {renderDetailRow('גובה', profileData.height ? `${profileData.height} ס"מ` : undefined)}
                        {renderDetailRow('משקל', profileData.weight ? `${profileData.weight} ק"ג` : undefined)}
                        {renderDetailRow('צבע השער', getTranslatedValue(profileData.hairColor))}
                        {renderDetailRow('תסרוקת', getTranslatedValue(profileData.hairStyle))}
                        {renderDetailRow('צבע עיניים', getTranslatedValue(profileData.eyeColor))}
                        {renderDetailRow('מבנה גוף', getTranslatedValue(profileData.bodyType))}
                        {renderDetailRow('מוצא', getTranslatedValue(profileData.ethnicity))}
                        {renderDetailRow('העדפה מינית', getTranslatedValue(profileData.sexualPreference))}
                        {renderDetailRow('ניסיון בחילופי זוגות', getTranslatedValue(profileData.swingingExperience))}
                        {renderDetailRow('הרגלי עישון', getTranslatedValue(profileData.smokingHabits))}
                        {renderDetailRow('הרגלי שתיה', getTranslatedValue(profileData.drinkingHabits))}
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="space-y-6">
                <div>
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
                    <h2 className="text-lg sm:text-xl font-semibold text-gray-900">מידע נוסף</h2>
                    <div className="text-xs text-gray-500">
                      עודכן: {lastUpdateTime.toLocaleTimeString('he-IL')}
                    </div>
                  </div>
                  <div className="space-y-4">
                    {renderDetailRow('מצב משפחתי', getTranslatedValue(profileData.maritalStatus))}
                    {profileData.maritalStatus !== 'single' && renderDetailRow('מס\' ילדים', profileData.children)}

                    {/* What we're looking for */}
                    {profileData.lookingFor && (
                      <div className="bg-blue-50 rounded-lg p-3 sm:p-4 border border-blue-200">
                        <h3 className="font-medium text-blue-800 mb-2 text-sm sm:text-base flex items-center gap-2">
                          🔍 מה אנחנו מחפשים
                        </h3>
                        <p className="text-blue-700 text-sm sm:text-base leading-relaxed">
                          {profileData.lookingFor}
                        </p>
                      </div>
                    )}

                    {profileData.aboutUs && (
                      <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                        <h3 className="font-medium text-gray-700 mb-2 text-sm sm:text-base">קצת עלינו</h3>
                        <p className="text-gray-900 text-sm sm:text-base leading-relaxed">{profileData.aboutUs}</p>
                      </div>
                    )}

                    {profileData.lookingFor && (
                      <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                        <h3 className="font-medium text-gray-700 mb-2 text-sm sm:text-base">מה אנחנו מחפשים</h3>
                        <p className="text-gray-900 text-sm sm:text-base leading-relaxed">{profileData.lookingFor}</p>
                      </div>
                    )}

                    {profileData.seekingGender && profileData.seekingGender.length > 0 && (
                      <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                        <h3 className="font-medium text-gray-700 mb-2 text-sm sm:text-base">מחפשים</h3>
                        <p className="text-gray-900 text-sm sm:text-base">
                          {profileData.seekingGender.map(gender => getTranslatedValue(gender)).join(', ')}
                        </p>
                      </div>
                    )}

                    {profileData.meetingTimes && profileData.meetingTimes.length > 0 && (
                      <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                        <h3 className="font-medium text-gray-700 mb-2 text-sm sm:text-base">זמני מפגש מועדפים</h3>
                        <p className="text-gray-900 text-sm sm:text-base">
                          {profileData.meetingTimes.map(time => getTranslatedValue(time)).join(', ')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <EditProfileModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setIsEditing(false);
          // Auto refresh after closing edit modal to show changes
          setTimeout(() => {
            handleManualRefresh();
          }, 500);
        }}
        onOpen={() => setIsEditing(true)}
      />
      <SettingsModal
        isOpen={isSettingsModalOpen}
        onClose={() => setIsSettingsModalOpen(false)}
      />

      {/* Gift Dialog - Render outside the component to prevent event bubbling issues */}
      {!isOwnProfile && isGiftDialogOpen && ReactDOM.createPortal(
        <SendGiftDialog
          isOpen={isGiftDialogOpen}
          onClose={() => setIsGiftDialogOpen(false)}
          receiverId={profile.id}
          receiverName={profile.username}
        />,
        document.body
      )}

      {/* Block Confirmation Dialog */}
      {isBlockConfirmOpen && ReactDOM.createPortal(
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-4 sm:p-6 text-right">
            <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-4">
              {isBlocked ? 'הסרת חסימה' : 'חסימת משתמש'}
            </h3>
            <p className="text-gray-700 mb-6 text-sm sm:text-base leading-relaxed">
              {isBlocked
                ? `האם אתה בטוח שברצונך להסיר את החסימה של ${profile.username}?`
                : `האם אתה בטוח שברצונך לחסום את ${profile.username}? משתמשים חסומים לא יוכלו ליצור איתך קשר.`
              }
            </p>
            <div className="flex flex-col sm:flex-row justify-end gap-3">
              <button
                onClick={() => setIsBlockConfirmOpen(false)}
                className="px-4 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors text-sm sm:text-base"
              >
                ביטול
              </button>
              <button
                onClick={confirmBlock}
                className={`px-4 py-3 ${isBlocked ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'} text-white rounded-lg transition-colors text-sm sm:text-base`}
              >
                {isBlocked ? 'הסר חסימה' : 'חסום'}
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Report Dialog */}
      {isReportDialogOpen && ReactDOM.createPortal(
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-4 sm:p-6 text-right max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-4">
              דיווח על פרופיל
            </h3>
            <p className="text-gray-700 mb-4 text-sm sm:text-base leading-relaxed">
              אנא בחר את הסיבה לדיווח על הפרופיל של {profile.username}:
            </p>
            <div className="space-y-3 mb-6">
              {['תמונות לא הולמות', 'התחזות', 'הטרדה', 'ספאם', 'מידע שקרי', 'אחר'].map((reason) => (
                <button
                  key={reason}
                  onClick={() => submitReport(reason)}
                  className="w-full text-right px-4 py-3 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors text-sm sm:text-base"
                >
                  {reason}
                </button>
              ))}
            </div>
            <div className="flex justify-end">
              <button
                onClick={() => setIsReportDialogOpen(false)}
                className="px-4 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors text-sm sm:text-base"
              >
                ביטול
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}

export default ProfilePage;