import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Clock, Filter, Search, X, Info, ChevronDown, Users, MapPin, Timer, Grid3X3, List, LayoutGrid } from 'lucide-react';
import CompactUserCard from './CompactUserCard';
import MiniUserCard from './MiniUserCard';
import SkeletonCard from './SkeletonCard';
import { Profile } from '../types/supabase';
import { useFreeTodayWithExpiry } from '../hooks/useFreeTodayWithExpiry';

interface FreeTodayPageProps {
  profiles: Profile[];
  isVip: boolean;
}

function FreeTodayPage({ profiles, isVip }: FreeTodayPageProps) {
  // Use the new hook for free today with expiry
  const {
    isFreeToday,
    expiresAt,
    timeRemaining,
    loading: freeTodayLoading,
    error: freeTodayError,
    joinFreeToday,
    leaveFreeToday
  } = useFreeTodayWithExpiry();

  // Simulate loading for better UX
  useEffect(() => {
    setIsLoading(true);
    setLoadingProgress(0);

    const timer = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setIsLoading(false);
          return 100;
        }
        return prev + 10;
      });
    }, 100);

    return () => clearInterval(timer);
  }, [profiles]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);
  // State for filters and view
  const [viewMode, setViewMode] = useState<'mini' | 'compact' | 'list'>('mini');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [showInfoCard, setShowInfoCard] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date());
  const prevProfilesRef = useRef<Profile[]>([]);

  // Update last update time when profiles change
  useEffect(() => {
    console.log('FreeTodayPage: Profiles updated, checking for changes', profiles.length);

    // Always update on first render
    if (prevProfilesRef.current.length === 0) {
      console.log('FreeTodayPage: First render, setting initial profiles');
      setLastUpdateTime(new Date());
      prevProfilesRef.current = [...profiles];
      return;
    }

    // Check if profiles have actually changed in length
    if (profiles.length !== prevProfilesRef.current.length) {
      console.log('FreeTodayPage: Profile count changed',
        prevProfilesRef.current.length, '->', profiles.length);
      setLastUpdateTime(new Date());
      prevProfilesRef.current = [...profiles];
      return;
    }

    // Create maps for more efficient comparison
    const prevProfilesMap = new Map(
      prevProfilesRef.current.map(profile => [profile.id, profile])
    );

    // Check for new profiles or changes in existing profiles
    let hasChanges = false;

    for (const profile of profiles) {
      const prevProfile = prevProfilesMap.get(profile.id);

      // New profile
      if (!prevProfile) {
        console.log('FreeTodayPage: New profile detected', profile.id);
        hasChanges = true;
        break;
      }

      // Check if user_metadata has changed
      const prevMetadata = JSON.stringify(prevProfile.user_metadata || {});
      const currentMetadata = JSON.stringify(profile.user_metadata || {});

      if (prevMetadata !== currentMetadata) {
        console.log('FreeTodayPage: Profile metadata changed for', profile.id);
        hasChanges = true;
        break;
      }
    }

    if (hasChanges) {
      console.log('FreeTodayPage: Updating profiles and last update time');
      setLastUpdateTime(new Date());
      prevProfilesRef.current = [...profiles];
    } else {
      console.log('FreeTodayPage: No changes detected in profiles');
    }
  }, [profiles]);

  // Get unique cities from profiles
  const cities = [...new Set(profiles.map(profile => profile.city))].sort();

  // Filter profiles based on search and filters
  const filteredProfiles = profiles.filter(profile => {
    // Filter by search term (using debounced search)
    const matchesSearch = debouncedSearchTerm === '' ||
      profile.username.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      profile.city.toLowerCase().includes(debouncedSearchTerm.toLowerCase());

    // Filter by city
    const matchesCity = selectedCity === '' || profile.city === selectedCity;

    // Filter by gender
    const matchesGender = selectedGender === '' || profile.gender === selectedGender;

    return matchesSearch && matchesCity && matchesGender;
  });

  // Empty state
  if (profiles.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-2xl mx-auto">
        <div className="bg-amber-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <Calendar className="w-10 h-10 text-amber-500" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">אין משתמשים פנויים היום</h3>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">נסה שוב מאוחר יותר או היה הראשון להצטרף ולהפוך את היום למיוחד!</p>
        {isVip && !isFreeToday && (
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            onClick={joinFreeToday}
            disabled={freeTodayLoading}
            className="mt-2 bg-gradient-to-r from-amber-500 to-amber-600 text-white px-8 py-3 rounded-xl font-medium hover:from-amber-600 hover:to-amber-700 transition-colors shadow-md disabled:opacity-50"
          >
            <span className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              {freeTodayLoading ? 'מצטרף...' : 'הצטרף לפנויים היום'}
            </span>
          </motion.button>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl shadow-lg">
        <div className="absolute inset-0 bg-pattern opacity-10"></div>

        <div className="relative z-10 p-4 sm:p-6 md:p-8 lg:p-10 flex flex-col md:flex-row md:items-center justify-between gap-4 sm:gap-6">
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-2 flex items-center gap-2">
              <Calendar className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7" />
              פנויים היום
            </h1>
            <p className="text-amber-100 text-xs sm:text-sm md:text-base max-w-xl">
              גלה משתמשים שפנויים למפגש היום! כאן תוכל למצוא אנשים שמחפשים חוויות ספונטניות ומפגשים מיידיים.
            </p>
          </div>

          {isVip && (
            <div className="flex flex-col items-end gap-2">
              <motion.button
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                onClick={isFreeToday ? leaveFreeToday : joinFreeToday}
                disabled={freeTodayLoading}
                className={`flex items-center gap-2 px-4 sm:px-6 py-2 sm:py-3 rounded-xl text-sm sm:text-base font-medium shadow-md transition-all disabled:opacity-50 ${
                  isFreeToday
                    ? 'bg-white text-amber-600'
                    : 'bg-gradient-to-r from-amber-700 to-amber-800 text-white border border-amber-400'
                }`}
              >
                <Calendar className="w-4 h-4 sm:w-5 sm:h-5" />
                <span>
                  {freeTodayLoading
                    ? (isFreeToday ? 'יוצא...' : 'מצטרף...')
                    : (isFreeToday ? 'לא פנוי היום' : 'פנוי היום')
                  }
                </span>
              </motion.button>

              {isFreeToday && timeRemaining && (
                <div className="flex items-center gap-1 text-xs sm:text-sm text-amber-100">
                  <Timer className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{timeRemaining}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Loading Progress Bar */}
        {isLoading && (
          <div className="absolute bottom-0 left-0 right-0 z-20">
            <div className="bg-white/20 h-1">
              <motion.div
                className="bg-white h-full"
                initial={{ width: 0 }}
                animate={{ width: `${loadingProgress}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
          </div>
        )}

        {/* Wave decoration */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path fill="#ffffff" fillOpacity="1" d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
          </svg>
        </div>
      </div>

      {/* Info Card - Can be dismissed */}
      <AnimatePresence>
        {showInfoCard && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-blue-50 border border-blue-200 rounded-xl p-4 relative"
          >
            <button
              onClick={() => setShowInfoCard(false)}
              className="absolute top-3 right-3 text-blue-400 hover:text-blue-600"
            >
              <X className="w-5 h-5" />
            </button>

            <div className="flex items-start gap-3">
              <div className="bg-blue-100 rounded-full p-2 mt-1">
                <Info className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-800 mb-1">איך זה עובד?</h3>
                <p className="text-sm text-blue-700">
                  משתמשי VIP יכולים לסמן את עצמם כ"פנויים היום" ולהופיע ברשימה זו למשך 24 שעות.
                  זוהי דרך מצוינת למצוא מפגשים ספונטניים ולהכיר אנשים חדשים שזמינים כרגע.
                  {!isVip && <span className="font-medium"> שדרג ל-VIP כדי להצטרף!</span>}
                  {isVip && <span className="font-medium"> הסטטוס יפוג אוטומטית אחרי 24 שעות.</span>}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-md p-5 border border-gray-100">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="חיפוש לפי שם או עיר..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-200 rounded-xl focus:border-amber-500 focus:ring-1 focus:ring-amber-500"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-xl p-1">
            <button
              onClick={() => setViewMode('mini')}
              className={`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                viewMode === 'mini'
                  ? 'bg-white text-amber-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
              <span className="hidden sm:inline">רשת</span>
            </button>
            <button
              onClick={() => setViewMode('compact')}
              className={`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                viewMode === 'compact'
                  ? 'bg-white text-amber-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <LayoutGrid className="w-4 h-4" />
              <span className="hidden sm:inline">קומפקט</span>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-amber-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <List className="w-4 h-4" />
              <span className="hidden sm:inline">רשימה</span>
            </button>
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center gap-2 px-4 py-3 bg-amber-50 text-amber-700 rounded-xl border border-amber-200 hover:bg-amber-100 transition-colors"
          >
            <Filter className="w-5 h-5" />
            <span className="hidden sm:inline">סינון</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Expanded Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ blockSize: 0, opacity: 0 }}
              animate={{ blockSize: 'auto', opacity: 1 }}
              exit={{ blockSize: 0, opacity: 0 }}
              className="overflow-hidden mt-4"
            >
              <div className="pt-4 border-t border-gray-100 grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* City Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">עיר</label>
                  <select
                    value={selectedCity}
                    onChange={(e) => setSelectedCity(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-amber-500 focus:ring-1 focus:ring-amber-500"
                  >
                    <option value="">כל הערים</option>
                    {cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>

                {/* Gender Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">מגדר</label>
                  <select
                    value={selectedGender}
                    onChange={(e) => setSelectedGender(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-amber-500 focus:ring-1 focus:ring-amber-500"
                  >
                    <option value="">כל המגדרים</option>
                    <option value="male">גברים</option>
                    <option value="female">נשים</option>
                    <option value="couple">זוגות</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Results Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="bg-amber-100 rounded-full p-1.5">
            <Users className="w-4 h-4 text-amber-700" />
          </div>
          <span className="text-gray-700">
            <span className="font-semibold">{filteredProfiles.length}</span> משתמשים פנויים היום
          </span>
        </div>

        <div className="text-sm text-gray-500 flex items-center gap-1">
          <Clock className="w-4 h-4" />
          <span>עודכן: {lastUpdateTime.toLocaleTimeString('he-IL')}</span>
        </div>
      </div>

      {/* Users Grid */}
      <div className={`grid gap-4 ${
        viewMode === 'mini'
          ? 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6'
          : viewMode === 'compact'
          ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
          : 'grid-cols-1'
      }`}>
        <AnimatePresence mode="popLayout">
          {isLoading ? (
            // Show skeleton cards while loading
            Array.from({ length: 12 }).map((_, index) => (
              <motion.div
                key={`skeleton-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <SkeletonCard variant={viewMode === 'mini' ? 'mini' : 'compact'} />
              </motion.div>
            ))
          ) : filteredProfiles.length > 0 ? (
            filteredProfiles.map((profile, index) => (
              <motion.div
                key={profile.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                layout
              >
                {viewMode === 'mini' ? (
                  <MiniUserCard profile={profile} />
                ) : (
                  <CompactUserCard profile={profile} />
                )}
              </motion.div>
            ))
          ) : (
          <div className="bg-gray-50 rounded-xl p-8 text-center">
            <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">לא נמצאו תוצאות</h3>
            <p className="text-gray-500">נסה לשנות את הגדרות החיפוש או הסינון שלך</p>
          </div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

export default FreeTodayPage;
