/*
  # Add VIP Packages Feature

  1. New Tables
    - `vip_packages` table for storing VIP subscription packages
      - `id` (uuid, primary key)
      - `name` (text)
      - `description` (text)
      - `price` (integer)
      - `duration_days` (integer)
      - `features` (text[])
      - `is_active` (boolean)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `user_subscriptions` table for tracking user subscriptions
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `package_id` (uuid, references vip_packages)
      - `start_date` (timestamptz)
      - `end_date` (timestamptz)
      - `is_active` (boolean)
      - `payment_id` (text)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - All users can view active packages
      - <PERSON>mins can manage packages
      - Users can view their own subscriptions
*/

-- Create vip_packages table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'vip_packages') THEN
    CREATE TABLE vip_packages (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      name text NOT NULL,
      description text,
      price integer NOT NULL,
      duration_days integer NOT NULL,
      features text[],
      is_active boolean DEFAULT true,
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now()
    );
  END IF;
END
$$;

-- Create user_subscriptions table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_subscriptions') THEN
    CREATE TABLE user_subscriptions (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
      package_id uuid REFERENCES vip_packages(id) ON DELETE RESTRICT,
      start_date timestamptz DEFAULT now(),
      end_date timestamptz NOT NULL,
      is_active boolean DEFAULT true,
      payment_id text,
      created_at timestamptz DEFAULT now(),
      UNIQUE (user_id, package_id, payment_id)
    );
  END IF;
END
$$;

-- Enable RLS
ALTER TABLE vip_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Create policies for vip_packages using DO blocks to check if they exist
DO $$
BEGIN
  -- Check if "Users can view active packages" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'vip_packages' AND policyname = 'Users can view active packages'
  ) THEN
    CREATE POLICY "Users can view active packages"
      ON vip_packages
      FOR SELECT
      TO authenticated
      USING (is_active = true);
  END IF;

  -- Check if "Admins can manage packages" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'vip_packages' AND policyname = 'Admins can manage packages'
  ) THEN
    CREATE POLICY "Admins can manage packages"
      ON vip_packages
      FOR ALL
      TO authenticated
      USING (auth.email() = '<EMAIL>')
      WITH CHECK (auth.email() = '<EMAIL>');
  END IF;
END
$$;

-- Create policies for user_subscriptions using DO blocks to check if they exist
DO $$
BEGIN
  -- Check if "Users can view their own subscriptions" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'user_subscriptions' AND policyname = 'Users can view their own subscriptions'
  ) THEN
    CREATE POLICY "Users can view their own subscriptions"
      ON user_subscriptions
      FOR SELECT
      TO authenticated
      USING (auth.uid() = user_id);
  END IF;

  -- Check if "Admins can view all subscriptions" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'user_subscriptions' AND policyname = 'Admins can view all subscriptions'
  ) THEN
    CREATE POLICY "Admins can view all subscriptions"
      ON user_subscriptions
      FOR SELECT
      TO authenticated
      USING (auth.email() = '<EMAIL>');
  END IF;

  -- Check if "Admins can manage subscriptions" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'user_subscriptions' AND policyname = 'Admins can manage subscriptions'
  ) THEN
    CREATE POLICY "Admins can manage subscriptions"
      ON user_subscriptions
      FOR ALL
      TO authenticated
      USING (auth.email() = '<EMAIL>')
      WITH CHECK (auth.email() = '<EMAIL>');
  END IF;
END
$$;

-- Create function to update user VIP status based on active subscriptions
CREATE OR REPLACE FUNCTION update_user_vip_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If a new subscription is added or an existing one is updated
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    -- Update the user's VIP status based on whether they have an active subscription
    UPDATE profiles
    SET is_vip = EXISTS (
      SELECT 1 FROM user_subscriptions
      WHERE user_id = NEW.user_id
      AND is_active = true
      AND end_date > now()
    )
    WHERE id = NEW.user_id;
  -- If a subscription is deleted
  ELSIF TG_OP = 'DELETE' THEN
    -- Update the user's VIP status based on whether they have any remaining active subscriptions
    UPDATE profiles
    SET is_vip = EXISTS (
      SELECT 1 FROM user_subscriptions
      WHERE user_id = OLD.user_id
      AND is_active = true
      AND end_date > now()
    )
    WHERE id = OLD.user_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers to update VIP status if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_vip_status_on_insert_or_update') THEN
    CREATE TRIGGER update_vip_status_on_insert_or_update
    AFTER INSERT OR UPDATE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_vip_status();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_vip_status_on_delete') THEN
    CREATE TRIGGER update_vip_status_on_delete
    AFTER DELETE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_vip_status();
  END IF;
END
$$;

-- Insert default packages only if the table is empty
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM vip_packages LIMIT 1) THEN
    INSERT INTO vip_packages (name, description, price, duration_days, features, is_active)
    VALUES
      ('חבילה בסיסית', 'חבילת VIP בסיסית עם יתרונות חיוניים', 49, 30,
       ARRAY['צ''אט ללא הגבלה', 'פנוי היום', 'צפייה בפרופילים ללא הגבלה'], true),

      ('חבילה מתקדמת', 'חבילת VIP מתקדמת עם יתרונות נוספים', 99, 30,
       ARRAY['צ''אט ללא הגבלה', 'פנוי היום', 'צפייה בפרופילים ללא הגבלה', 'הודעות מועדפות', 'תג VIP'], true),

      ('חבילה שנתית', 'חבילת VIP שנתית במחיר מוזל', 499, 365,
       ARRAY['צ''אט ללא הגבלה', 'פנוי היום', 'צפייה בפרופילים ללא הגבלה', 'הודעות מועדפות', 'תג VIP', 'תמיכה VIP'], true);
  END IF;
END
$$;
