import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';

interface StoryImage {
  url: string;
  created_at: string;
}

interface Story {
  id: string;
  user_id: string;
  username: string;
  images: StoryImage[];
  created_at: string;
}

export function useStories() {
  const { user } = useAuth();
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    const fetchStories = async () => {
      try {
        const { data, error: fetchError } = await supabase
          .from('stories')
          .select(`
            id,
            user_id,
            url,
            created_at,
            profiles!stories_user_id_fkey (username)
          `)
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
          .order('created_at', { ascending: false });

        if (fetchError) {
          console.error('Supabase error:', fetchError);
          throw new Error('Failed to fetch stories from database');
        }

        if (!data) {
          setStories([]);
          return;
        }

        // Group stories by user_id
        const groupedStories = data.reduce((acc: Record<string, Story>, curr) => {
          if (!acc[curr.user_id]) {
            acc[curr.user_id] = {
              id: curr.user_id,
              user_id: curr.user_id,
              username: curr.profiles?.username || 'Unknown User',
              images: [],
              created_at: curr.created_at
            };
          }
          
          if (curr.url) {
            acc[curr.user_id].images.push({
              url: curr.url,
              created_at: curr.created_at
            });
          }
          
          return acc;
        }, {});

        setStories(Object.values(groupedStories));
        setError(null);
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError(err instanceof Error ? err.message : 'Failed to load stories');
        setStories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStories();

    // Subscribe to new stories
    const subscription = supabase
      .channel('stories')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'stories'
        },
        async (payload) => {
          try {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('username')
              .eq('id', payload.new.user_id)
              .single();

            if (profileError) {
              console.error('Error fetching profile:', profileError);
              return;
            }

            const newImage = {
              url: payload.new.url,
              created_at: payload.new.created_at
            };

            setStories(prev => {
              const existingStoryIndex = prev.findIndex(s => s.user_id === payload.new.user_id);
              
              if (existingStoryIndex > -1) {
                const updatedStories = [...prev];
                updatedStories[existingStoryIndex].images.unshift(newImage);
                return updatedStories;
              }

              return [{
                id: payload.new.user_id,
                user_id: payload.new.user_id,
                username: profileData?.username || 'Unknown User',
                images: [newImage],
                created_at: payload.new.created_at
              }, ...prev];
            });
          } catch (err) {
            console.error('Error processing new story:', err);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user]);

  const uploadStory = async (file: File) => {
    if (!user) {
      throw new Error('Must be logged in to upload stories');
    }

    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      // Upload file to storage
      const { error: uploadError } = await supabase.storage
        .from('stories')
        .upload(fileName, file);

      if (uploadError) {
        console.error('Storage upload error:', uploadError);
        throw new Error('Failed to upload story image');
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('stories')
        .getPublicUrl(fileName);

      // Create story record
      const { error: insertError } = await supabase
        .from('stories')
        .insert({
          user_id: user.id,
          url: publicUrl
        });

      if (insertError) {
        console.error('Database insert error:', insertError);
        throw new Error('Failed to create story record');
      }

      return { error: null };
    } catch (err) {
      console.error('Error uploading story:', err);
      return { error: err instanceof Error ? err.message : 'Failed to upload story' };
    }
  };

  return {
    stories,
    loading,
    error,
    uploadStory
  };
}