/*
  # Update storage bucket configuration

  1. Storage Bucket
    - Set file size limits and allowed mime types for 'photo' bucket
    - Enable RLS on storage objects
    - Add policies with existence checks for:
      - Uploading own photos
      - Viewing all photos
      - Deleting own photos
*/

-- Create storage bucket for photos if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'photo'
  ) THEN
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('photo', 'photo', true);
  END IF;
END $$;

-- Set bucket configuration
UPDATE storage.buckets
SET file_size_limit = 5242880, -- 5MB in bytes
    allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
WHERE id = 'photo';

-- Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create policies with existence checks
DO $$
BEGIN
  -- Upload policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'objects' 
    AND policyname = 'Users can upload their own photos'
  ) THEN
    CREATE POLICY "Users can upload their own photos"
    ON storage.objects FOR INSERT TO authenticated
    WITH CHECK (
      bucket_id = 'photo' AND
      (storage.foldername(name))[1] = auth.uid()::text
    );
  END IF;

  -- View policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'objects' 
    AND policyname = 'Users can view all photos'
  ) THEN
    CREATE POLICY "Users can view all photos"
    ON storage.objects FOR SELECT TO authenticated
    USING (bucket_id = 'photo');
  END IF;

  -- Delete policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'objects' 
    AND policyname = 'Users can delete their own photos'
  ) THEN
    CREATE POLICY "Users can delete their own photos"
    ON storage.objects FOR DELETE TO authenticated
    USING (
      bucket_id = 'photo' AND
      (storage.foldername(name))[1] = auth.uid()::text
    );
  END IF;
END $$;