import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, Image as ImageIcon, ArrowRight, Check, X, Loader2 } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';

interface ImageUploadFormProps {
  onBack: () => void;
  onComplete: () => Promise<{ error: any }>;
}

function ImageUploadForm({ onBack, onComplete }: ImageUploadFormProps) {
  const { user } = useAuth();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const newFiles: File[] = [];
    const newPreviewUrls: string[] = [];

    Array.from(files).forEach(file => {
      if (selectedFiles.length + newFiles.length >= 4) return;
      
      // Check file type
      if (!file.type.startsWith('image/')) {
        setError('ניתן להעלות קבצי תמונה בלבד');
        return;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('גודל התמונה לא יכול לעלות על 5MB');
        return;
      }

      newFiles.push(file);
      newPreviewUrls.push(URL.createObjectURL(file));
    });

    setSelectedFiles([...selectedFiles, ...newFiles]);
    setPreviewUrls([...previewUrls, ...newPreviewUrls]);
    setError(null);
  };

  const removeImage = (index: number) => {
    URL.revokeObjectURL(previewUrls[index]);
    
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleComplete = async () => {
    setUploading(true);
    setError(null);

    try {
      // First complete the signup
      const { error: signupError } = await onComplete();
      if (signupError) throw signupError;

      // Then upload images if any were selected
      if (selectedFiles.length > 0 && user) {
        const uploadPromises = selectedFiles.map(async (file, index) => {
          const fileExt = file.name.split('.').pop();
          const fileName = `${user.id}/${Date.now()}-${index}.${fileExt}`;

          const { error: uploadError } = await supabase.storage
            .from('photos')
            .upload(fileName, file);

          if (uploadError) throw uploadError;
        });

        await Promise.all(uploadPromises);
      }
    } catch (err) {
      console.error('Error during completion:', err);
      setError('אירעה שגיאה בתהליך ההרשמה. אנא נסה שוב.');
      setUploading(false);
      return;
    }

    // No need to call onComplete again or handle navigation - 
    // Auth state change will automatically redirect
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <div className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] p-8">
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="inline-block p-3 bg-pink-100 rounded-full mb-4"
            >
              <ImageIcon className="w-8 h-8 text-pink-600" />
            </motion.div>
            <h1 className="text-2xl font-bold text-gray-900">הוסף תמונות</h1>
            <p className="text-gray-600 mt-2">העלה עד 4 תמונות לפרופיל שלך</p>
          </div>

          <div className="space-y-6">
            <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center">
              <input
                type="file"
                id="fileInput"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
                disabled={uploading}
              />
              <label
                htmlFor="fileInput"
                className="cursor-pointer flex flex-col items-center justify-center gap-3"
              >
                <Upload className="w-12 h-12 text-gray-400" />
                <span className="text-lg font-medium text-gray-700">
                  {uploading ? 'מעלה תמונות...' : 'בחר תמונות'}
                </span>
                <span className="text-sm text-gray-500">
                  {selectedFiles.length === 0 ? 'לא נבחרו קבצים' : `נבחרו ${selectedFiles.length} קבצים`}
                </span>
              </label>
            </div>

            {error && (
              <div className="bg-red-50 text-red-700 p-4 rounded-lg">
                {error}
              </div>
            )}

            {previewUrls.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {previewUrls.map((url, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={url}
                      alt={`תמונה ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <button
                      onClick={() => removeImage(index)}
                      disabled={uploading}
                      className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className="bg-blue-50 p-4 rounded-lg text-sm text-blue-700">
              <p className="mb-2">מומלץ לצרף תמונה, בעלי תמונות זוכים להתייחסות רצינית יותר.</p>
              <p className="mb-2">תמונות גלויות פרצוף תמחקנה מהמערכת.</p>
              <p className="mb-2">באפשרותכם להעלות עד 4 תמונות. גודל מקסימלי לתמונה: 5MB.</p>
            </div>

            <div className="flex justify-between gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onBack}
                disabled={uploading}
                className="flex items-center justify-center gap-2 px-6 py-3 border-2 border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <ArrowRight className="w-5 h-5" />
                חזור
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleComplete}
                disabled={uploading}
                className="flex-1 flex items-center justify-center gap-2 bg-pink-600 text-white py-3 px-6 rounded-xl font-medium hover:bg-pink-700 transition-colors disabled:opacity-50"
              >
                {uploading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    מעלה...
                  </>
                ) : (
                  <>
                    <Check className="w-5 h-5" />
                    סיום
                  </>
                )}
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default ImageUploadForm;