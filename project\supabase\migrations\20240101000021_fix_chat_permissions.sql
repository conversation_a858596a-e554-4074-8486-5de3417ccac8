-- Fix chat permissions
-- This migration fixes issues with chat permissions

-- Enable RLS on messages table
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies on messages table
DROP POLICY IF EXISTS "Anyone can send messages" ON messages;
DROP POLICY IF EXISTS "VIP users can send messages" ON messages;
DROP POLICY IF EXISTS "Users can send messages to their chats" ON messages;
DROP POLICY IF EXISTS "Users can read messages in their chats" ON messages;

-- Create policies for messages table
CREATE POLICY "Users can insert messages"
ON messages
FOR INSERT
TO authenticated
WITH CHECK (
  sender_id = auth.uid()
);

CREATE POLICY "Users can select messages in their chats"
ON messages
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
);

-- Enable RLS on chats table
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies on chats table
DROP POLICY IF EXISTS "Users can view their chats" ON chats;
DROP POLICY IF EXISTS "Users can update their chats" ON chats;

-- Create policies for chats table
CREATE POLICY "Users can select their chats"
ON chats
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = chats.id
    AND chat_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their chats"
ON chats
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = chats.id
    AND chat_participants.user_id = auth.uid()
  )
);

-- Enable RLS on chat_participants table
ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies on chat_participants table
DROP POLICY IF EXISTS "Users can view chat participants" ON chat_participants;

-- Create policies for chat_participants table
CREATE POLICY "Users can select chat participants"
ON chat_participants
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants AS cp
    WHERE cp.chat_id = chat_participants.chat_id
    AND cp.user_id = auth.uid()
  )
);
