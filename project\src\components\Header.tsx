import React, { useEffect, useState } from 'react';
import { Bell, Heart, Shield, LogOut, User, Menu, X, Crown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAdmin } from '../hooks/useAdmin';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from '../hooks/useNavigate';
import { useSimpleProfileImage } from '../hooks/useSimpleProfileImage';
import { supabase } from '../lib/supabase';
import NotificationsDropdown from './NotificationsDropdown';

function Header() {
  const isAdmin = useAdmin();
  const { user, signOut } = useAuth();
  const { navigateToHome, navigateToProfile, navigateToAdmin } = useNavigate();

  // Add a function to navigate to VIP page
  const navigateToVip = () => {
    // Access the App component's setCurrentPage function
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'vip' } }));
  };
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Use the profile image hook for automatic updates
  const { profileImage } = useSimpleProfileImage(user?.id);

  useEffect(() => {

    const fetchLogo = async () => {
      try {
        const { data: { publicUrl } } = supabase.storage
          .from('site-assets')
          .getPublicUrl('logo.png');

        const response = await fetch(publicUrl, { method: 'HEAD' });
        if (response.ok) {
          setLogoUrl(publicUrl);
        }
      } catch (error) {
        console.error('Error fetching logo:', error);
      }
    };

    const fetchNotifications = async () => {
      if (!user) return;

      try {
        // First try to fetch from the new notifications table
        const { data: notifications, error: notificationsError } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(10);

        if (!notificationsError && notifications) {
          setNotifications(notifications);
          setUnreadCount(notifications.filter(n => !n.read).length);
          return;
        }

        // Fallback to user_warnings if notifications table doesn't exist yet
        const { data: warnings, error } = await supabase
          .from('user_warnings')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(10);

        if (error) {
          console.error('Error fetching notifications:', error);
          return;
        }

        const notificationData = warnings?.map(warning => ({
          id: warning.id,
          type: 'warning',
          title: 'אזהרה מהמערכת',
          message: warning.message,
          created_at: warning.created_at,
          read: warning.is_read || false
        })) || [];

        setNotifications(notificationData);
        setUnreadCount(notificationData.filter(n => !n.read).length);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      }
    };

    fetchLogo();
    fetchNotifications();

    // Set up real-time subscription for new notifications
    let subscription: any = null;
    if (user) {
      subscription = supabase
        .channel('notifications_changes')
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            console.log('New notification received:', payload);
            // Add the new notification to the list
            setNotifications(prev => [payload.new, ...prev]);
            setUnreadCount(prev => prev + 1);
          }
        )
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'user_warnings',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            console.log('New warning received:', payload);
            // Add the new warning as a notification
            const newNotification = {
              id: payload.new.id,
              type: 'warning',
              title: 'אזהרה מהמערכת',
              message: payload.new.message,
              created_at: payload.new.created_at,
              read: false
            };
            setNotifications(prev => [newNotification, ...prev]);
            setUnreadCount(prev => prev + 1);
          }
        )
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'user_gifts',
            filter: `receiver_id=eq.${user.id}`
          },
          (payload) => {
            console.log('New gift received:', payload);
            // This will be handled by the trigger, but we can add immediate feedback here
          }
        )
        .subscribe();
    }

    return () => {
      if (subscription) {
        supabase.removeChannel(subscription);
      }
    };
  }, [user]);

  const handleSignOut = async () => {
    await signOut();
    setIsMenuOpen(false);
  };

  const handleProfileClick = () => {
    // Pass null explicitly to indicate we want to view the current user's profile
    navigateToProfile(null);
    setIsMenuOpen(false);
  };

  const handleAdminClick = () => {
    navigateToAdmin();
    setIsMenuOpen(false);
  };

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };

  const markNotificationAsRead = async (notificationId: string) => {
    try {
      // Try to update in notifications table first
      const { error: notificationError } = await supabase
        .from('notifications')
        .update({ read: true, read_at: new Date().toISOString() })
        .eq('id', notificationId);

      // If that fails, try user_warnings table
      if (notificationError) {
        const { error: warningError } = await supabase
          .from('user_warnings')
          .update({ is_read: true, read_at: new Date().toISOString() })
          .eq('id', notificationId);

        if (warningError) {
          console.error('Error marking notification as read:', warningError);
          return;
        }
      }

      // Update local state
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, read: true } : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const unreadIds = notifications.filter(n => !n.read).map(n => n.id);

      if (unreadIds.length === 0) return;

      // Try to update in notifications table first
      const { error: notificationError } = await supabase
        .from('notifications')
        .update({ read: true, read_at: new Date().toISOString() })
        .in('id', unreadIds);

      // If that fails, try user_warnings table
      if (notificationError) {
        const { error: warningError } = await supabase
          .from('user_warnings')
          .update({ is_read: true, read_at: new Date().toISOString() })
          .in('id', unreadIds);

        if (warningError) {
          console.error('Error marking all notifications as read:', warningError);
          return;
        }
      }

      // Update local state
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-40">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center cursor-pointer"
            onClick={navigateToHome}
          >
            {logoUrl ? (
              <img
                src={logoUrl}
                alt="Logo"
                className="h-8 w-auto"
              />
            ) : (
              <Heart className="h-8 w-8 text-pink-600" />
            )}
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            {/* Admin Icon */}
            {isAdmin && (
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="cursor-pointer"
                onClick={navigateToAdmin}
              >
                <Shield className="h-6 w-6 text-purple-600 hover:text-purple-700 transition-colors" />
              </motion.div>
            )}

            {/* VIP Subscription */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="cursor-pointer"
              onClick={navigateToVip}
            >
              <Crown className="h-6 w-6 text-yellow-500 hover:text-yellow-600 transition-colors" />
            </motion.div>

            {/* Notifications */}
            <div className="relative">
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="relative cursor-pointer"
                onClick={handleNotificationClick}
              >
                <Bell className="h-6 w-6 text-gray-600 hover:text-pink-600 transition-colors" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -left-1 bg-pink-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </span>
                )}
              </motion.div>

              {/* Notifications Dropdown */}
              <NotificationsDropdown
                isOpen={showNotifications}
                notifications={notifications}
                onClose={() => setShowNotifications(false)}
                onMarkAsRead={markNotificationAsRead}
                onMarkAllAsRead={markAllAsRead}
              />
            </div>

            {/* User Profile */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleProfileClick}
              className="flex items-center gap-2 cursor-pointer"
            >
              {profileImage ? (
                <div className="w-8 h-8 rounded-full overflow-hidden">
                  <img
                    src={profileImage}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="w-5 h-5 text-gray-500" />
                </div>
              )}
              <span className="text-gray-700 hover:text-pink-600 transition-colors">
                {user?.user_metadata?.username}
              </span>
            </motion.div>

            {/* Sign Out */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleSignOut}
              className="flex items-center gap-2 text-gray-600 hover:text-pink-600 transition-colors"
            >
              <LogOut className="h-5 w-5" />
              <span>התנתק</span>
            </motion.button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-gray-600 hover:text-pink-600 transition-colors"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-gray-100"
          >
            <div className="px-4 py-2 space-y-1">
              <div
                onClick={handleProfileClick}
                className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
              >
                {profileImage ? (
                  <div className="w-8 h-8 rounded-full overflow-hidden">
                    <img
                      src={profileImage}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="w-5 h-5 text-gray-500" />
                  </div>
                )}
                <span className="text-gray-700">{user?.user_metadata?.username}</span>
              </div>

              {isAdmin && (
                <button
                  onClick={handleAdminClick}
                  className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-purple-600"
                >
                  <Shield className="h-5 w-5" />
                  <span>ניהול</span>
                </button>
              )}

              <button
                onClick={navigateToVip}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-yellow-600"
              >
                <Crown className="h-5 w-5" />
                <span>מנוי VIP</span>
              </button>

              <button
                onClick={() => {
                  handleNotificationClick();
                  setIsMenuOpen(false);
                }}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600 relative"
              >
                <Bell className="h-5 w-5" />
                <span>התראות</span>
                {unreadCount > 0 && (
                  <span className="bg-pink-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </span>
                )}
              </button>

              <button
                onClick={handleSignOut}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600"
              >
                <LogOut className="h-5 w-5" />
                <span>התנתק</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}

export default Header;