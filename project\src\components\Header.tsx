import React, { useEffect, useState } from 'react';
import { Bell, Heart, Shield, LogOut, User, Menu, X, Crown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAdmin } from '../hooks/useAdmin';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from '../hooks/useNavigate';
import { supabase } from '../lib/supabase';

function Header() {
  const isAdmin = useAdmin();
  const { user, signOut } = useAuth();
  const { navigateToHome, navigateToProfile, navigateToAdmin } = useNavigate();

  // Add a function to navigate to VIP page
  const navigateToVip = () => {
    // Access the App component's setCurrentPage function
    window.dispatchEvent(new CustomEvent('navigation', { detail: { page: 'vip' } }));
  };
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const fetchProfileImage = async () => {
      if (!user) return;

      try {
        // First check if user has a profile_image_url in metadata
        if (user.user_metadata?.profile_image_url) {
          setProfileImage(user.user_metadata.profile_image_url);
          return;
        }

        // Fallback to first image in storage if no profile image is set
        const { data: photos } = await supabase.storage
          .from('photos')
          .list(user.id + '/', {
            limit: 1,
            sortBy: { column: 'name', order: 'asc' },
          });

        if (photos && photos.length > 0) {
          const { data: { publicUrl } } = supabase.storage
            .from('photos')
            .getPublicUrl(`${user.id}/${photos[0].name}`);
          setProfileImage(publicUrl);
        }
      } catch (error) {
        console.error('Error fetching profile image:', error);
      }
    };

    const fetchLogo = async () => {
      try {
        const { data: { publicUrl } } = supabase.storage
          .from('site-assets')
          .getPublicUrl('logo.png');

        const response = await fetch(publicUrl, { method: 'HEAD' });
        if (response.ok) {
          setLogoUrl(publicUrl);
        }
      } catch (error) {
        console.error('Error fetching logo:', error);
      }
    };

    fetchProfileImage();
    fetchLogo();
  }, [user]);

  const handleSignOut = async () => {
    await signOut();
    setIsMenuOpen(false);
  };

  const handleProfileClick = () => {
    // Pass null explicitly to indicate we want to view the current user's profile
    navigateToProfile(null);
    setIsMenuOpen(false);
  };

  const handleAdminClick = () => {
    navigateToAdmin();
    setIsMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-40">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center cursor-pointer"
            onClick={navigateToHome}
          >
            {logoUrl ? (
              <img
                src={logoUrl}
                alt="Logo"
                className="h-8 w-auto"
              />
            ) : (
              <Heart className="h-8 w-8 text-pink-600" />
            )}
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            {/* Admin Icon */}
            {isAdmin && (
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="cursor-pointer"
                onClick={navigateToAdmin}
              >
                <Shield className="h-6 w-6 text-purple-600 hover:text-purple-700 transition-colors" />
              </motion.div>
            )}

            {/* VIP Subscription */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="cursor-pointer"
              onClick={navigateToVip}
            >
              <Crown className="h-6 w-6 text-yellow-500 hover:text-yellow-600 transition-colors" />
            </motion.div>

            {/* Notifications */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="relative cursor-pointer"
            >
              <Bell className="h-6 w-6 text-gray-600 hover:text-pink-600 transition-colors" />
              <span className="absolute -top-1 -left-1 bg-pink-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                3
              </span>
            </motion.div>

            {/* User Profile */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleProfileClick}
              className="flex items-center gap-2 cursor-pointer"
            >
              {profileImage ? (
                <div className="w-8 h-8 rounded-full overflow-hidden">
                  <img
                    src={profileImage}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="w-5 h-5 text-gray-500" />
                </div>
              )}
              <span className="text-gray-700 hover:text-pink-600 transition-colors">
                {user?.user_metadata?.username}
              </span>
            </motion.div>

            {/* Sign Out */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleSignOut}
              className="flex items-center gap-2 text-gray-600 hover:text-pink-600 transition-colors"
            >
              <LogOut className="h-5 w-5" />
              <span>התנתק</span>
            </motion.button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-gray-600 hover:text-pink-600 transition-colors"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-gray-100"
          >
            <div className="px-4 py-2 space-y-1">
              <div
                onClick={handleProfileClick}
                className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
              >
                {profileImage ? (
                  <div className="w-8 h-8 rounded-full overflow-hidden">
                    <img
                      src={profileImage}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="w-5 h-5 text-gray-500" />
                  </div>
                )}
                <span className="text-gray-700">{user?.user_metadata?.username}</span>
              </div>

              {isAdmin && (
                <button
                  onClick={handleAdminClick}
                  className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-purple-600"
                >
                  <Shield className="h-5 w-5" />
                  <span>ניהול</span>
                </button>
              )}

              <button
                onClick={navigateToVip}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-yellow-600"
              >
                <Crown className="h-5 w-5" />
                <span>מנוי VIP</span>
              </button>

              <button
                onClick={handleSignOut}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600"
              >
                <LogOut className="h-5 w-5" />
                <span>התנתק</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}

export default Header;