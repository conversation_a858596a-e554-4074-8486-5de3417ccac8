-- Fix profile views policies
-- This migration adds explicit UPDATE policy for profile_views table

-- Add explicit UPDATE policy for profile_views
DO $$
BEGIN
  -- Check if "Users can update their own profile views" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'profile_views' AND policyname = 'Users can update their own profile views'
  ) THEN
    CREATE POLICY "Users can update their own profile views"
      ON profile_views
      FOR UPDATE
      TO authenticated
      USING (auth.uid() = viewer_id)
      WITH CHECK (auth.uid() = viewer_id);
  END IF;
  
  -- Check if "Users can delete their own profile views" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'profile_views' AND policyname = 'Users can delete their own profile views'
  ) THEN
    CREATE POLICY "Users can delete their own profile views"
      ON profile_views
      FOR DELETE
      TO authenticated
      USING (auth.uid() = viewer_id);
  END IF;
END
$$;

-- Fix the record_profile_view function to use explicit viewer_id and viewed_id parameters
CREATE OR REPLACE FUNCTION record_profile_view(viewer uuid, viewed uuid)
RETURNS void AS $$
DECLARE
  view_id uuid;
BEGIN
  -- Don't record if viewing own profile
  IF viewer = viewed THEN
    RETURN;
  END IF;

  -- Insert or update the profile view
  -- The ON CONFLICT clause ensures we don't get duplicate entries
  -- and updates the timestamp when a user views the same profile again
  INSERT INTO profile_views (viewer_id, viewed_id, created_at)
  VALUES (viewer, viewed, now())
  ON CONFLICT (viewer_id, viewed_id)
  DO UPDATE SET created_at = now()
  RETURNING id INTO view_id;
  
  -- Log the view for debugging
  RAISE NOTICE 'Profile view recorded: viewer % viewed % (id: %)', viewer, viewed, view_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add a direct function to insert profile views that bypasses RLS
CREATE OR REPLACE FUNCTION insert_profile_view_bypass_rls(viewer_id uuid, viewed_id uuid)
RETURNS uuid AS $$
DECLARE
  view_id uuid;
BEGIN
  -- Don't record if viewing own profile
  IF viewer_id = viewed_id THEN
    RETURN NULL;
  END IF;

  -- Insert or update the profile view
  INSERT INTO profile_views (viewer_id, viewed_id, created_at)
  VALUES (viewer_id, viewed_id, now())
  ON CONFLICT (viewer_id, viewed_id)
  DO UPDATE SET created_at = now()
  RETURNING id INTO view_id;
  
  RETURN view_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
