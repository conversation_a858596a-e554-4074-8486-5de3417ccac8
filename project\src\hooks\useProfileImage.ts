import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';

/**
 * Simple hook to manage profile image state and listen for changes
 */
export function useProfileImage(profileId?: string, profileData?: any) {
  const { user } = useAuth();
  const [profileImage, setProfileImage] = useState<string | null>(null);

  // Initialize profile image
  useEffect(() => {
    let initialImage = null;

    // If this is the current user, use their auth metadata
    if (user && profileId === user.id) {
      initialImage = user.user_metadata?.profile_image_url;
    }

    // If no image from auth, try profile data
    if (!initialImage && profileData?.user_metadata?.profile_image_url) {
      initialImage = profileData.user_metadata.profile_image_url;
    }

    // Fallback to first photo
    if (!initialImage && profileData?.photos && profileData.photos.length > 0) {
      initialImage = profileData.photos[0];
    }

    setProfileImage(initialImage);
    console.log('🎣 useProfileImage: Initialized for profile:', profileId, 'image:', initialImage);
  }, [user, profileId, profileData]);

  // Listen for profile image changes
  useEffect(() => {
    const handleProfileImageChange = (event: CustomEvent) => {
      const { userId: changedUserId, newImageUrl } = event.detail;

      // Only update if this is for the current profile
      if (changedUserId === profileId) {
        console.log('✅ useProfileImage: Updating image for profile:', profileId, 'new URL:', newImageUrl);
        setProfileImage(newImageUrl);
      }
    };

    window.addEventListener('profileImageChanged', handleProfileImageChange as EventListener);

    return () => {
      window.removeEventListener('profileImageChanged', handleProfileImageChange as EventListener);
    };
  }, [profileId]);

  return profileImage;
}
