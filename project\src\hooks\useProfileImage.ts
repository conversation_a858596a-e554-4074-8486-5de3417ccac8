import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';

/**
 * Hook to manage profile image state and listen for changes
 * This ensures all components stay in sync when profile image changes
 */
export function useProfileImage(userId?: string, initialProfileData?: any) {
  const { user } = useAuth();
  const [profileImage, setProfileImage] = useState<string | null>(null);

  // Use the provided userId or fall back to current user
  const targetUserId = userId || user?.id;

  useEffect(() => {
    // Initialize profile image
    if (user && (!userId || userId === user.id)) {
      // For current user, use their metadata
      if (user.user_metadata?.profile_image_url) {
        console.log('useProfileImage: Setting current user image:', user.user_metadata.profile_image_url);
        setProfileImage(user.user_metadata.profile_image_url);
      }
    } else if (initialProfileData) {
      // For other users, use the provided profile data
      if (initialProfileData.user_metadata?.profile_image_url) {
        console.log('useProfileImage: Setting other user image:', initialProfileData.user_metadata.profile_image_url, 'for user:', userId);
        setProfileImage(initialProfileData.user_metadata.profile_image_url);
      }
    }
  }, [user, userId, initialProfileData]);

  useEffect(() => {
    // Listen for profile image changes
    const handleProfileImageChange = (event: CustomEvent) => {
      const { userId: changedUserId, newImageUrl } = event.detail;
      console.log('useProfileImage: Received profile image change event:', changedUserId, 'target:', targetUserId);
      if (changedUserId === targetUserId) {
        console.log('useProfileImage: Profile image changed for user:', changedUserId, 'new URL:', newImageUrl);
        setProfileImage(newImageUrl);
      }
    };

    // Add event listener
    window.addEventListener('profileImageChanged', handleProfileImageChange as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('profileImageChanged', handleProfileImageChange as EventListener);
    };
  }, [targetUserId]);

  return profileImage;
}
