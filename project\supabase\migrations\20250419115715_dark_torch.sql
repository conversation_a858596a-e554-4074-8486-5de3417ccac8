/*
  # Remove Chat System

  1. Changes
    - Drop all chat-related tables and functions
    - Remove associated policies
    - Handle dependencies properly using CASCADE where needed
    
  2. Security
    - Clean removal of all chat-related security policies
*/

-- Drop policies first to avoid dependency issues
DROP POLICY IF EXISTS "Users can view their chats" ON chats;
DROP POLICY IF EXISTS "Chat participants can update chats" ON chats;
DROP POLICY IF EXISTS "Users can delete their chats" ON chats;
DROP POLICY IF EXISTS "Users can create chats" ON chats;
DROP POLICY IF EXISTS "Users can view chat participants" ON chat_participants;
DROP POLICY IF EXISTS "Allow chat participant creation" ON chat_participants;
DROP POLICY IF EXISTS "Users can view messages in their chats" ON messages;
DROP POLICY IF EXISTS "Users can send messages to their chats" ON messages;

-- Drop tables with CASCAD<PERSON> to handle any remaining dependencies
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS chat_participants CASCADE;
DROP TABLE IF EXISTS chats CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS update_chat_last_message();