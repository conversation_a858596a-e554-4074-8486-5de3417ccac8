import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Circle, MessageSquare, Gift, Crown, Users, Search, Filter, MapPin, Clock, Heart, ChevronDown, AlertTriangle, Loader2 } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';
import { useOnlineUsers } from '../hooks/useOnlineUsers';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import SendGiftDialog from './SendGiftDialog';


function OnlineFriendsPage() {
  const { user } = useAuth();
  const { navigateToProfile } = useNavigate();
  const { onlineUsers, loading, error, isRefreshing, addedUsers, removedUsers } = useOnlineUsers();
  const [initialFetchDone, setInitialFetchDone] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date());
  const userCardsRef = useRef<Map<string, HTMLDivElement>>(new Map());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isVip, setIsVip] = useState(false);
  const [isGiftDialogOpen, setIsGiftDialogOpen] = useState(false);
  const [selectedFriend, setSelectedFriend] = useState<Profile | null>(null);
  const [statusMessage, setStatusMessage] = useState<{text: string, type: 'error' | 'success'} | null>(null);
  const [creatingChat, setCreatingChat] = useState(false);

  // Check if user is VIP and track initial fetch
  useEffect(() => {
    const checkVipStatus = async () => {
      if (!user?.id) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_vip')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking VIP status:', error);
          return;
        }

        setIsVip(data?.is_vip || false);
      } catch (err) {
        console.error('Error checking VIP status:', err);
      }
    };

    checkVipStatus();
  }, [user?.id]);

  // Track when initial fetch is complete and update time
  useEffect(() => {
    if (!loading && !initialFetchDone) {
      setInitialFetchDone(true);
    }

    // Update last refresh time when users are added or removed
    if ((addedUsers.length > 0 || removedUsers.length > 0) && initialFetchDone) {
      setLastUpdateTime(new Date());
    }
  }, [loading, initialFetchDone, addedUsers, removedUsers]);

  // Handle user cards references
  const setUserCardRef = (id: string, element: HTMLDivElement | null) => {
    if (element) {
      userCardsRef.current.set(id, element);
    }
  };

  const getGenderIcon = (gender: string) => {
    switch (gender) {
      case 'male':
        return <FontAwesomeIcon icon={faMars} className="text-xl text-blue-600" />;
      case 'female':
        return <FontAwesomeIcon icon={faVenus} className="text-xl text-pink-600" />;
      case 'couple':
        return <Users className="h-5 w-5 text-purple-600" />;
      default:
        return null;
    }
  };

  const getGenderText = (gender: string) => {
    switch (gender) {
      case 'male':
        return 'גבר';
      case 'female':
        return 'אישה';
      case 'couple':
        return 'זוג';
      default:
        return '';
    }
  };

  const calculateAge = (birthDate: string) => {
    if (!birthDate) return '';

    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  const handleChat = async (friend: Profile, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (!user?.id) {
      setStatusMessage({
        text: 'עליך להתחבר כדי לשלוח הודעות',
        type: 'error'
      });

      setTimeout(() => {
        setStatusMessage(null);
      }, 5000);

      return;
    }

    if (!isVip) {
      setStatusMessage({
        text: 'רק משתמשי VIP יכולים לשלוח הודעות. שדרג לחשבון VIP כדי לשלוח הודעות.',
        type: 'error'
      });

      setTimeout(() => {
        setStatusMessage(null);
      }, 5000);

      return;
    }

    try {
      setCreatingChat(true);
      setStatusMessage({
        text: 'יוצר צ\'אט...',
        type: 'success'
      });

      console.log('Creating chat between', user.id, 'and', friend.id);

      // Check if user can send messages (is VIP)
      const { data: canSendMessages, error: vipCheckError } = await supabase.rpc(
        'can_user_send_messages',
        { user_id: user.id }
      );

      if (vipCheckError) {
        console.error('Error checking VIP status:', vipCheckError);
        throw new Error('Failed to verify VIP status');
      }

      if (!canSendMessages) {
        setStatusMessage({
          text: 'רק משתמשי VIP יכולים לשלוח הודעות. שדרג לחשבון VIP כדי לשלוח הודעות.',
          type: 'error'
        });
        return;
      }

      // Create a chat between the current user and the selected friend
      const { data: chatId, error: chatError } = await supabase.rpc(
        'create_chat_between_users',
        {
          user1_id: user.id,
          user2_id: friend.id
        }
      );

      if (chatError) {
        console.error('Error creating chat:', chatError);
        throw new Error('Failed to create chat');
      }

      if (!chatId) {
        throw new Error('No chat ID returned');
      }

      console.log('Created chat with ID:', chatId);

      // Navigate to the home page with the messages tab active
      window.location.href = '/?tab=הודעות';

      // Set a timeout to allow the home page to load before showing success message
      setTimeout(() => {
        setStatusMessage({
          text: 'הצ\'אט נוצר בהצלחה!',
          type: 'success'
        });

        setTimeout(() => {
          setStatusMessage(null);
        }, 5000);
      }, 1000);
    } catch (err) {
      console.error('Error in handleChat:', err);
      setStatusMessage({
        text: 'אירעה שגיאה ביצירת הצ\'אט. אנא נסה שוב מאוחר יותר.',
        type: 'error'
      });

      setTimeout(() => {
        setStatusMessage(null);
      }, 5000);
    } finally {
      setCreatingChat(false);
    }
  };

  const handleGift = (friend: Profile, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedFriend(friend);
    setIsGiftDialogOpen(true);
  };

  const getTimeAgo = (lastActive: string) => {
    if (!lastActive) return 'לא ידוע';

    const now = new Date();
    const lastActiveDate = new Date(lastActive);
    const diffMs = now.getTime() - lastActiveDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'עכשיו';
    if (diffMins < 60) return `לפני ${diffMins} דקות`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `לפני ${diffHours} שעות`;

    const diffDays = Math.floor(diffHours / 24);
    return `לפני ${diffDays} ימים`;
  };

  // Filter users based on search and gender
  const filteredUsers = onlineUsers.filter(user => {
    const matchesSearch = !searchTerm ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.city?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesGender = !selectedGender || user.gender === selectedGender;

    return matchesSearch && matchesGender;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl overflow-hidden">
        <div className="absolute inset-0 bg-pattern opacity-10"></div>
        <div className="relative px-6 py-10 md:px-12 md:py-12 text-white">
          <div className="max-w-4xl">
            <h1 className="text-3xl md:text-4xl font-bold mb-2">חברים מחוברים עכשיו</h1>
            <div className="inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl">
              <Users className="w-5 h-5" />
              <span className="font-medium">
                {loading ? 'טוען...' : `${filteredUsers.length} משתמשים מחוברים`}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Status Message */}
      <AnimatePresence>
        {statusMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={`rounded-xl p-4 flex items-center gap-3 ${
              statusMessage.type === 'error'
                ? 'bg-red-50 text-red-700 border border-red-200'
                : 'bg-green-50 text-green-700 border border-green-200'
            }`}
          >
            {statusMessage.type === 'error' ? (
              <AlertTriangle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <Circle className="w-5 h-5 flex-shrink-0" />
            )}
            <p>{statusMessage.text}</p>
          </motion.div>
        )}
      </AnimatePresence>



      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm p-5">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="חיפוש לפי שם או עיר..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-xl focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
            />
          </div>

          {/* Filter Button */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-between gap-2 px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors md:w-48"
          >
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">סינון</span>
            </div>
            <ChevronDown className={`w-5 h-5 text-gray-500 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters Dropdown */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="mt-4 pt-4 border-t border-gray-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">מגדר</label>
                  <select
                    value={selectedGender}
                    onChange={(e) => setSelectedGender(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="">כל המגדרים</option>
                    <option value="male">גברים</option>
                    <option value="female">נשים</option>
                    <option value="couple">זוגות</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Results Stats */}
      <div className="flex items-center justify-between">
        <div className="text-gray-600">
          {loading && !initialFetchDone ? (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>טוען משתמשים...</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span>מציג {filteredUsers.length} משתמשים מחוברים</span>
              {isRefreshing && (
                <Loader2 className="w-3 h-3 animate-spin text-gray-400 ml-2" />
              )}
            </div>
          )}
        </div>
        <div className="text-gray-500 text-sm flex items-center">
          <span>עודכן: {lastUpdateTime.toLocaleTimeString('he-IL')}</span>
          {isRefreshing && (
            <div className="ml-2 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
          )}
        </div>
      </div>

      {/* Loading State - only show on initial load */}
      {loading && !initialFetchDone && (
        <div className="flex items-center justify-center py-8">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        </div>
      )}

      {/* Error State */}
      {!loading && error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <p className="text-red-700">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            נסה שוב
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredUsers.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm p-4 text-center">
          <p className="text-gray-600">אין משתמשים מחוברים כרגע</p>
          {selectedGender && (
            <button
              onClick={() => setSelectedGender('')}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              נקה סינון
            </button>
          )}
        </div>
      )}

      {/* User Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {!loading && !error && filteredUsers.map((friend) => (
            <div
              key={friend.id}
              ref={(el) => setUserCardRef(friend.id, el as HTMLDivElement)}
              className="user-card-container"
            >
              <motion.div
                initial={false}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, transition: { duration: 0.2 } }}
                transition={{ duration: 0.3 }}
                layout="position"
                whileHover={{ scale: 1.02 }}
                className={`bg-white rounded-xl shadow-sm overflow-hidden border hover:shadow-md transition-all ${
                  addedUsers.some(u => u.id === friend.id)
                    ? 'border-green-200 animate-pulse-once'
                    : 'border-gray-100'
                }`}
            >
              {/* Profile Image */}
              <div className="relative aspect-[4/3]">
                {friend.user_metadata?.profile_image_url ? (
                  <img
                    src={friend.user_metadata.profile_image_url}
                    alt={friend.username}
                    className="w-full h-full object-cover"
                    onClick={() => navigateToProfile(friend)}
                  />
                ) : friend.photos && friend.photos.length > 0 ? (
                  <img
                    src={friend.photos[0]}
                    alt={friend.username}
                    className="w-full h-full object-cover"
                    onClick={() => navigateToProfile(friend)}
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <Users className="w-12 h-12 text-gray-400" />
                  </div>
                )}

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent"></div>

                {/* Online Status */}
                <div className="absolute top-3 right-3 flex items-center gap-2 bg-green-500 text-white px-2 py-1 rounded-full text-sm shadow-lg">
                  <Circle className="w-2 h-2 fill-current animate-pulse" />
                  <span>מחובר/ת</span>
                </div>

                {/* Last Active */}
                <div className="absolute top-3 left-3 flex items-center gap-1.5 bg-black/40 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs">
                  <Clock className="w-3 h-3" />
                  <span>{getTimeAgo(friend.last_active)}</span>
                </div>

                {/* VIP Badge */}
                {friend.is_vip && (
                  <div className="absolute top-12 right-3 bg-gradient-to-r from-yellow-500 to-amber-500 text-white px-2 py-1 rounded-full flex items-center gap-1 shadow-lg">
                    <Crown className="w-3 h-3" />
                    <span className="text-xs font-medium">VIP</span>
                  </div>
                )}

                {/* Profile Info Overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-white drop-shadow-sm">{friend.username}</h3>
                    <div className="flex items-center gap-1.5">
                      <div className="bg-white/15 backdrop-blur-sm px-2 py-0.5 rounded-full flex items-center gap-1">
                        {getGenderIcon(friend.gender)}
                        <span className="text-white text-xs">{getGenderText(friend.gender)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-1.5 text-white/90 text-sm mb-3">
                    <MapPin className="w-3.5 h-3.5" />
                    <span>{friend.city || 'לא צוין'}</span>
                    <span className="mx-1">•</span>
                    <span>
                      {friend.gender === 'couple'
                        ? `${calculateAge(friend.birth_date)}/${calculateAge(friend.partner_birth_date || '')}`
                        : calculateAge(friend.birth_date)
                      }
                    </span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={(e) => handleChat(friend, e)}
                      disabled={creatingChat}
                      className={`flex-1 flex items-center justify-center gap-1.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 rounded-lg hover:opacity-90 transition-colors relative ${
                        creatingChat ? 'opacity-70 cursor-not-allowed' : ''
                      }`}
                    >
                      {creatingChat ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <MessageSquare className="w-4 h-4" />
                      )}
                      <span>{creatingChat ? 'יוצר צ\'אט...' : 'צ\'אט'}</span>
                      {!isVip && <Crown className="absolute -top-1 -right-1 w-3.5 h-3.5 text-yellow-400" />}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={(e) => handleGift(friend, e)}
                      className="w-10 flex items-center justify-center p-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:opacity-90 transition-colors"
                    >
                      <Gift className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-10 flex items-center justify-center p-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      <Heart className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
            </div>
          ))}
      </div>

      {/* Gift Dialog */}
      {isGiftDialogOpen && selectedFriend && (
        <SendGiftDialog
          isOpen={isGiftDialogOpen}
          onClose={() => setIsGiftDialogOpen(false)}
          receiverId={selectedFriend.id}
          receiverName={selectedFriend.username}
        />
      )}
    </div>
  );
}

export default OnlineFriendsPage;