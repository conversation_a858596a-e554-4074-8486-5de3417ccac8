import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Circle, MessageSquare, Gift, Crown, Users, Search, Filter, MapPin, Clock, Heart, ChevronDown, AlertTriangle, Loader2 } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';
import { useOnlineUsers } from '../hooks/useOnlineUsers';
import { useGlobalProfileImage } from '../hooks/useGlobalProfileImage';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import SendGiftDialog from './SendGiftDialog';
import MiniUserCard from './MiniUserCard';

// Using MiniUserCard component now

function OnlineFriendsPage() {
  const { user } = useAuth();
  const { navigateToProfile } = useNavigate();
  const { onlineUsers, loading, error, isRefreshing, addedUsers, removedUsers } = useOnlineUsers();
  const [initialFetchDone, setInitialFetchDone] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date());
  const userCardsRef = useRef<Map<string, HTMLDivElement>>(new Map());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isVip, setIsVip] = useState(false);
  // Gift dialog and chat states removed - handled by MiniUserCard now

  // Check if user is VIP and track initial fetch
  useEffect(() => {
    const checkVipStatus = async () => {
      if (!user?.id) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_vip')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking VIP status:', error);
          return;
        }

        setIsVip(data?.is_vip || false);
      } catch (err) {
        console.error('Error checking VIP status:', err);
      }
    };

    checkVipStatus();
  }, [user?.id]);

  // Track when initial fetch is complete and update time
  useEffect(() => {
    if (!loading && !initialFetchDone) {
      setInitialFetchDone(true);
    }

    // Update last refresh time when users are added or removed
    if ((addedUsers.length > 0 || removedUsers.length > 0) && initialFetchDone) {
      setLastUpdateTime(new Date());
    }
  }, [loading, initialFetchDone, addedUsers, removedUsers]);

  // Handle user cards references
  const setUserCardRef = (id: string, element: HTMLDivElement | null) => {
    if (element) {
      userCardsRef.current.set(id, element);
    }
  };

  // Helper functions removed - using MiniUserCard now

  // Chat function removed - handled by MiniUserCard now

  // Gift function removed - handled by MiniUserCard now

  // Time ago function removed - not needed with MiniUserCard

  // Manual debug function
  const debugOnlineUsers = async () => {
    try {
      console.log('🔍 Manual debug check...');

      // Check all profiles
      const { data: allProfiles, error: allError } = await supabase
        .from('profiles')
        .select('id, username, is_online, last_active')
        .limit(10);

      console.log('All profiles (first 10):', allProfiles);

      // Check online profiles
      const { data: onlineProfiles, error: onlineError } = await supabase
        .from('profiles')
        .select('id, username, is_online, last_active')
        .eq('is_online', true);

      console.log('Online profiles:', onlineProfiles);

      // Check with time threshold
      const now = new Date();
      const activeThreshold = new Date(now.getTime() - 5 * 60 * 1000).toISOString();

      const { data: activeProfiles, error: activeError } = await supabase
        .from('profiles')
        .select('id, username, is_online, last_active')
        .eq('is_online', true)
        .gt('last_active', activeThreshold);

      console.log('Active profiles (last 5 min):', activeProfiles);
      console.log('Active threshold:', activeThreshold);

      // Check current user
      if (user?.id) {
        const { data: currentUser, error: currentError } = await supabase
          .from('profiles')
          .select('id, username, is_online, last_active')
          .eq('id', user.id)
          .single();

        console.log('Current user:', currentUser);
      }

    } catch (error) {
      console.error('Debug error:', error);
    }
  };

  // Filter users based on search and gender
  const filteredUsers = onlineUsers.filter(user => {
    const matchesSearch = !searchTerm ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.city?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesGender = !selectedGender || user.gender === selectedGender;

    return matchesSearch && matchesGender;
  });

  // Debug information
  console.log('🔍 OnlineFriendsPage Debug:', {
    onlineUsers: onlineUsers.length,
    filteredUsers: filteredUsers.length,
    loading,
    error,
    initialFetchDone,
    isRefreshing,
    searchTerm,
    selectedGender
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl overflow-hidden">
        <div className="absolute inset-0 bg-pattern opacity-10"></div>
        <div className="relative px-6 py-10 md:px-12 md:py-12 text-white">
          <div className="max-w-4xl">
            <h1 className="text-3xl md:text-4xl font-bold mb-2">חברים מחוברים עכשיו</h1>
            <div className="inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl">
              <Users className="w-5 h-5" />
              <span className="font-medium">
                {loading ? 'טוען...' : `${filteredUsers.length} משתמשים מחוברים`}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Status Message removed - not needed with MiniUserCard */}



      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm p-5">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="חיפוש לפי שם או עיר..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-xl focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
            />
          </div>

          {/* Filter Button */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-between gap-2 px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors md:w-48"
          >
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-gray-500" />
              <span className="text-gray-700">סינון</span>
            </div>
            <ChevronDown className={`w-5 h-5 text-gray-500 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters Dropdown */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="mt-4 pt-4 border-t border-gray-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">מגדר</label>
                  <select
                    value={selectedGender}
                    onChange={(e) => setSelectedGender(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="">כל המגדרים</option>
                    <option value="male">גברים</option>
                    <option value="female">נשים</option>
                    <option value="couple">זוגות</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Results Stats */}
      <div className="flex items-center justify-between">
        <div className="text-gray-600">
          {loading && !initialFetchDone ? (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>טוען משתמשים...</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span>מציג {filteredUsers.length} משתמשים מחוברים</span>
              {isRefreshing && (
                <Loader2 className="w-3 h-3 animate-spin text-gray-400 ml-2" />
              )}
            </div>
          )}
        </div>
        <div className="text-gray-500 text-sm flex items-center">
          <span>עודכן: {lastUpdateTime.toLocaleTimeString('he-IL')}</span>
          {isRefreshing && (
            <div className="ml-2 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
          )}
        </div>
      </div>

      {/* Loading State - only show on initial load */}
      {loading && !initialFetchDone && (
        <div className="flex items-center justify-center py-8">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        </div>
      )}

      {/* Error State */}
      {!loading && error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <p className="text-red-700">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            נסה שוב
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredUsers.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm p-4 text-center">
          <p className="text-gray-600">אין משתמשים מחוברים כרגע</p>
          <div className="mt-2 text-sm text-gray-500">
            <p>סה"כ משתמשים: {onlineUsers.length}</p>
            <p>משתמשים מסוננים: {filteredUsers.length}</p>
            <p>טוען: {loading ? 'כן' : 'לא'}</p>
            <p>שגיאה: {error || 'אין'}</p>
          </div>
          <div className="flex gap-2 justify-center mt-3">
            {selectedGender && (
              <button
                onClick={() => setSelectedGender('')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                נקה סינון
              </button>
            )}
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              רענן דף
            </button>
            <button
              onClick={debugOnlineUsers}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              בדוק נתונים
            </button>
          </div>
        </div>
      )}

      {/* User Cards Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3 md:gap-4">
          {!loading && !error && filteredUsers.map((friend) => (
            <motion.div
              key={friend.id}
              ref={(el) => setUserCardRef(friend.id, el as HTMLDivElement)}
              initial={false}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, transition: { duration: 0.2 } }}
              transition={{ duration: 0.3 }}
              layout="position"
              className={`user-card-container ${
                addedUsers.some(u => u.id === friend.id)
                  ? 'animate-pulse-once'
                  : ''
              }`}
            >
              <MiniUserCard profile={friend} />
            </motion.div>

          ))}
      </div>

      {/* Gift Dialog removed - handled by MiniUserCard now */}
    </div>
  );
}

export default OnlineFriendsPage;