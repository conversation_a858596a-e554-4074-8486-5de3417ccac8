import { useState, useEffect, useRef } from 'react';
import { supabase } from '../lib/supabase';
import { Profile } from '../types/supabase';

export function useOnlineUsers() {
  const [onlineUsers, setOnlineUsers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialFetchDone, setInitialFetchDone] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const previousUsersRef = useRef<Profile[]>([]);
  const [addedUsers, setAddedUsers] = useState<Profile[]>([]);
  const [removedUsers, setRemovedUsers] = useState<string[]>([]);
  const usersMapRef = useRef<Map<string, Profile>>(new Map());

  // Initialize users map on mount
  useEffect(() => {
    // Initialize the users map with any existing users
    const map = new Map<string, Profile>();
    onlineUsers.forEach(user => {
      map.set(user.id, user);
    });
    usersMapRef.current = map;
  }, []);

  useEffect(() => {
    const fetchOnlineUsers = async () => {
      try {
        // Only show loading indicator on initial fetch
        if (!initialFetchDone) {
          setLoading(true);
        } else {
          // For subsequent fetches, just set refreshing state
          setIsRefreshing(true);
        }
        setError(null);

        // Store current users for reference
        previousUsersRef.current = onlineUsers;

        // Get current timestamp
        const now = new Date();
        // Consider users active if they were active in the last 30 minutes (for testing)
        const activeThreshold = new Date(now.getTime() - 30 * 60 * 1000).toISOString();

        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('*')
          .eq('is_online', true)
          .gt('last_active', activeThreshold)
          .order('last_active', { ascending: false });

        if (profilesError) throw profilesError;

        // Debug information
        console.log('🔍 useOnlineUsers Debug:', {
          totalProfiles: profiles?.length || 0,
          activeThreshold,
          rawProfiles: profiles?.slice(0, 3) // First 3 for debugging
        });

        // Filter out any profiles where is_online might be null/undefined
        const onlineProfiles = (profiles || []).filter(profile => profile.is_online === true);

        console.log('🔍 Filtered online profiles:', onlineProfiles.length);

        // Process profiles to add photos
        const profilesWithPhotos = await Promise.all(onlineProfiles.map(async (profile) => {
          try {
            // Check if user has a profile image set in metadata
            if (profile.user_metadata?.profile_image_url) {
              return { ...profile, photos: [profile.user_metadata.profile_image_url] };
            }

            // Fallback to first image in storage
            const { data: photos, error: photosError } = await supabase.storage
              .from('photos')
              .list(profile.id + '/', {
                limit: 1,
                sortBy: { column: 'name', order: 'asc' },
              });

            if (photosError) {
              console.error('Error fetching photos:', photosError);
              return { ...profile, photos: [] };
            }

            if (!photos || photos.length === 0) {
              return { ...profile, photos: [] };
            }

            const { data } = supabase.storage
              .from('photos')
              .getPublicUrl(`${profile.id}/${photos[0].name}`);

            const publicUrl = data.publicUrl;

            return { ...profile, photos: [publicUrl] };
          } catch (error) {
            console.error('Error processing photos:', error);
            return { ...profile, photos: [] };
          }
        }));

        // Create maps for efficient lookups
        const previousUsersMap = new Map(
          previousUsersRef.current.map(user => [user.id, user])
        );

        const newUsersMap = new Map(
          profilesWithPhotos.map(user => [user.id, user])
        );

        // Find users that are new (not in previous list)
        const newUsers = profilesWithPhotos.filter(
          user => !previousUsersMap.has(user.id)
        );

        // Find users that are gone (in previous list but not in new list)
        const goneUsers = previousUsersRef.current.filter(
          user => !newUsersMap.has(user.id)
        );

        // Find users that exist in both lists (need to be updated)
        const existingUsers = profilesWithPhotos.filter(
          user => previousUsersMap.has(user.id)
        );

        // Track added and removed users for animations
        if (newUsers.length > 0) {
          setAddedUsers(newUsers.map(user => ({...user, isNew: true})));
        } else {
          setAddedUsers([]);
        }

        if (goneUsers.length > 0) {
          setRemovedUsers(goneUsers.map(user => user.id));
        } else {
          setRemovedUsers([]);
        }

        // If there are no changes, don't update the state
        if (newUsers.length === 0 && goneUsers.length === 0 && existingUsers.length === previousUsersRef.current.length) {
          return;
        }

        // Update the users map with the full refresh data
        const usersMap = usersMapRef.current;

        // First, mark all existing users as not new
        usersMap.forEach(user => {
          if (user.isNew) {
            usersMap.set(user.id, {...user, isNew: false});
          }
        });

        // Update existing users and add new ones
        profilesWithPhotos.forEach(newUser => {
          const existingUser = usersMap.get(newUser.id);

          if (existingUser) {
            // Preserve photos to prevent flickering
            usersMap.set(newUser.id, {
              ...newUser,
              photos: newUser.photos.length > 0 ? newUser.photos : existingUser.photos,
              isNew: false
            });
          } else {
            // Add new user
            usersMap.set(newUser.id, {
              ...newUser,
              isNew: true
            });
          }
        });

        // Remove users that are no longer online
        const currentIds = new Set(profilesWithPhotos.map(user => user.id));
        const idsToRemove: string[] = [];

        usersMap.forEach((user, id) => {
          if (!currentIds.has(id)) {
            idsToRemove.push(id);
          }
        });

        idsToRemove.forEach(id => {
          usersMap.delete(id);
        });

        // Create the updated list from the map
        const updatedUsers = Array.from(usersMap.values());

        // Set the updated list
        setOnlineUsers(updatedUsers);

        // Mark initial fetch as done
        if (!initialFetchDone) {
          setInitialFetchDone(true);
        }
      } catch (err) {
        console.error('Error fetching online users:', err);
        setError('Failed to load online users');
      } finally {
        setLoading(false);
        setIsRefreshing(false);
      }
    };

    // Function to silently refresh data without showing loading indicators
    const silentRefresh = async () => {
      try {
        setError(null);
        previousUsersRef.current = onlineUsers;

        const now = new Date();
        const activeThreshold = new Date(now.getTime() - 5 * 60 * 1000).toISOString();

        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('*')
          .eq('is_online', true)
          .gt('last_active', activeThreshold)
          .order('last_active', { ascending: false });

        if (profilesError) throw profilesError;

        const onlineProfiles = (profiles || []).filter(profile => profile.is_online === true);

        // Create maps for efficient lookups
        const existingUsersMap = new Map(
          onlineUsers.map(user => [user.id, user])
        );

        const newUsersMap = new Map(
          onlineProfiles.map(user => [user.id, user])
        );

        // Find new and removed users
        const newUsers = onlineProfiles.filter(profile => !existingUsersMap.has(profile.id));
        const removedUserIds = onlineUsers
          .filter(user => !newUsersMap.has(user.id))
          .map(user => user.id);

        // If no changes, don't update state
        if (newUsers.length === 0 && removedUserIds.length === 0) {
          return;
        }

        // Process new users to add photos
        const newUsersWithPhotos = await Promise.all(newUsers.map(async (profile) => {
          try {
            if (profile.user_metadata?.profile_image_url) {
              return { ...profile, photos: [profile.user_metadata.profile_image_url], isNew: true };
            }

            const { data: photos, error: photosError } = await supabase.storage
              .from('photos')
              .list(profile.id + '/', {
                limit: 1,
                sortBy: { column: 'name', order: 'asc' },
              });

            if (photosError || !photos || photos.length === 0) {
              return { ...profile, photos: [], isNew: true };
            }

            const { data } = supabase.storage
              .from('photos')
              .getPublicUrl(`${profile.id}/${photos[0].name}`);

            return { ...profile, photos: [data.publicUrl], isNew: true };
          } catch (error) {
            return { ...profile, photos: [], isNew: true };
          }
        }));

        // Update the users map with new users and mark removed users
        const usersMap = usersMapRef.current;

        // Add new users to the map
        newUsersWithPhotos.forEach(user => {
          usersMap.set(user.id, {...user, isNew: true});
        });

        // Remove disconnected users from the map
        removedUserIds.forEach(id => {
          usersMap.delete(id);
        });

        // Update added and removed users for animations
        setAddedUsers(newUsersWithPhotos);
        setRemovedUsers(removedUserIds);

        // Create updated list from the map
        const updatedUsers = Array.from(usersMap.values());

        // Only update the state if there are actual changes
        if (newUsersWithPhotos.length > 0 || removedUserIds.length > 0) {
          setOnlineUsers(updatedUsers);
        }
      } catch (err) {
        console.error('Error refreshing online users:', err);
      }
    };

    // Initial fetch
    fetchOnlineUsers();

    // Set up interval to silently refresh online users
    const silentRefreshInterval = setInterval(() => {
      silentRefresh();
    }, 10000); // Silently refresh every 10 seconds

    // Set up interval for full refresh (less frequent)
    const fullRefreshInterval = setInterval(() => {
      fetchOnlineUsers();
    }, 60000); // Full refresh every 60 seconds

    // Subscribe to presence changes
    const presenceChannel = supabase.channel('online_users');

    presenceChannel
      .on('presence', { event: 'sync' }, () => {
        fetchOnlineUsers();
      })
      .on('presence', { event: 'join' }, () => {
        fetchOnlineUsers();
      })
      .on('presence', { event: 'leave' }, () => {
        fetchOnlineUsers();
      })
      .subscribe();

    // Set up realtime subscription for profile changes
    const profilesSubscription = supabase
      .channel('profiles_changes')
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'profiles',
          filter: 'is_online=eq.true'
        },
        () => {
          fetchOnlineUsers();
        }
      )
      .subscribe();

    return () => {
      clearInterval(silentRefreshInterval);
      clearInterval(fullRefreshInterval);
      presenceChannel.unsubscribe();
      profilesSubscription.unsubscribe();
    };
  }, []);

  return { onlineUsers, loading, error, isRefreshing, addedUsers, removedUsers };
}