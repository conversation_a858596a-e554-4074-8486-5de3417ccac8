import React, { useState } from 'react';
import { MapPin, Gift, Crown, Users, Calendar, Heart, Timer, MessageCircle } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';
import { useSimpleProfileImage } from '../hooks/useSimpleProfileImage';
import { Profile } from '../types/supabase';
import SendGiftDialog from './SendGiftDialog';

interface HorizontalUserCardProps {
  profile: Profile;
}

function HorizontalUserCard({ profile }: HorizontalUserCardProps) {
  const { navigateToProfile, navigateToChat } = useNavigate();
  const { profileImage: currentProfileImage } = useSimpleProfileImage(profile.id);
  const [showGiftDialog, setShowGiftDialog] = useState(false);
  const [isNavigatingToChat, setIsNavigatingToChat] = useState(false);

  // Fallback to profile photos if no profile image is set
  const displayImage = currentProfileImage || 
    (profile.user_metadata?.profile_image_url) || 
    (profile.photos && profile.photos.length > 0 ? profile.photos[0] : null);

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const handleCardClick = async () => {
    try {
      await navigateToProfile(profile);
    } catch (error) {
      console.error('Error navigating to profile:', error);
    }
  };

  const handleGiftClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('🎁 Gift button clicked for:', profile.username);
    setShowGiftDialog(true);
  };

  const handleMessageClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('💬 Message button clicked for:', profile.username);

    setIsNavigatingToChat(true);

    try {
      // Navigate to chat with this user
      await navigateToChat(profile.id);
      console.log('✅ Successfully navigated to chat with:', profile.username);
    } catch (error) {
      console.error('❌ Error navigating to chat:', error);
      alert('שגיאה בפתיחת הצ\'אט. נסה שוב.');
      setIsNavigatingToChat(false);
    }
  };

  return (
    <>
      <div
        onClick={handleCardClick}
        className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden cursor-pointer hover:shadow-md hover:scale-[1.01] transition-all duration-200"
      >
        <div className="flex">
          {/* Profile Image */}
          <div className="relative w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 flex-shrink-0">
            {displayImage ? (
              <img
                src={displayImage}
                alt={profile.username}
                className="w-full h-full object-cover"
                loading="lazy"
                onLoad={() => console.log('HorizontalUserCard: Image loaded:', displayImage)}
                onError={() => console.log('HorizontalUserCard: Image failed to load:', displayImage)}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Users className="w-8 h-8 text-gray-400" />
              </div>
            )}
            
            {/* VIP Badge */}
            {profile.is_vip && (
              <div className="absolute top-2 left-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                  <Crown className="w-3 h-3" />
                  VIP
                </div>
              </div>
            )}

            {/* Online Status */}
            {profile.is_online && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 p-4 flex flex-col justify-between">
            <div>
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-bold text-lg text-gray-900 mb-1">{profile.username}</h3>
                  <div className="flex items-center gap-3 text-sm text-gray-600">
                    {profile.birth_date && (
                      <span className="flex items-center gap-1">
                        {calculateAge(profile.birth_date)} שנים
                      </span>
                    )}
                    {profile.gender && (
                      <FontAwesomeIcon 
                        icon={profile.gender === 'male' ? faMars : faVenus} 
                        className="w-4 h-4 text-blue-500"
                      />
                    )}
                    {profile.city && (
                      <span className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {profile.city}
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={handleMessageClick}
                    disabled={isNavigatingToChat}
                    className={`bg-blue-500 hover:bg-blue-600 hover:scale-110 text-white p-2 rounded-full transition-all disabled:opacity-50 disabled:cursor-not-allowed ${
                      isNavigatingToChat ? 'animate-pulse' : ''
                    }`}
                    title={isNavigatingToChat ? 'פותח צ\'אט...' : 'שלח הודעה'}
                  >
                    <MessageCircle className="w-4 h-4" />
                  </button>
                  <button
                    onClick={handleGiftClick}
                    className="bg-pink-500 hover:bg-pink-600 hover:scale-110 text-white p-2 rounded-full transition-all"
                    title="שלח מתנה"
                  >
                    <Gift className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* What we're looking for */}
              {(() => {
                // Try multiple sources for looking_for data
                const lookingFor = profile.looking_for ||
                                  profile.user_metadata?.profile_data?.lookingFor ||
                                  profile.profile_data?.lookingFor;

                console.log('🔍 HorizontalUserCard looking_for debug:', {
                  username: profile.username,
                  direct: profile.looking_for,
                  metadata: profile.user_metadata?.profile_data?.lookingFor,
                  profile_data: profile.profile_data?.lookingFor,
                  final: lookingFor
                });

                if (!lookingFor) return null;

                return (
                  <div className="text-sm text-gray-700 bg-blue-50 px-3 py-2 rounded-lg mb-3 border border-blue-200">
                    <div className="font-medium text-blue-800 mb-1 flex items-center gap-1">
                      🔍 מה אנחנו מחפשים:
                    </div>
                    <div className="text-blue-700 leading-relaxed">
                      {lookingFor}
                    </div>
                  </div>
                );
              })()}

              {/* Status Badges */}
              <div className="flex items-center gap-2 mb-3">
                {profile.free_today && (
                  <div className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    פנוי היום
                  </div>
                )}
                {profile.is_online && (
                  <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                    מחובר עכשיו
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center gap-4">
                {profile.photos && profile.photos.length > 0 && (
                  <span>{profile.photos.length} תמונות</span>
                )}
              </div>
              
              {profile.last_seen && !profile.is_online && (
                <span className="text-gray-400">
                  נראה לאחרונה: {new Date(profile.last_seen).toLocaleDateString('he-IL')}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Gift Dialog */}
      {showGiftDialog && (
        <SendGiftDialog
          isOpen={showGiftDialog}
          onClose={() => setShowGiftDialog(false)}
          receiverId={profile.id}
          receiverName={profile.username}
        />
      )}
    </>
  );
}

export default HorizontalUserCard;
