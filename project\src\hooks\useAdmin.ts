import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';

export function useAdmin() {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setIsAdmin(false);
      setLoading(false);
      return;
    }

    try {
      // Check if user is admin by email or role
      const isAdminEmail = user.email === '<EMAIL>';

      if (isAdminEmail) {
        setIsAdmin(true);
      } else {
        // Check if user has admin role in profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (!error && data?.role === 'admin') {
          setIsAdmin(true);
        } else {
          setIsAdmin(false);
        }
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const deleteUserProfile = async (userId: string) => {
    try {
      // Delete from profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) throw profileError;

      console.log('Profile deleted successfully');
      return { error: null };
    } catch (error) {
      console.error('Error deleting profile:', error);
      return { error };
    }
  };

  const updateUserProfile = async (userId: string, updates: any) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      console.log('Profile updated successfully');
      return { error: null };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { error };
    }
  };

  const deleteUserPhoto = async (userId: string, photoUrl: string) => {
    try {
      // Get current photos
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('photos')
        .eq('id', userId)
        .single();

      if (fetchError) throw fetchError;

      // Remove photo from array
      const updatedPhotos = (profile.photos || []).filter((photo: string) => photo !== photoUrl);

      // Update profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          photos: updatedPhotos,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) throw updateError;

      console.log('Photo deleted successfully');
      return { error: null };
    } catch (error) {
      console.error('Error deleting photo:', error);
      return { error };
    }
  };

  const clearUserData = async (userId: string, dataType: 'photos' | 'profile_data' | 'all') => {
    try {
      const updates: any = {
        updated_at: new Date().toISOString()
      };

      switch (dataType) {
        case 'photos':
          updates.photos = [];
          break;
        case 'profile_data':
          updates.user_metadata = { profile_data: {} };
          break;
        case 'all':
          updates.photos = [];
          updates.user_metadata = { profile_data: {} };
          updates.birth_date = null;
          updates.partner_birth_date = null;
          break;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (error) throw error;

      console.log(`${dataType} cleared successfully`);
      return { error: null };
    } catch (error) {
      console.error(`Error clearing ${dataType}:`, error);
      return { error };
    }
  };

  return {
    isAdmin,
    loading,
    deleteUserProfile,
    updateUserProfile,
    deleteUserPhoto,
    clearUserData
  };
}