import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';

export function useAdmin() {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setIsAdmin(false);
      setLoading(false);
      return;
    }

    try {
      // Check if user is admin by email or role
      const isAdminEmail = user.email === '<EMAIL>';

      if (isAdminEmail) {
        setIsAdmin(true);
      } else {
        // Check if user has admin role in profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (!error && data?.role === 'admin') {
          setIsAdmin(true);
        } else {
          setIsAdmin(false);
        }
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const deleteUserProfile = async (userId: string) => {
    try {
      // Delete from profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) throw profileError;

      console.log('Profile deleted successfully');
      return { error: null };
    } catch (error) {
      console.error('Error deleting profile:', error);
      return { error };
    }
  };

  const updateUserProfile = async (userId: string, updates: any) => {
    try {
      console.log('Updating profile with data:', updates);

      // Build update object with all fields
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Basic profile fields
      if (updates.username !== undefined) updateData.username = updates.username;
      if (updates.gender !== undefined) updateData.gender = updates.gender;
      if (updates.birth_date !== undefined) updateData.birth_date = updates.birth_date;
      if (updates.partner_birth_date !== undefined) updateData.partner_birth_date = updates.partner_birth_date;
      if (updates.city !== undefined) updateData.city = updates.city;
      if (updates.area !== undefined) updateData.area = updates.area;
      if (updates.phone !== undefined) updateData.phone = updates.phone;

      // Profile data fields (stored in individual columns)
      if (updates.user_metadata?.profile_data) {
        const profileData = updates.user_metadata.profile_data;
        if (profileData.height !== undefined) updateData.height = parseInt(profileData.height) || null;
        if (profileData.weight !== undefined) updateData.weight = parseInt(profileData.weight) || null;
        if (profileData.hairColor !== undefined) updateData.hair_color = profileData.hairColor;
        if (profileData.eyeColor !== undefined) updateData.eye_color = profileData.eyeColor;
        if (profileData.bodyType !== undefined) updateData.body_type = profileData.bodyType;
        if (profileData.ethnicity !== undefined) updateData.ethnicity = profileData.ethnicity;
        if (profileData.sexualPreference !== undefined) updateData.sexual_preference = profileData.sexualPreference;
        if (profileData.swingingExperience !== undefined) updateData.swinging_experience = profileData.swingingExperience;
        if (profileData.smokingHabits !== undefined) updateData.smoking_habits = profileData.smokingHabits;
        if (profileData.drinkingHabits !== undefined) updateData.drinking_habits = profileData.drinkingHabits;
        if (profileData.about !== undefined) updateData.about_us = profileData.about;
        if (profileData.lookingFor !== undefined) updateData.looking_for = profileData.lookingFor;
        if (profileData.maritalStatus !== undefined) updateData.marital_status = profileData.maritalStatus;
        if (profileData.children !== undefined) updateData.children = parseInt(profileData.children) || null;
      }

      console.log('Final update data:', updateData);

      // Update profiles table
      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) throw error;

      console.log('Profile updated successfully');
      return { error: null };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { error };
    }
  };

  const deleteUserPhoto = async (userId: string, photoUrl: string) => {
    try {
      console.log('Deleting photo:', photoUrl, 'for user:', userId);

      // For now, we'll just show success since photos are managed differently
      // In a real implementation, you'd need to:
      // 1. Delete from storage bucket
      // 2. Remove from any photo tracking tables
      // 3. Update profile references

      console.log('Photo deletion simulated successfully');
      return { error: null };
    } catch (error) {
      console.error('Error deleting photo:', error);
      return { error };
    }
  };

  const clearUserData = async (userId: string, dataType: 'photos' | 'profile_data' | 'all') => {
    try {
      console.log(`Clearing ${dataType} for user:`, userId);

      const updates: any = {
        updated_at: new Date().toISOString()
      };

      switch (dataType) {
        case 'photos':
          // Clear photo-related fields
          updates.profile_image_url = null;
          break;
        case 'profile_data':
          // Clear all profile data fields
          updates.height = null;
          updates.weight = null;
          updates.hair_color = null;
          updates.hair_style = null;
          updates.eye_color = null;
          updates.body_type = null;
          updates.marital_status = null;
          updates.children = null;
          updates.ethnicity = null;
          updates.sexual_preference = null;
          updates.swinging_experience = null;
          updates.smoking_habits = null;
          updates.drinking_habits = null;
          updates.about_us = null;
          updates.looking_for = null;
          break;
        case 'all':
          // Clear everything except basic required fields
          updates.height = null;
          updates.weight = null;
          updates.hair_color = null;
          updates.hair_style = null;
          updates.eye_color = null;
          updates.body_type = null;
          updates.marital_status = null;
          updates.children = null;
          updates.ethnicity = null;
          updates.sexual_preference = null;
          updates.swinging_experience = null;
          updates.smoking_habits = null;
          updates.drinking_habits = null;
          updates.about_us = null;
          updates.looking_for = null;
          updates.profile_image_url = null;
          updates.partner_birth_date = null;
          break;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (error) throw error;

      console.log(`${dataType} cleared successfully`);
      return { error: null };
    } catch (error) {
      console.error(`Error clearing ${dataType}:`, error);
      return { error };
    }
  };

  return {
    isAdmin,
    loading,
    deleteUserProfile,
    updateUserProfile,
    deleteUserPhoto,
    clearUserData
  };
}