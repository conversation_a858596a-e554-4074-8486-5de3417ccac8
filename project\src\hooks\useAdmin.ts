import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';

export function useAdmin() {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setIsAdmin(false);
      setLoading(false);
      return;
    }

    try {
      // Check if user is admin by email or role
      const isAdminEmail = user.email === '<EMAIL>';

      if (isAdminEmail) {
        setIsAdmin(true);
      } else {
        // Check if user has admin role in profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (!error && data?.role === 'admin') {
          setIsAdmin(true);
        } else {
          setIsAdmin(false);
        }
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const deleteUserProfile = async (userId: string) => {
    try {
      // Delete from profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) throw profileError;

      console.log('Profile deleted successfully');
      return { error: null };
    } catch (error) {
      console.error('Error deleting profile:', error);
      return { error };
    }
  };

  const updateUserProfile = async (userId: string, updates: any) => {
    try {
      console.log('Updating profile with data:', updates);

      // Separate profile table fields from user_metadata
      const profileFields = {
        username: updates.username,
        gender: updates.gender,
        birth_date: updates.birth_date,
        partner_birth_date: updates.partner_birth_date,
        city: updates.city,
        area: updates.area,
        phone: updates.phone,
        updated_at: new Date().toISOString()
      };

      // Remove undefined fields
      Object.keys(profileFields).forEach(key => {
        if (profileFields[key] === undefined) {
          delete profileFields[key];
        }
      });

      // Update profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileFields)
        .eq('id', userId);

      if (profileError) throw profileError;

      // Update user_metadata if provided
      if (updates.user_metadata) {
        const { error: metadataError } = await supabase
          .from('profiles')
          .update({
            user_metadata: updates.user_metadata,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (metadataError) throw metadataError;
      }

      console.log('Profile updated successfully');
      return { error: null };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { error };
    }
  };

  const deleteUserPhoto = async (userId: string, photoUrl: string) => {
    try {
      console.log('Deleting photo:', photoUrl, 'for user:', userId);

      // Get current profile data including user_metadata
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('photos, user_metadata')
        .eq('id', userId)
        .single();

      if (fetchError) throw fetchError;

      // Remove photo from photos array
      const updatedPhotos = (profile.photos || []).filter((photo: string) => photo !== photoUrl);

      // Also remove from user_metadata.gallery_images if exists
      const userMetadata = profile.user_metadata || {};
      const updatedGalleryImages = (userMetadata.gallery_images || []).filter((photo: string) => photo !== photoUrl);

      const updatedUserMetadata = {
        ...userMetadata,
        gallery_images: updatedGalleryImages
      };

      // Update profile with both photos and user_metadata
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          photos: updatedPhotos,
          user_metadata: updatedUserMetadata,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) throw updateError;

      console.log('Photo deleted successfully');
      return { error: null };
    } catch (error) {
      console.error('Error deleting photo:', error);
      return { error };
    }
  };

  const clearUserData = async (userId: string, dataType: 'photos' | 'profile_data' | 'all') => {
    try {
      console.log(`Clearing ${dataType} for user:`, userId);

      const updates: any = {
        updated_at: new Date().toISOString()
      };

      switch (dataType) {
        case 'photos':
          updates.photos = [];
          updates.user_metadata = {
            profile_data: {},
            gallery_images: [],
            profile_image_url: null
          };
          break;
        case 'profile_data':
          // Get current user_metadata to preserve photos
          const { data: currentProfile } = await supabase
            .from('profiles')
            .select('user_metadata')
            .eq('id', userId)
            .single();

          const currentMetadata = currentProfile?.user_metadata || {};
          updates.user_metadata = {
            ...currentMetadata,
            profile_data: {}
          };
          break;
        case 'all':
          updates.photos = [];
          updates.user_metadata = {
            profile_data: {},
            gallery_images: [],
            profile_image_url: null
          };
          updates.birth_date = null;
          updates.partner_birth_date = null;
          updates.city = '';
          updates.area = '';
          updates.phone = '';
          break;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (error) throw error;

      console.log(`${dataType} cleared successfully`);
      return { error: null };
    } catch (error) {
      console.error(`Error clearing ${dataType}:`, error);
      return { error };
    }
  };

  return {
    isAdmin,
    loading,
    deleteUserProfile,
    updateUserProfile,
    deleteUserPhoto,
    clearUserData
  };
}