import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';

interface AdminAction {
  type: 'delete_profile' | 'clear_data' | 'update_profile' | 'delete_photo';
  userId: string;
  data?: any;
}

export function useAdmin() {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setIsAdmin(false);
      setLoading(false);
      return;
    }

    try {
      // Check if user is admin by email
      const isAdminEmail = user.email === '<EMAIL>';
      setIsAdmin(isAdminEmail);
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  // Execute admin action with proper error handling
  const executeAdminAction = async (action: AdminAction): Promise<{ success: boolean; message: string }> => {
    if (!isAdmin) {
      return { success: false, message: 'אין הרשאות אדמין' };
    }

    setActionLoading(true);

    try {
      switch (action.type) {
        case 'delete_profile':
          return await deleteUserProfile(action.userId);
        case 'clear_data':
          return await clearUserData(action.userId, action.data.dataType);
        case 'update_profile':
          return await updateUserProfile(action.userId, action.data);
        case 'delete_photo':
          return await deleteUserPhoto(action.userId, action.data.photoUrl);
        default:
          return { success: false, message: 'פעולה לא מוכרת' };
      }
    } finally {
      setActionLoading(false);
    }
  };

  const deleteUserProfile = async (userId: string): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('🗑️ Starting complete profile deletion for user:', userId);

      // First clear all photos from storage
      try {
        const { data: files, error: listError } = await supabase.storage
          .from('photos')
          .list(userId);

        if (!listError && files && files.length > 0) {
          const filePaths = files.map(file => `${userId}/${file.name}`);
          await supabase.storage.from('photos').remove(filePaths);
          console.log(`Deleted ${files.length} photos from storage`);
        }
      } catch (storageError) {
        console.error('Error clearing photos during profile deletion:', storageError);
      }

      // Delete from profiles table (CASCADE will handle related data)
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        console.error('❌ Profile deletion error:', profileError);
        return { success: false, message: `שגיאה במחיקת הפרופיל: ${profileError.message}` };
      }

      console.log('✅ Profile deleted completely');
      return { success: true, message: 'הפרופיל נמחק לחלוטין מהאתר' };
    } catch (error) {
      console.error('❌ Unexpected error:', error);
      return { success: false, message: 'שגיאה לא צפויה במחיקת הפרופיל' };
    }
  };

  const updateUserProfile = async (userId: string, updates: any): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('📝 Updating profile for user:', userId, updates);

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Handle basic fields
      if (updates.birth_date !== undefined) updateData.birth_date = updates.birth_date;
      if (updates.partner_birth_date !== undefined) updateData.partner_birth_date = updates.partner_birth_date;

      // Handle user_metadata
      if (updates.user_metadata) {
        updateData.user_metadata = updates.user_metadata;
      }

      console.log('Final update data:', updateData);

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        console.error('❌ Update error:', error);
        return { success: false, message: `שגיאה בעדכון: ${error.message}` };
      }

      console.log('✅ Profile updated successfully');
      return { success: true, message: 'הפרופיל עודכן בהצלחה' };
    } catch (error) {
      console.error('❌ Unexpected error:', error);
      return { success: false, message: 'שגיאה לא צפויה בעדכון הפרופיל' };
    }
  };

  const deleteUserPhoto = async (userId: string, photoUrl: string): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('🖼️ Deleting photo for user:', userId);

      // Clear profile image URL if it matches
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({
          profile_image_url: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .eq('profile_image_url', photoUrl);

      if (profileUpdateError) {
        console.error('❌ Profile update error:', profileUpdateError);
        return { success: false, message: 'שגיאה בעדכון הפרופיל' };
      }

      console.log('✅ Photo reference removed successfully');
      return { success: true, message: 'התמונה נמחקה בהצלחה' };
    } catch (error) {
      console.error('❌ Unexpected error:', error);
      return { success: false, message: 'שגיאה לא צפויה במחיקת התמונה' };
    }
  };

  const clearUserData = async (userId: string, dataType: 'photos' | 'profile_data' | 'all'): Promise<{ success: boolean; message: string }> => {
    try {
      console.log(`🧹 Clearing ${dataType} for user:`, userId);

      const updates: any = {
        updated_at: new Date().toISOString()
      };

      switch (dataType) {
        case 'photos':
          // Clear photos like user does - delete from storage and clear profile image
          try {
            // Get all files in user's folder
            const { data: files, error: listError } = await supabase.storage
              .from('photos')
              .list(userId);

            if (!listError && files && files.length > 0) {
              // Delete all files
              const filePaths = files.map(file => `${userId}/${file.name}`);
              const { error: deleteError } = await supabase.storage
                .from('photos')
                .remove(filePaths);

              if (deleteError) {
                console.error('Storage deletion error:', deleteError);
              } else {
                console.log(`Deleted ${files.length} photos from storage`);
              }
            }
          } catch (storageError) {
            console.error('Error clearing photos from storage:', storageError);
          }

          updates.profile_image_url = null;
          break;
        case 'profile_data':
          // Clear profile data - reset user_metadata
          updates.user_metadata = { profile_data: {} };
          break;
        case 'all':
          // Clear everything - photos + profile data + birth dates
          try {
            // Delete photos from storage
            const { data: files, error: listError } = await supabase.storage
              .from('photos')
              .list(userId);

            if (!listError && files && files.length > 0) {
              const filePaths = files.map(file => `${userId}/${file.name}`);
              await supabase.storage.from('photos').remove(filePaths);
              console.log(`Deleted ${files.length} photos from storage`);
            }
          } catch (storageError) {
            console.error('Error clearing photos from storage:', storageError);
          }

          updates.profile_image_url = null;
          updates.user_metadata = { profile_data: {} };
          updates.partner_birth_date = null;
          break;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (error) {
        console.error('❌ Clear data error:', error);
        return { success: false, message: `שגיאה בניקוי הנתונים: ${error.message}` };
      }

      const messages = {
        photos: 'כל התמונות נמחקו בהצלחה',
        profile_data: 'נתוני הפרופיל נוקו בהצלחה',
        all: 'כל הנתונים נוקו בהצלחה'
      };

      console.log(`✅ ${dataType} cleared successfully`);
      return { success: true, message: messages[dataType] };
    } catch (error) {
      console.error('❌ Unexpected error:', error);
      return { success: false, message: 'שגיאה לא צפויה בניקוי הנתונים' };
    }
  };

  return {
    isAdmin,
    loading,
    actionLoading,
    executeAdminAction
  };
}