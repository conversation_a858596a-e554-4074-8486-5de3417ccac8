import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Configure retry options
const retryConfig = {
  retries: 3,
  retryDelay: (retryCount: number) => Math.min(1000 * Math.pow(2, retryCount), 10000), // Exponential backoff
};

// Create Supabase client with retry configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js/2.39.7',
    },
  },
  // Add retry configuration to all requests
  db: {
    schema: 'public',
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  // Add custom fetch with timeout and retry logic
  fetch: (url, options) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    return new Promise((resolve, reject) => {
      const attemptFetch = async (attempt: number) => {
        try {
          const response = await fetch(url, {
            ...options,
            signal: controller.signal,
          });
          clearTimeout(timeoutId);
          resolve(response);
        } catch (error) {
          if (error.name === 'AbortError') {
            reject(new Error('Request timeout'));
            return;
          }

          if (attempt < retryConfig.retries) {
            const delay = retryConfig.retryDelay(attempt);
            setTimeout(() => attemptFetch(attempt + 1), delay);
          } else {
            clearTimeout(timeoutId);
            reject(error);
          }
        }
      };

      attemptFetch(0);
    });
  },
});

// Add health check function
export const checkSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase.from('profiles').select('id').limit(1);
    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Supabase connection error:', error);
    return false;
  }
};