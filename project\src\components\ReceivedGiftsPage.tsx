import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Gift,
  Heart,
  Flower,
  Wine,
  Cookie,
  CircleDot,
  Star,
  Diamond,
  Banknote,
  HeartCrack,
  ArrowRight,
  Calendar,
  User,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import Header from './Header';

interface ReceivedGift {
  id: string;
  type: string;
  title: string;
  message: string;
  created_at: string;
  read: boolean;
  data: {
    gift_name: string;
    gift_price: number;
    sender_id: string;
  };
  sender?: {
    username: string;
    profile_image_url?: string;
  };
}

// Function to get the appropriate icon component based on gift name
const getGiftIcon = (giftName: string) => {
  const name = giftName.toLowerCase();
  
  switch (name) {
    case 'לב':
    case 'heart':
      return <Heart className="w-8 h-8 text-red-500" />;
    case 'פרח':
    case 'flower':
      return <Flower className="w-8 h-8 text-pink-500" />;
    case 'שמפניה':
    case 'champagne':
    case 'wine':
      return <Wine className="w-8 h-8 text-purple-500" />;
    case 'שוקולד':
    case 'chocolate':
    case 'candy':
      return <Cookie className="w-8 h-8 text-yellow-500" />;
    case 'טבעת':
    case 'ring':
      return <CircleDot className="w-8 h-8 text-yellow-400" />;
    case 'כוכב':
    case 'star':
      return <Star className="w-8 h-8 text-yellow-400" />;
    case 'יהלום':
    case 'diamond':
      return <Diamond className="w-8 h-8 text-blue-400" />;
    case 'כסף':
    case 'money':
      return <Banknote className="w-8 h-8 text-green-500" />;
    default:
      return <Gift className="w-8 h-8 text-pink-500" />;
  }
};

function ReceivedGiftsPage() {
  const { user } = useAuth();
  const [gifts, setGifts] = useState<ReceivedGift[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchReceivedGifts();
    }
  }, [user?.id]);

  const fetchReceivedGifts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch gift notifications for the current user
      const { data: notifications, error: notificationsError } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user?.id)
        .eq('type', 'gift')
        .order('created_at', { ascending: false });

      if (notificationsError) throw notificationsError;

      // Get sender details for each gift
      const giftsWithSenders = await Promise.all(
        (notifications || []).map(async (notification) => {
          const senderId = notification.data?.sender_id;
          let sender = null;

          if (senderId) {
            const { data: senderData } = await supabase
              .from('profiles')
              .select('username, profile_image_url')
              .eq('id', senderId)
              .single();
            
            sender = senderData;
          }

          return {
            ...notification,
            sender
          };
        })
      );

      setGifts(giftsWithSenders);
    } catch (err) {
      console.error('Error fetching received gifts:', err);
      setError('אירעה שגיאה בטעינת המתנות. אנא נסה שוב מאוחר יותר.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `לפני ${diffInMinutes} דקות`;
    } else if (diffInHours < 24) {
      return `לפני ${diffInHours} שעות`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `לפני ${diffInDays} ימים`;
    }
  };

  const extractGiftMessage = (message: string) => {
    // Extract the personal message part (after the gift line)
    const lines = message.split('\n');
    if (lines.length > 2) {
      return lines.slice(2).join('\n').trim();
    }
    return '';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Gift className="w-8 h-8 text-pink-600" />
            <h1 className="text-3xl font-bold text-gray-900">מתנות שקיבלת</h1>
          </div>
          <p className="text-gray-600">כל המתנות שקיבלת ממשתמשים אחרים</p>
        </motion.div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-pink-500" />
            <span className="mr-3 text-gray-600">טוען מתנות...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchReceivedGifts}
              className="bg-pink-500 text-white px-6 py-2 rounded-lg hover:bg-pink-600"
            >
              נסה שוב
            </button>
          </div>
        ) : gifts.length === 0 ? (
          <div className="text-center py-12">
            <Gift className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">אין מתנות עדיין</h3>
            <p className="text-gray-500">כשתקבל מתנות, הן יופיעו כאן</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {gifts.map((gift) => (
              <motion.div
                key={gift.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`bg-white rounded-xl shadow-md p-6 border-r-4 ${
                  !gift.read ? 'border-pink-500 bg-pink-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-start gap-4">
                  {/* Gift Icon */}
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full flex items-center justify-center">
                      {getGiftIcon(gift.data?.gift_name || '')}
                    </div>
                  </div>

                  {/* Gift Details */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          🎁 {gift.data?.gift_name} ({gift.data?.gift_price} נקודות)
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                          <User className="w-4 h-4" />
                          <span>מאת: {gift.sender?.username || 'משתמש לא ידוע'}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(gift.created_at)}</span>
                      </div>
                    </div>

                    {/* Personal Message */}
                    {extractGiftMessage(gift.message) && (
                      <div className="bg-gray-50 rounded-lg p-3 mt-3">
                        <p className="text-gray-700 text-sm italic">
                          "{extractGiftMessage(gift.message)}"
                        </p>
                      </div>
                    )}

                    {/* Read Status */}
                    {!gift.read && (
                      <div className="flex items-center gap-2 mt-3">
                        <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                        <span className="text-sm text-pink-600 font-medium">מתנה חדשה</span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default ReceivedGiftsPage;
