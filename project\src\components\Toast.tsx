import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, AlertTriangle, Info, X, AlertCircle } from 'lucide-react';

interface ToastProps {
  id: string;
  title: string;
  status: 'success' | 'error' | 'warning' | 'info';
  isClosable?: boolean;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ id, title, status, isClosable = true, onClose }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'success':
        return {
          bg: 'bg-green-50',
          border: 'border-green-500',
          text: 'text-green-700',
          icon: <CheckCircle className="w-5 h-5 text-green-500" />
        };
      case 'error':
        return {
          bg: 'bg-red-50',
          border: 'border-red-500',
          text: 'text-red-700',
          icon: <AlertCircle className="w-5 h-5 text-red-500" />
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50',
          border: 'border-yellow-500',
          text: 'text-yellow-700',
          icon: <AlertTriangle className="w-5 h-5 text-yellow-500" />
        };
      case 'info':
      default:
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-500',
          text: 'text-blue-700',
          icon: <Info className="w-5 h-5 text-blue-500" />
        };
    }
  };

  const styles = getStatusStyles();

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`${styles.bg} ${styles.text} border-r-4 ${styles.border} p-4 rounded-lg shadow-md flex items-center justify-between mb-3`}
    >
      <div className="flex items-center">
        {styles.icon}
        <span className="mr-2 font-medium">{title}</span>
      </div>
      {isClosable && (
        <button
          onClick={() => onClose(id)}
          className="text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </motion.div>
  );
};

export const ToastContainer: React.FC<{
  toasts: Array<{ id: string; title: string; status: 'success' | 'error' | 'warning' | 'info'; isClosable?: boolean }>;
  closeToast: (id: string) => void;
}> = ({ toasts, closeToast }) => {
  return (
    <div className="fixed top-4 right-4 z-50 w-72">
      <AnimatePresence>
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            id={toast.id}
            title={toast.title}
            status={toast.status}
            isClosable={toast.isClosable}
            onClose={closeToast}
          />
        ))}
      </AnimatePresence>
    </div>
  );
};

export default Toast;
