import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { UserPlus, Mail, Lock, Phone, MapPin, Users, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../hooks/useAuth';

type Gender = 'male' | 'female' | 'couple';

interface FormData {
  firstName: string;
  email: string;
  emailConfirm: string;
  password: string;
  passwordConfirm: string;
  birthDay: string;
  birthMonth: string;
  birthYear: string;
  partnerBirthDay: string;
  partnerBirthMonth: string;
  partnerBirthYear: string;
  city: string;
  area: string;
  phone: string;
  gender: Gender | null;
  termsAccepted: boolean;
  privacyAccepted: boolean;
  newsletterSubscription: boolean;
}

interface SignupFormProps {
  onNext: (data: any) => void;
  onSignInClick: () => void;
  initialData?: any;
}

const MaleIcon = () => (
  <div className="h-8 w-8 flex items-center justify-center">
    <FontAwesomeIcon icon={faMars} style={{ color: '#2c85c9' }} className="text-2xl" />
  </div>
);

const FemaleIcon = () => (
  <div className="h-8 w-8 flex items-center justify-center">
    <FontAwesomeIcon icon={faVenus} style={{ color: '#c33fc6' }} className="text-2xl" />
  </div>
);

function SignupForm({ onNext, onSignInClick, initialData = {} }: SignupFormProps) {
  const { signUp } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    firstName: initialData.username || '',
    email: initialData.email || '',
    emailConfirm: initialData.email || '',
    password: initialData.password || '',
    passwordConfirm: initialData.password || '',
    birthDay: initialData.birthDay || '',
    birthMonth: initialData.birthMonth || '',
    birthYear: initialData.birthYear || '',
    partnerBirthDay: initialData.partnerBirthDay || '',
    partnerBirthMonth: initialData.partnerBirthMonth || '',
    partnerBirthYear: initialData.partnerBirthYear || '',
    city: initialData.city || '',
    area: initialData.area || '',
    phone: initialData.phone || '',
    gender: initialData.gender || null,
    termsAccepted: initialData.termsAccepted || false,
    privacyAccepted: initialData.privacyAccepted || false,
    newsletterSubscription: initialData.newsletterSubscription || false
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof FormData, string>>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [generalError, setGeneralError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    if (formErrors[name as keyof FormData]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
    setGeneralError(null);
  };

  const handleGenderSelect = (gender: Gender) => {
    setFormData(prev => ({ ...prev, gender }));
    if (formErrors.gender) {
      setFormErrors(prev => ({ ...prev, gender: '' }));
    }
  };

  const validateForm = () => {
    const errors: Partial<Record<keyof FormData, string>> = {};

    if (!formData.firstName) errors.firstName = 'שדה חובה';
    if (!formData.email) errors.email = 'שדה חובה';
    if (!formData.emailConfirm) errors.emailConfirm = 'שדה חובה';
    if (!formData.password) errors.password = 'שדה חובה';
    if (!formData.passwordConfirm) errors.passwordConfirm = 'שדה חובה';
    if (!formData.gender) errors.gender = 'שדה חובה';
    if (!formData.city) errors.city = 'שדה חובה';
    if (!formData.area) errors.area = 'שדה חובה';
    if (!formData.phone) errors.phone = 'שדה חובה';
    if (!formData.birthYear || !formData.birthMonth || !formData.birthDay) {
      errors.birthYear = 'תאריך לידה הוא שדה חובה';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'כתובת אימייל לא תקינה';
    }

    if (formData.email !== formData.emailConfirm) {
      errors.emailConfirm = 'כתובות האימייל אינן תואמות';
    }

    if (formData.password !== formData.passwordConfirm) {
      errors.passwordConfirm = 'הסיסמאות אינן תואמות';
    }

    if (formData.password.length < 6) {
      errors.password = 'הסיסמה חייבת להכיל לפחות 6 תווים';
    }

    if (!formData.termsAccepted) {
      errors.termsAccepted = 'יש לאשר את תנאי השימוש';
    }
    if (!formData.privacyAccepted) {
      errors.privacyAccepted = 'יש לאשר את מדיניות הפרטיות';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setGeneralError(null);

    try {
      const birthDate = `${formData.birthYear}-${formData.birthMonth.padStart(2, '0')}-${formData.birthDay.padStart(2, '0')}`;
      let partnerBirthDate;
      
      if (formData.gender === 'couple' && formData.partnerBirthYear && formData.partnerBirthMonth && formData.partnerBirthDay) {
        partnerBirthDate = `${formData.partnerBirthYear}-${formData.partnerBirthMonth.padStart(2, '0')}-${formData.partnerBirthDay.padStart(2, '0')}`;
      }

      const signUpData = {
        email: formData.email,
        password: formData.password,
        username: formData.firstName,
        gender: formData.gender,
        birthDate,
        partnerBirthDate,
        city: formData.city,
        area: formData.area,
        phone: formData.phone
      };

      await onNext(signUpData);
    } catch (error: any) {
      console.error('Signup error:', error);
      
      if (error?.message?.includes('user_already_exists') || 
          (error?.body && JSON.parse(error.body)?.code === 'user_already_exists')) {
        setGeneralError('כתובת האימייל כבר רשומה במערכת. אנא התחבר או השתמש בכתובת אימייל אחרת.');
        setFormErrors(prev => ({
          ...prev,
          email: 'כתובת האימייל כבר קיימת במערכת'
        }));
      } else {
        setGeneralError('אירעה שגיאה בתהליך ההרשמה. אנא נסה שוב מאוחר יותר.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderBirthDateSelectors = (prefix: string = '', label: string = '* תאריך לידה') => (
    <div className="flex flex-col">
      <label className="text-sm font-medium text-gray-700 mb-1">{label}</label>
      <div className="grid grid-cols-3 gap-4">
        <select
          name={`${prefix}birthYear`}
          value={formData[`${prefix}birthYear` as keyof FormData]}
          onChange={handleInputChange}
          className={`p-2 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
            formErrors[`${prefix}birthYear` as keyof FormData] ? 'border-red-500' : 'border-gray-200'
          }`}
          required
        >
          <option value="">שנה</option>
          {Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i).map(year => (
            <option key={year} value={year}>{year}</option>
          ))}
        </select>
        <select
          name={`${prefix}birthMonth`}
          value={formData[`${prefix}birthMonth` as keyof FormData]}
          onChange={handleInputChange}
          className={`p-2 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
            formErrors[`${prefix}birthMonth` as keyof FormData] ? 'border-red-500' : 'border-gray-200'
          }`}
          required
        >
          <option value="">חודש</option>
          {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
            <option key={month} value={month}>{month}</option>
          ))}
        </select>
        <select
          name={`${prefix}birthDay`}
          value={formData[`${prefix}birthDay` as keyof FormData]}
          onChange={handleInputChange}
          className={`p-2 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
            formErrors[`${prefix}birthDay` as keyof FormData] ? 'border-red-500' : 'border-gray-200'
          }`}
          required
        >
          <option value="">יום</option>
          {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
            <option key={day} value={day}>{day}</option>
          ))}
        </select>
      </div>
      {formErrors[`${prefix}birthYear` as keyof FormData] && (
        <span className="text-red-500 text-sm mt-1">{formErrors[`${prefix}birthYear` as keyof FormData]}</span>
      )}
    </div>
  );

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <div className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] p-8">
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="inline-block p-3 bg-pink-100 rounded-full mb-4"
            >
              <UserPlus className="w-8 h-8 text-pink-600" />
            </motion.div>
            <h1 className="text-2xl font-bold text-gray-900">הרשמה</h1>
            <p className="text-gray-600 mt-2">צור חשבון חדש</p>
          </div>

          {generalError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700"
            >
              <AlertCircle className="w-5 h-5" />
              <span>{generalError}</span>
            </motion.div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="relative">
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  placeholder="שם משתמש"
                  className={`w-full pr-4 pl-4 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.firstName ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                {formErrors.firstName && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.firstName}</span>
                )}
              </div>

              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="סיסמה"
                  className={`w-full pr-12 pl-10 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.password ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
                {formErrors.password && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.password}</span>
                )}
                <span className="text-xs text-gray-500 mt-1 block">(לפחות 6 תווים)</span>
              </div>

              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  name="passwordConfirm"
                  value={formData.passwordConfirm}
                  onChange={handleInputChange}
                  placeholder="אימות סיסמה"
                  className={`w-full pr-12 pl-10 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.passwordConfirm ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
                {formErrors.passwordConfirm && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.passwordConfirm}</span>
                )}
              </div>

              <div className="relative">
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="אימייל"
                  className={`w-full pr-12 pl-4 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.email ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                {formErrors.email && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.email}</span>
                )}
              </div>

              <div className="relative">
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="email"
                  name="emailConfirm"
                  value={formData.emailConfirm}
                  onChange={handleInputChange}
                  placeholder="אימות אימייל"
                  className={`w-full pr-12 pl-4 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.emailConfirm ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                {formErrors.emailConfirm && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.emailConfirm}</span>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">אני/אתה</label>
                <div className="grid grid-cols-3 gap-4">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => handleGenderSelect('male')}
                    className={`p-4 border rounded-xl flex flex-col items-center transition-all duration-200 ${
                      formData.gender === 'male'
                        ? 'border-blue-500 bg-blue-50 shadow-lg'
                        : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/50'
                    }`}
                  >
                    <MaleIcon />
                    <span className="mt-2">גבר</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => handleGenderSelect('female')}
                    className={`p-4 border rounded-xl flex flex-col items-center transition-all duration-200 ${
                      formData.gender === 'female'
                        ? 'border-pink-500 bg-pink-50 shadow-lg'
                        : 'border-gray-200 hover:border-pink-200 hover:bg-pink-50/50'
                    }`}
                  >
                    <FemaleIcon />
                    <span className="mt-2">אישה</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => handleGenderSelect('couple')}
                    className={`p-4 border rounded-xl flex flex-col items-center transition-all duration-200 ${
                      formData.gender === 'couple'
                        ? 'border-pink-500 bg-pink-50 shadow-lg'
                        : 'border-gray-200 hover:border-pink-200 hover:bg-pink-50/50'
                    }`}
                  >
                    <Users className="h-8 w-8 mb-2 text-pink-600" />
                    <span>זוג</span>
                  </motion.button>
                </div>
                {formErrors.gender && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.gender}</span>
                )}
              </div>

              {formData.gender === 'couple' ? (
                <>
                  {renderBirthDateSelectors('', '* תאריך לידה -גבר')}
                  {renderBirthDateSelectors('partner', '* תאריך לידה - אישה')}
                </>
              ) : (
                renderBirthDateSelectors()
              )}

              <div className="relative">
                <MapPin className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <select
                  name="area"
                  value={formData.area}
                  onChange={handleInputChange}
                  className={`w-full pr-12 pl-4 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.area ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                >
                  <option value="">אזור מגורים</option>
                  <option value="north">צפון</option>
                  <option value="haifa">חיפה</option>
                  <option value="jerusalem">ירושלים</option>
                  <option value="tel-aviv">תל אביב</option>
                  <option value="judea-samaria">יהודה ושומרון</option>
                  <option value="sharon-rehovot">השרון רחובות</option>
                  <option value="ashdod">אשדוד</option>
                  <option value="ashkelon">אשקלון</option>
                  <option value="beer-sheva">באר שבע</option>
                  <option value="eilat">אילת</option>
                </select>
                {formErrors.area && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.area}</span>
                )}
              </div>

              <div className="relative">
                <MapPin className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  placeholder="עיר מגורים"
                  className={`w-full pr-12 pl-4 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.city ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                {formErrors.city && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.city}</span>
                )}
              </div>

              <div className="relative">
                <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="טלפון"
                  className={`w-full pr-12 pl-4 py-3 border rounded-xl focus:border-pink-500 focus:ring-2 focus:ring-pink-200 transition-all duration-200 ${
                    formErrors.phone ? 'border-red-500' : 'border-gray-200'
                  }`}
                  required
                />
                {formErrors.phone && (
                  <span className="text-red-500 text-sm mt-1">{formErrors.phone}</span>
                )}
                <span className="text-xs text-gray-500 mt-1 block">המספר לא יוצג באתר</span>
              </div>

              <div className="space-y-2">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    name="termsAccepted"
                    checked={formData.termsAccepted}
                    onChange={handleInputChange}
                    className={`w-4 h-4 rounded border-gray-300 text-pink-600 focus:ring-pink-500 ${
                      formErrors.termsAccepted ? 'border-red-500' : ''
                    }`}
                  />
                  <span className="text-sm text-gray-600">אני מסכים/ה לתנאי השימוש באתר</span>
                </label>
                {formErrors.termsAccepted && (
                  <span className="text-red-500 text-sm block">{formErrors.termsAccepted}</span>
                )}

                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    name="newsletterSubscription"
                    checked={formData.newsletterSubscription}
                    onChange={handleInputChange}
                    className="w-4 h-4 rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                  />
                  <span className="text-sm text-gray-600">אני מעוניין/ת לקבל עדכונים במייל</span>
                </label>

                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    name="privacyAccepted"
                    checked={formData.privacyAccepted}
                    onChange={handleInputChange}
                    className={`w-4 h-4 rounded border-gray-300 text-pink-600 focus:ring-pink-500 ${
                      formErrors.privacyAccepted ? 'border-red-500' : ''
                    }`}
                  />
                  <span className="text-sm text-gray-600">קראתי והסכמתי לתנאי התקנון והפרטיות</span>
                </label>
                {formErrors.privacyAccepted && (
                  <span className="text-red-500 text-sm block">{formErrors.privacyAccepted}</span>
                )}
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isLoading}
              className={`w-full bg-pink-600 text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl ${
                isLoading ? 'opacity-70 cursor-not-allowed' : 'hover:bg-pink-700'
              }`}
            >
              {isLoading ? 'נרשם...' : 'המשך'}
            </motion.button>

            <div className="text-center mt-6">
              <p className="text-gray-600">
                כבר יש לך חשבון?{' '}
                <button
                  type="button"
                  onClick={onSignInClick}
                  className="text-pink-600 hover:text-pink-700 transition-colors"
                >
                  התחבר כאן
                </button>
              </p>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
}

export default SignupForm;