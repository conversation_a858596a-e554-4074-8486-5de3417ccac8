/*
  # Add Storage Bucket for Profile Photos

  1. Create Storage Bucket
    - Name: profile-photos
    - Public access enabled
    - File size limit: 5MB
    - Allowed mime types: image/*

  2. Security
    - Enable RLS
    - Add policies for:
      - Authenticated users can upload their own photos
      - Authenticated users can view all photos
      - Users can only delete their own photos
*/

-- Create storage bucket for profile photos
INSERT INTO storage.buckets (id, name, public)
VALUES ('profile-photos', 'profile-photos', true);

-- Set bucket configuration
UPDATE storage.buckets
SET file_size_limit = 5242880, -- 5MB in bytes
    allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
WHERE id = 'profile-photos';

-- Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to upload their own photos
CREATE POLICY "Users can upload their own photos"
ON storage.objects FOR INSERT TO authenticated
WITH CHECK (
  bucket_id = 'profile-photos' AND
  (storage.foldername(name))[1] = auth.uid()::text
);

-- Create policy to allow authenticated users to view all photos
CREATE POLICY "Users can view all photos"
ON storage.objects FOR SELECT TO authenticated
USING (bucket_id = 'profile-photos');

-- Create policy to allow users to delete their own photos
CREATE POLICY "Users can delete their own photos"
ON storage.objects FOR DELETE TO authenticated
USING (
  bucket_id = 'profile-photos' AND
  (storage.foldername(name))[1] = auth.uid()::text
);