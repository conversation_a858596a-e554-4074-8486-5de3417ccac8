import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Loader2 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useProfile } from '../hooks/useProfile';
import { Profile } from '../types/supabase';

interface EditProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen?: () => void;
}

function EditProfileModal({ isOpen, onClose, onOpen }: EditProfileModalProps) {
  const { user } = useAuth();
  const { updateProfile, loading, error } = useProfile();
  const profile = user?.user_metadata as Profile;
  const [formData, setFormData] = useState({});
  const [success, setSuccess] = useState(false);

  // Initialize form data when modal opens
  React.useEffect(() => {
    if (isOpen && profile?.profile_data) {
      console.log('Initializing form data with:', profile.profile_data);
      setFormData(profile.profile_data);
      onOpen?.(); // Notify parent that editing started
    }
  }, [isOpen, profile?.profile_data, onOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (field: 'seekingGender' | 'meetingTimes', value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field]?.includes(value)
        ? prev[field].filter((item: string) => item !== value)
        : [...(prev[field] || []), value]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const { error: updateError } = await updateProfile(formData);

    if (!updateError) {
      setSuccess(true);
      setTimeout(() => {
        setSuccess(false);
        onClose();
      }, 1500);
    }
  };

  const renderPersonalFields = (prefix: string = '', title: string = '') => (
    <div className="space-y-4">
      {title && <h3 className="text-lg font-semibold text-gray-900">{title}</h3>}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">שם</label>
          <input
            type="text"
            name={prefix ? `${prefix}name` : 'name'}
            value={formData[prefix ? `${prefix}name` : 'name'] || ''}
            disabled
            className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm text-gray-500 cursor-not-allowed"
            title="לא ניתן לשנות שם"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">תאריך לידה</label>
          <input
            type="date"
            name={prefix ? `${prefix}birthDate` : 'birthDate'}
            value={formData[prefix ? `${prefix}birthDate` : 'birthDate'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">גובה</label>
          <select
            name={prefix ? `${prefix}height` : 'height'}
            value={formData[prefix ? `${prefix}height` : 'height'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            {Array.from({ length: 61 }, (_, i) => i + 140).map(height => (
              <option key={height} value={height}>{height} ס"מ</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">משקל</label>
          <select
            name={prefix ? `${prefix}weight` : 'weight'}
            value={formData[prefix ? `${prefix}weight` : 'weight'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            {Array.from({ length: 151 }, (_, i) => i + 40).map(weight => (
              <option key={weight} value={weight}>{weight} ק"ג</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">צבע שיער</label>
          <select
            name={prefix ? `${prefix}hairColor` : 'hairColor'}
            value={formData[prefix ? `${prefix}hairColor` : 'hairColor'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="black">שחור</option>
            <option value="brown">חום</option>
            <option value="blonde">בלונדיני</option>
            <option value="red">ג'ינג'י</option>
            <option value="gray">אפור</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">תסרוקת</label>
          <select
            name={prefix ? `${prefix}hairStyle` : 'hairStyle'}
            value={formData[prefix ? `${prefix}hairStyle` : 'hairStyle'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="long">ארוך</option>
            <option value="short">קצר</option>
            <option value="bald">קרח</option>
            <option value="medium">בינוני</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">צבע עיניים</label>
          <select
            name={prefix ? `${prefix}eyeColor` : 'eyeColor'}
            value={formData[prefix ? `${prefix}eyeColor` : 'eyeColor'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="brown">חום</option>
            <option value="blue">כחול</option>
            <option value="green">ירוק</option>
            <option value="hazel">דבש</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">מבנה גוף</label>
          <select
            name={prefix ? `${prefix}bodyType` : 'bodyType'}
            value={formData[prefix ? `${prefix}bodyType` : 'bodyType'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="slim">רזה</option>
            <option value="athletic">אתלטי</option>
            <option value="average">ממוצע</option>
            <option value="curvy">מלא</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">מוצא</label>
          <select
            name={prefix ? `${prefix}ethnicity` : 'ethnicity'}
            value={formData[prefix ? `${prefix}ethnicity` : 'ethnicity'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="ashkenazi">אשכנזי</option>
            <option value="sephardi">ספרדי</option>
            <option value="mixed">מעורב</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">העדפה מינית</label>
          <select
            name={prefix ? `${prefix}sexualPreference` : 'sexualPreference'}
            value={formData[prefix ? `${prefix}sexualPreference` : 'sexualPreference'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="straight">סטרייט</option>
            <option value="bisexual">דו מיני/ת</option>
            <option value="gay">הומו</option>
            <option value="lesbian">לסבית</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">ניסיון בחילופי זוגות</label>
          <select
            name={prefix ? `${prefix}swingingExperience` : 'swingingExperience'}
            value={formData[prefix ? `${prefix}swingingExperience` : 'swingingExperience'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="none">ללא ניסיון</option>
            <option value="some">מעט ניסיון</option>
            <option value="experienced">מנוסה</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">הרגלי עישון</label>
          <select
            name={prefix ? `${prefix}smokingHabits` : 'smokingHabits'}
            value={formData[prefix ? `${prefix}smokingHabits` : 'smokingHabits'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="non-smoker">לא מעשן</option>
            <option value="occasional">מעשן לעיתים</option>
            <option value="regular">מעשן קבוע</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">הרגלי שתיה</label>
          <select
            name={prefix ? `${prefix}drinkingHabits` : 'drinkingHabits'}
            value={formData[prefix ? `${prefix}drinkingHabits` : 'drinkingHabits'] || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
          >
            <option value="">בחר</option>
            <option value="non-drinker">לא שותה</option>
            <option value="social">שותה חברתי</option>
            <option value="regular">שותה באופן קבוע</option>
          </select>
        </div>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto m-4"
      >
        <div className="p-6 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h2 className="text-2xl font-bold text-gray-900">עריכת פרופיל</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Marital Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">מצב משפחתי</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">מצב משפחתי</label>
                <select
                  name="maritalStatus"
                  value={formData.maritalStatus || ''}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                >
                  <option value="">בחר</option>
                  <option value="single">רווק/ה</option>
                  <option value="married">נשוי/אה</option>
                  <option value="divorced">גרוש/ה</option>
                </select>
              </div>

              {formData.maritalStatus !== 'single' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">מס' ילדים</label>
                  <select
                    name="children"
                    value={formData.children || ''}
                    onChange={handleInputChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                  >
                    <option value="">בחר</option>
                    {Array.from({ length: 11 }, (_, i) => (
                      <option key={i} value={i}>{i}</option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* Personal Details */}
          {profile.gender === 'couple' ? (
            <>
              {renderPersonalFields('1', 'פרטי גבר')}
              {renderPersonalFields('2', 'פרטי אישה')}
            </>
          ) : (
            renderPersonalFields()
          )}

          {/* Shared Fields */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">פרטים נוספים</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">קצת עלינו</label>
              <textarea
                name="aboutUs"
                value={formData.aboutUs || ''}
                onChange={handleInputChange}
                rows={4}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">מה אנחנו מחפשים</label>
              <textarea
                name="lookingFor"
                value={formData.lookingFor || ''}
                onChange={handleInputChange}
                rows={4}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">מחפשים</label>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.seekingGender?.includes('male')}
                    onChange={() => handleCheckboxChange('seekingGender', 'male')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>גבר</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.seekingGender?.includes('female')}
                    onChange={() => handleCheckboxChange('seekingGender', 'female')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>אישה</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.seekingGender?.includes('couple')}
                    onChange={() => handleCheckboxChange('seekingGender', 'couple')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>זוג</span>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">שעות מפגש מועדפות</label>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.meetingTimes?.includes('morning')}
                    onChange={() => handleCheckboxChange('meetingTimes', 'morning')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>בוקר</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.meetingTimes?.includes('afternoon')}
                    onChange={() => handleCheckboxChange('meetingTimes', 'afternoon')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>צהריים</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.meetingTimes?.includes('evening')}
                    onChange={() => handleCheckboxChange('meetingTimes', 'evening')}
                    className="rounded text-pink-600 focus:ring-pink-500"
                  />
                  <span>ערב</span>
                </label>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 text-red-700 p-4 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-50 text-green-700 p-4 rounded-lg">
              הפרופיל עודכן בהצלחה!
            </div>
          )}

          <div className="flex justify-end gap-4 sticky bottom-0 bg-white py-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              ביטול
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  מעדכן...
                </>
              ) : (
                'שמור שינויים'
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}

export default EditProfileModal;