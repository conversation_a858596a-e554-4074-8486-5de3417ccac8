/*
  # Implement Private Messaging System

  1. Changes
    - Update chat system to ensure message privacy
    - Add proper RLS policies for messages and chats
    - Ensure messages are only visible to participants
    
  2. Security
    - Messages are private to chat participants
    - Users can only access their own chats
    - Chat participants are properly managed
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view messages in their chats" ON messages;
DROP POLICY IF EXISTS "Users can send messages to their chats" ON messages;
DROP POLICY IF EXISTS "Users can view their chats" ON chats;
DROP POLICY IF EXISTS "Users can create chats" ON chats;
DROP POLICY IF EXISTS "Chat participants can update chats" ON chats;
DROP POLICY IF EXISTS "Users can view chat participants" ON chat_participants;
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON chat_participants;

-- Chat policies
CREATE POLICY "Users can view their chats"
ON chats
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = id
    AND chat_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Users can create chats"
ON chats
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Chat participants can update chats"
ON chats
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = id
    AND chat_participants.user_id = auth.uid()
  )
);

-- Chat participants policies
CREATE POLICY "Users can view chat participants"
ON chat_participants
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid() OR
  chat_id IN (
    SELECT chat_id 
    FROM chat_participants 
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Allow chat participant creation"
ON chat_participants
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() IS NOT NULL);

-- Message policies
CREATE POLICY "Users can view messages in their chats"
ON messages
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Users can send messages to their chats"
ON messages
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
  AND sender_id = auth.uid()
);