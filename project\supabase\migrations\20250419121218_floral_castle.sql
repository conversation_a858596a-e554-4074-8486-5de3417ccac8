/*
  # Add VIP status to profiles

  1. Changes
    - Add `is_vip` boolean column to `profiles` table with default value of false
    
  2. Security
    - No changes to RLS policies needed as this column will be managed through existing profile policies
*/

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'is_vip'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_vip boolean DEFAULT false;
  END IF;
END $$;