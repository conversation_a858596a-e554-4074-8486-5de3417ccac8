import React from 'react';
import { useSimpleProfileImage } from '../hooks/useSimpleProfileImage';

interface ProfileImageProps {
  userId: string;
  username?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showOnlineStatus?: boolean;
  isOnline?: boolean;
  className?: string;
  fallbackUrl?: string;
}

const sizeClasses = {
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-10 h-10',
  xl: 'w-12 h-12'
};

const onlineStatusSizes = {
  sm: 'w-1.5 h-1.5',
  md: 'w-2 h-2',
  lg: 'w-2.5 h-2.5',
  xl: 'w-3 h-3'
};

function ProfileImage({ 
  userId, 
  username = 'משתמש', 
  size = 'md', 
  showOnlineStatus = false, 
  isOnline = false,
  className = '',
  fallbackUrl
}: ProfileImageProps) {
  const { profileImage } = useSimpleProfileImage(userId);

  // Default avatar SVG
  const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE1IiBmaWxsPSIjOUI5QkEzIi8+CjxwYXRoIGQ9Ik0yNSA3NUMyNSA2NS4zMzUgMzMuMzM1IDU3IDQzIDU3SDE1N0M2Ni42NjUgNTcgNzUgNjUuMzM1IDc1IDc1VjgwSDI1Vjc1WiIgZmlsbD0iIzlCOUJBMyIvPgo8L3N2Zz4K';

  // Determine which image to use
  const imageUrl = profileImage || fallbackUrl || defaultAvatar;

  return (
    <div className={`relative ${className}`}>
      <img
        src={imageUrl}
        alt={username}
        className={`${sizeClasses[size]} rounded-full object-cover`}
        onError={(e) => {
          // If image fails to load, use default avatar
          e.currentTarget.src = defaultAvatar;
        }}
      />
      {showOnlineStatus && (
        <div className={`${onlineStatusSizes[size]} rounded-full absolute bottom-0 right-0 border-2 border-white ${
          isOnline ? 'bg-green-500' : 'bg-gray-400'
        }`} />
      )}
    </div>
  );
}

export default ProfileImage;
