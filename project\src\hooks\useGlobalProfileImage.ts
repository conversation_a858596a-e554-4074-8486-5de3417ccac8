import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';

// Global state for profile images
const profileImageStore = new Map<string, string>();
const subscribers = new Set<() => void>();

// Function to update profile image globally
export const updateGlobalProfileImage = (userId: string, imageUrl: string) => {
  profileImageStore.set(userId, imageUrl);
  // Notify all subscribers
  subscribers.forEach(callback => callback());
};

// Function to get profile image from global store
export const getGlobalProfileImage = (userId: string): string | null => {
  return profileImageStore.get(userId) || null;
};

// Hook to use profile image with automatic updates
export function useGlobalProfileImage(userId?: string, fallbackData?: any) {
  const { user } = useAuth();
  const [profileImage, setProfileImage] = useState<string | null>(null);
  
  const targetUserId = userId || user?.id;

  useEffect(() => {
    if (!targetUserId) return;

    // Initialize profile image
    const initializeImage = () => {
      console.log('🎯 Initializing image for user:', targetUserId, 'fallbackData:', {
        hasUserMetadata: !!fallbackData?.user_metadata,
        profileImageUrl: fallbackData?.user_metadata?.profile_image_url,
        hasPhotos: !!fallbackData?.photos?.length,
        firstPhoto: fallbackData?.photos?.[0]
      });

      // Check global store first
      const globalImage = getGlobalProfileImage(targetUserId);
      if (globalImage) {
        console.log('📦 Using global store image:', globalImage);
        setProfileImage(globalImage);
        return;
      }

      // For current user, use auth metadata
      if (user && targetUserId === user.id && user.user_metadata?.profile_image_url) {
        const authImage = user.user_metadata.profile_image_url;
        console.log('🔐 Using auth metadata image:', authImage);
        setProfileImage(authImage);
        updateGlobalProfileImage(targetUserId, authImage);
        return;
      }

      // Use fallback data
      if (fallbackData?.user_metadata?.profile_image_url) {
        const fallbackImage = fallbackData.user_metadata.profile_image_url;
        console.log('📋 Using fallback metadata image:', fallbackImage);
        setProfileImage(fallbackImage);
        updateGlobalProfileImage(targetUserId, fallbackImage);
        return;
      }

      // Use first photo as fallback
      if (fallbackData?.photos && fallbackData.photos.length > 0) {
        const photoImage = fallbackData.photos[0];
        console.log('📸 Using first photo as fallback:', photoImage);
        setProfileImage(photoImage);
        updateGlobalProfileImage(targetUserId, photoImage);
        return;
      }

      console.log('❌ No image found for user:', targetUserId);
      setProfileImage(null);
    };

    initializeImage();

    // Subscribe to global updates
    const updateCallback = () => {
      const newImage = getGlobalProfileImage(targetUserId);
      if (newImage !== profileImage) {
        setProfileImage(newImage);
      }
    };

    subscribers.add(updateCallback);

    return () => {
      subscribers.delete(updateCallback);
    };
  }, [targetUserId, user, fallbackData, profileImage]);

  return profileImage;
}

// Function to set profile image (replaces setAsProfileImage)
export const setGlobalProfileImage = async (url: string) => {
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'User not authenticated' };
  }

  try {
    console.log('🔄 Starting profile image update for user:', user.id, 'new URL:', url);

    // Update user metadata in auth.users
    const { error: updateError } = await supabase.auth.updateUser({
      data: {
        profile_image_url: url
      }
    });

    if (updateError) {
      console.error('❌ Error updating auth user metadata:', updateError);
      throw updateError;
    }

    console.log('✅ Auth user metadata updated successfully');

    // Also update the profiles table if it exists
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          user_metadata: {
            ...user.user_metadata,
            profile_image_url: url
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (profileError) {
        console.log('⚠️ Could not update profiles table (might not exist):', profileError);
      } else {
        console.log('✅ Profiles table updated successfully');
      }
    } catch (profileErr) {
      console.log('⚠️ Profiles table update failed:', profileErr);
    }

    // Update global store for immediate UI update
    updateGlobalProfileImage(user.id, url);

    // Broadcast the change to other users via Supabase realtime (if available)
    try {
      await supabase
        .from('profile_updates')
        .insert({
          user_id: user.id,
          update_type: 'profile_image',
          new_value: url,
          timestamp: new Date().toISOString()
        });
      console.log('📡 Broadcasted profile image change');
    } catch (broadcastErr) {
      console.log('⚠️ Could not broadcast change (table might not exist):', broadcastErr);
    }

    console.log('✅ Profile image updated globally for user:', user.id, 'new URL:', url);

    return { error: null };
  } catch (err) {
    console.error('❌ Error setting profile image:', err);
    return { error: err };
  }
};
