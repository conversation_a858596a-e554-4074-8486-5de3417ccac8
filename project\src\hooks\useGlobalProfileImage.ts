import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';

// Global state for profile images
const profileImageStore = new Map<string, string>();
const subscribers = new Set<() => void>();

// Function to update profile image globally
export const updateGlobalProfileImage = (userId: string, imageUrl: string) => {
  profileImageStore.set(userId, imageUrl);
  // Notify all subscribers
  subscribers.forEach(callback => callback());
};

// Function to get profile image from global store
export const getGlobalProfileImage = (userId: string): string | null => {
  return profileImageStore.get(userId) || null;
};

// Hook to use profile image with automatic updates
export function useGlobalProfileImage(userId?: string, fallbackData?: any) {
  const { user } = useAuth();
  const [profileImage, setProfileImage] = useState<string | null>(null);
  
  const targetUserId = userId || user?.id;

  useEffect(() => {
    if (!targetUserId) return;

    // Initialize profile image
    const initializeImage = async () => {
      console.log('🎯 Initializing image for user:', targetUserId, 'fallbackData:', {
        hasUserMetadata: !!fallbackData?.user_metadata,
        profileImageUrl: fallbackData?.user_metadata?.profile_image_url,
        hasPhotos: !!fallbackData?.photos?.length,
        firstPhoto: fallbackData?.photos?.[0]
      });

      // Check global store first
      const globalImage = getGlobalProfileImage(targetUserId);
      if (globalImage) {
        console.log('📦 Using global store image:', globalImage);
        // Test if the image URL is accessible
        const img = new Image();
        img.onload = () => console.log('✅ Global store image loaded successfully');
        img.onerror = () => {
          console.error('❌ Global store image failed to load (404), clearing from store');
          // Remove invalid image from global store
          profileImageStore.delete(targetUserId);
          // Try to use fallback data instead
          if (fallbackData?.photos && fallbackData.photos.length > 0) {
            const photoImage = fallbackData.photos[0];
            console.log('🔄 Using first photo as fallback:', photoImage);
            setProfileImage(photoImage);
            updateGlobalProfileImage(targetUserId, photoImage);
          } else {
            setProfileImage(null);
          }
        };
        img.src = globalImage;
        setProfileImage(globalImage);
        return;
      }

      // For current user, use auth metadata (refresh auth data first)
      if (user && targetUserId === user.id) {
        // Get fresh auth data to ensure we have the latest profile_image_url
        try {
          const { data: { user: freshUser } } = await supabase.auth.getUser();
          if (freshUser?.user_metadata?.profile_image_url) {
            const authImage = freshUser.user_metadata.profile_image_url;
            console.log('🔐 Using fresh auth metadata image:', authImage);
            setProfileImage(authImage);
            updateGlobalProfileImage(targetUserId, authImage);
            return;
          }
        } catch (authErr) {
          console.log('⚠️ Could not fetch fresh auth data:', authErr);
        }

        // Fallback to existing auth metadata
        if (user.user_metadata?.profile_image_url) {
          const authImage = user.user_metadata.profile_image_url;
          console.log('🔐 Using existing auth metadata image:', authImage);
          setProfileImage(authImage);
          updateGlobalProfileImage(targetUserId, authImage);
          return;
        }
      }

      // Use fallback data
      if (fallbackData?.user_metadata?.profile_image_url) {
        const fallbackImage = fallbackData.user_metadata.profile_image_url;
        console.log('📋 Using fallback metadata image:', fallbackImage);
        // Test if the fallback image URL is accessible
        const img = new Image();
        img.onload = () => console.log('✅ Fallback metadata image loaded successfully');
        img.onerror = () => {
          console.error('❌ Fallback metadata image failed to load (404)');
          // If fallback image fails, try first photo
          if (fallbackData?.photos && fallbackData.photos.length > 0) {
            const photoImage = fallbackData.photos[0];
            console.log('🔄 Trying first photo instead:', photoImage);
            setProfileImage(photoImage);
            updateGlobalProfileImage(targetUserId, photoImage);
          } else {
            setProfileImage(null);
          }
        };
        img.src = fallbackImage;
        setProfileImage(fallbackImage);
        updateGlobalProfileImage(targetUserId, fallbackImage);
        return;
      }

      // Use first photo as fallback
      if (fallbackData?.photos && fallbackData.photos.length > 0) {
        const photoImage = fallbackData.photos[0];
        console.log('📸 Using first photo as fallback:', photoImage);
        setProfileImage(photoImage);
        updateGlobalProfileImage(targetUserId, photoImage);
        return;
      }

      console.log('❌ No image found for user:', targetUserId);
      setProfileImage(null);
    };

    initializeImage().catch(err => {
      console.error('Error initializing image:', err);
      setProfileImage(null);
    });

    // Subscribe to global updates
    const updateCallback = () => {
      const newImage = getGlobalProfileImage(targetUserId);
      if (newImage !== profileImage) {
        setProfileImage(newImage);
      }
    };

    subscribers.add(updateCallback);

    return () => {
      subscribers.delete(updateCallback);
    };
  }, [targetUserId, user, fallbackData, profileImage]);

  return profileImage;
}

// Function to validate if image URL is accessible
const validateImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

// Function to set profile image (replaces setAsProfileImage)
export const setGlobalProfileImage = async (url: string) => {
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'User not authenticated' };
  }

  try {
    console.log('🔄 Starting profile image update for user:', user.id, 'new URL:', url);

    // First, validate that the image URL is accessible
    const isValidImage = await validateImageUrl(url);
    if (!isValidImage) {
      console.error('❌ Image URL is not accessible:', url);
      return { error: 'Image URL is not accessible' };
    }

    console.log('✅ Image URL validated successfully');

    // Update user metadata in auth.users
    const { error: updateError } = await supabase.auth.updateUser({
      data: {
        profile_image_url: url
      }
    });

    if (updateError) {
      console.error('❌ Error updating auth user metadata:', updateError);
      throw updateError;
    }

    console.log('✅ Auth user metadata updated successfully');

    // Also update the profiles table - let's first check what columns exist
    try {
      // First, let's see what the current profile structure looks like
      const { data: currentProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        console.log('⚠️ Could not fetch current profile data:', fetchError);
        console.log('⚠️ Skipping profiles table update - table might not exist or be accessible');
        // Don't throw error, just continue without updating profiles table
      } else {
        console.log('📋 Current profile structure:', Object.keys(currentProfile));
        console.log('📋 Current profile data:', currentProfile);

        // Check if the table has a user_metadata column
        if ('user_metadata' in currentProfile) {
          // Merge the new profile_image_url with existing user_metadata
          const updatedUserMetadata = {
            ...(currentProfile.user_metadata || {}),
            ...(user.user_metadata || {}),
            profile_image_url: url
          };

          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              user_metadata: updatedUserMetadata,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (profileError) {
            console.error('❌ Failed to update profiles table:', profileError);
          } else {
            console.log('✅ Profiles table updated successfully with user_metadata:', updatedUserMetadata);
          }
        } else {
          // Try to update just the updated_at field to trigger any listeners
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (profileError) {
            console.log('⚠️ Could not update profiles table timestamp:', profileError);
          } else {
            console.log('✅ Profiles table timestamp updated (no user_metadata column found)');
          }
        }
      }
    } catch (profileErr) {
      console.log('⚠️ Profiles table update failed, continuing anyway:', profileErr);
      // Don't throw error - the auth update is more important
    }

    // Update global store for immediate UI update
    updateGlobalProfileImage(user.id, url);

    // Force refresh of profile data in other components by triggering a custom event
    // This will help update any cached profile data
    window.dispatchEvent(new CustomEvent('profileDataUpdated', {
      detail: {
        userId: user.id,
        updateType: 'profile_image',
        newImageUrl: url,
        timestamp: new Date().toISOString()
      }
    }));
    console.log('📡 Dispatched profileDataUpdated event');

    console.log('✅ Profile image updated globally for user:', user.id, 'new URL:', url);

    return { error: null };
  } catch (err) {
    console.error('❌ Error setting profile image:', err);
    return { error: err };
  }
};
