-- Create a function to get complete profile data
CREATE OR REPLACE FUNCTION get_complete_profile(profile_id UUID)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_data jsonb;
  auth_metadata jsonb;
  photos_array jsonb;
BEGIN
  -- Get auth user metadata
  SELECT
    COALESCE(au.raw_user_meta_data, '{}'::jsonb)
  INTO auth_metadata
  FROM auth.users au
  WHERE au.id = profile_id;

  -- Get photos array
  SELECT COALESCE(
    jsonb_agg(ps.photo_url ORDER BY ps.created_at),
    '[]'::jsonb
  )
  INTO photos_array
  FROM profile_photos ps
  WHERE ps.profile_id = profile_id;

  -- Get the profile data from the profiles table
  SELECT
    jsonb_build_object(
      'id', p.id,
      'username', p.username,
      'gender', p.gender,
      'birth_date', p.birth_date,
      'partner_birth_date', p.partner_birth_date,
      'city', p.city,
      'area', p.area,
      'phone', p.phone,
      'height', p.height,
      'weight', p.weight,
      'hair_color', p.hair_color,
      'hair_style', p.hair_style,
      'eye_color', p.eye_color,
      'body_type', p.body_type,
      'marital_status', p.marital_status,
      'children', p.children,
      'ethnicity', p.ethnicity,
      'sexual_preference', p.sexual_preference,
      'swinging_experience', p.swinging_experience,
      'smoking_habits', p.smoking_habits,
      'drinking_habits', p.drinking_habits,
      'about_us', p.about_us,
      'looking_for', p.looking_for,
      'seeking_gender', p.seeking_gender,
      'meeting_times', p.meeting_times,
      'created_at', p.created_at,
      'updated_at', p.updated_at,
      'user_metadata', auth_metadata,
      'is_vip', EXISTS(SELECT 1 FROM vip_subscriptions vs WHERE vs.user_id = p.id AND vs.expires_at > NOW()),
      'photos', photos_array
    )
  INTO profile_data
  FROM profiles p
  WHERE p.id = profile_id;

  RETURN profile_data;
END;
$$;
