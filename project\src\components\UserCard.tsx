import React, { useState } from 'react';
import ReactDOM from 'react-dom';
import { motion } from 'framer-motion';
import { MapPin, Gift, Crown, Users } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';
import { useProfileImage } from '../hooks/useProfileImage';
import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import SendGiftDialog from './SendGiftDialog';

interface UserCardProps {
  profile: Profile;
}

function UserCard({ profile }: UserCardProps) {
  const { navigateToProfile } = useNavigate();
  const [isGiftDialogOpen, setIsGiftDialogOpen] = useState(false);

  // Get the current profile image (will update automatically when changed)
  const currentProfileImage = useProfileImage(profile.id, profile);

  // Debug logging
  console.log('UserCard: profile.id:', profile.id, 'currentProfileImage:', currentProfileImage, 'profile.user_metadata?.profile_image_url:', profile.user_metadata?.profile_image_url);

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  const getGenderIcon = () => {
    switch (profile.gender) {
      case 'male':
        return <FontAwesomeIcon icon={faMars} className="text-xl text-blue-600" />;
      case 'female':
        return <FontAwesomeIcon icon={faVenus} className="text-xl text-pink-600" />;
      case 'couple':
        return <Users className="h-5 w-5 text-purple-600" />;
      default:
        return null;
    }
  };

  const getGenderText = () => {
    switch (profile.gender) {
      case 'male':
        return 'גבר';
      case 'female':
        return 'אישה';
      case 'couple':
        return 'זוג';
      default:
        return '';
    }
  };

  const handleGift = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsGiftDialogOpen(true);
  };

  const getSexualPreferenceTranslation = (preference: string) => {
    const translations: Record<string, string> = {
      straight: 'סטרייט',
      bisexual: 'דו מיני/ת',
      gay: 'הומו',
      lesbian: 'לסבית'
    };
    return translations[preference] || preference;
  };

  // Function to handle profile navigation with complete data
  const handleProfileClick = async () => {
    try {
      console.log('Fetching complete profile data for:', profile.id);

      // Try to get complete profile data using RPC
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('get_complete_profile', { profile_id: profile.id });

        if (!rpcError && rpcData) {
          console.log('Complete profile data fetched with RPC:', rpcData);

          // Make sure we have user_metadata
          if (!rpcData.user_metadata) {
            rpcData.user_metadata = {};
          }

          // Make sure we have profile_data in user_metadata
          if (!rpcData.user_metadata.profile_data) {
            rpcData.user_metadata.profile_data = {};
          }

          // If we have profile_data directly on the profile, copy it to user_metadata
          if (rpcData.profile_data && Object.keys(rpcData.profile_data).length > 0) {
            console.log('Copying profile_data to user_metadata.profile_data');
            rpcData.user_metadata.profile_data = {
              ...rpcData.user_metadata.profile_data,
              ...rpcData.profile_data
            };
          }

          // Navigate with the complete profile data
          navigateToProfile(rpcData);
          return;
        }
      } catch (rpcErr) {
        console.error('Error fetching with RPC:', rpcErr);
      }

      // Fallback to regular query
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profile.id)
        .single();

      if (error) {
        console.error('Error fetching complete profile data:', error);
        // Fall back to using the profile we have
        navigateToProfile(profile);
        return;
      }

      if (data) {
        console.log('Complete profile data fetched:', data);

        // Make sure we have user_metadata
        if (!data.user_metadata) {
          data.user_metadata = {};
        }

        // Make sure we have profile_data in user_metadata
        if (!data.user_metadata.profile_data) {
          data.user_metadata.profile_data = {};
        }

        // If we have profile_data directly on the profile, copy it to user_metadata
        if (data.profile_data && Object.keys(data.profile_data).length > 0) {
          console.log('Copying profile_data to user_metadata.profile_data');
          data.user_metadata.profile_data = {
            ...data.user_metadata.profile_data,
            ...data.profile_data
          };
        }

        // Navigate with the complete profile data
        navigateToProfile(data);
      } else {
        // Fall back to using the profile we have
        navigateToProfile(profile);
      }
    } catch (err) {
      console.error('Error in handleProfileClick:', err);
      // Fall back to using the profile we have
      navigateToProfile(profile);
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      onClick={handleProfileClick}
      className="bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-all cursor-pointer w-full"
    >
      {/* Profile Image Container */}
      <div className="relative aspect-[3/4] overflow-hidden">
        {currentProfileImage || profile.user_metadata?.profile_image_url ? (
          <img
            src={currentProfileImage || profile.user_metadata?.profile_image_url}
            alt={profile.username}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          />
        ) : profile.photos && profile.photos.length > 0 ? (
          <img
            src={profile.photos[0]}
            alt={profile.username}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <Users className="w-12 h-12 text-gray-400" />
          </div>
        )}

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

        {/* VIP Badge */}
        {profile.is_vip && (
          <div className="absolute top-2 right-2 bg-yellow-500/90 backdrop-blur-sm text-white px-2 py-0.5 rounded-full flex items-center gap-1 shadow-lg">
            <Crown className="w-3 h-3" />
            <span className="text-xs font-medium">VIP</span>
          </div>
        )}

        {/* Gift Button */}
        <div
          onClick={handleGift}
          className="absolute top-2 left-2 bg-white/90 p-2 rounded-full text-purple-600 hover:bg-white transition-colors shadow-lg backdrop-blur-sm cursor-pointer hover:shadow-xl"
        >
          <Gift className="w-4 h-4" />
        </div>

        {/* Profile Info */}
        <div className="absolute bottom-0 left-0 right-0 p-3 text-white">
          {/* Username and Location */}
          <div className="mb-2">
            <h3 className="text-lg font-semibold mb-1 line-clamp-1 drop-shadow-sm">
              {profile.username}
            </h3>
            <div className="flex items-center gap-1 text-white/90">
              <MapPin className="w-3.5 h-3.5" />
              <span className="text-sm line-clamp-1">{profile.city}</span>
            </div>
          </div>

          {/* Gender, Age and Preferences */}
          <div className="flex items-center gap-3 text-white/90">
            {/* Gender and Age */}
            <div className="flex items-center gap-2">
              <div className="bg-white/10 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1.5">
                {getGenderIcon()}
                <span className="text-sm font-medium">{getGenderText()}</span>
              </div>
              <div className="bg-white/10 backdrop-blur-sm px-2 py-1 rounded-full">
                <span className="text-sm font-medium">
                  {profile.gender === 'couple' ? (
                    <>
                      {calculateAge(profile.birth_date)}/{calculateAge(profile.partner_birth_date || '')}
                    </>
                  ) : (
                    calculateAge(profile.birth_date)
                  )}
                </span>
              </div>
            </div>

            {/* Sexual Preference */}
            <div className="bg-white/10 backdrop-blur-sm px-2 py-1 rounded-full">
              <span className="text-sm">
                {getSexualPreferenceTranslation(profile.sexual_preference)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Gift Dialog - Render outside the card to prevent event bubbling issues */}
      {isGiftDialogOpen && ReactDOM.createPortal(
        <SendGiftDialog
          isOpen={isGiftDialogOpen}
          onClose={() => setIsGiftDialogOpen(false)}
          receiverId={profile.id}
          receiverName={profile.username}
        />,
        document.body
      )}
    </motion.div>
  );
}

export default UserCard;