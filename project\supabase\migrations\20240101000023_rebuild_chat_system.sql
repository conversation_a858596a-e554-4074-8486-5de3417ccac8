-- Rebuild chat system
-- This migration rebuilds the entire chat system with improved functionality

-- Drop existing tables and recreate them
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS chat_participants CASCADE;
DROP TABLE IF EXISTS chats CASCADE;

-- <PERSON>reate improved chats table
CREATE TABLE chats (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  is_deleted BOOLEAN DEFAULT FALSE,
  deleted_at TIMESTAMPTZ
);

-- Create improved chat_participants table
CREATE TABLE chat_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  chat_id UUID NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  left_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  last_read_at TIMESTAMPTZ,
  UNIQUE(chat_id, user_id)
);

-- Create improved messages table
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  chat_id UUID NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_edited BOOLEAN DEFAULT FALSE,
  is_deleted BOOLEAN DEFAULT FALSE,
  deleted_at TIMESTAMPTZ,
  reply_to_id UUID REFERENCES messages(id),
  read BOOLEAN DEFAULT FALSE
);

-- Enable RLS on all tables
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create policies for chats table
CREATE POLICY "Users can view their chats" 
ON chats
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = chats.id
    AND chat_participants.user_id = auth.uid()
    AND chat_participants.is_active = TRUE
  )
);

CREATE POLICY "Users can update their chats"
ON chats
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = chats.id
    AND chat_participants.user_id = auth.uid()
    AND chat_participants.is_active = TRUE
  )
);

-- Create policies for chat_participants table
CREATE POLICY "Users can view chat participants"
ON chat_participants
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants AS cp
    WHERE cp.chat_id = chat_participants.chat_id
    AND cp.user_id = auth.uid()
    AND cp.is_active = TRUE
  )
);

-- Create policies for messages table
CREATE POLICY "Users can view messages in their chats"
ON messages
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
    AND chat_participants.is_active = TRUE
  )
);

CREATE POLICY "Users can insert messages in their chats"
ON messages
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
    AND chat_participants.is_active = TRUE
  )
  AND sender_id = auth.uid()
);

CREATE POLICY "Users can update their own messages"
ON messages
FOR UPDATE
TO authenticated
USING (
  sender_id = auth.uid()
  AND NOT is_deleted
);

-- Drop existing functions
DROP FUNCTION IF EXISTS create_chat_between_users(UUID, UUID);
DROP FUNCTION IF EXISTS get_messages_with_sender(UUID, UUID);
DROP FUNCTION IF EXISTS get_user_chats_with_details(UUID);
DROP FUNCTION IF EXISTS get_chat_participants_with_details(UUID, UUID);
DROP FUNCTION IF EXISTS get_user_profile_image(UUID);
DROP FUNCTION IF EXISTS get_user_metadata(UUID);
DROP FUNCTION IF EXISTS get_user_chats_simple(UUID);
DROP FUNCTION IF EXISTS get_chat_messages(UUID);
DROP FUNCTION IF EXISTS get_user_chats(UUID);
