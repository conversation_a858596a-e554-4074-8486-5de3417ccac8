-- Check if triggers exist and create them if they don't
-- This migration will show us what's happening with the triggers

-- Check existing triggers
SELECT 
    trigger_name, 
    event_object_table, 
    action_statement,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE event_object_table IN ('messages', 'user_gifts')
ORDER BY event_object_table, trigger_name;

-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('messages', 'user_gifts', 'chat_participants', 'profiles', 'gifts');

-- Check if functions exist
SELECT routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name IN ('notify_new_message', 'notify_new_gift', 'create_notification');

-- Force create the message notification trigger
DO $$
BEGIN
    -- Drop existing trigger if it exists
    DROP TRIGGER IF EXISTS trigger_notify_new_message ON messages;
    
    -- Create the trigger
    CREATE TRIGGER trigger_notify_new_message
        AFTER INSERT ON messages
        FOR EACH ROW
        EXECUTE FUNCTION notify_new_message();
        
    RAISE NOTICE 'Message notification trigger created successfully';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error creating message trigger: %', SQLERRM;
END $$;

-- Force create the gift notification trigger
DO $$
BEGIN
    -- Drop existing trigger if it exists
    DROP TRIGGER IF EXISTS trigger_notify_new_gift ON user_gifts;
    
    -- Create the trigger
    CREATE TRIGGER trigger_notify_new_gift
        AFTER INSERT ON user_gifts
        FOR EACH ROW
        EXECUTE FUNCTION notify_new_gift();
        
    RAISE NOTICE 'Gift notification trigger created successfully';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error creating gift trigger: %', SQLERRM;
END $$;
