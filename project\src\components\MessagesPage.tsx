import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageSquare,
  Circle,
  Send,
  Crown,
  Info,
  X,
  ChevronLeft,
  Loader2,
  AlertTriangle,
  Trash2,
  <PERSON>ly,
  MoreVertical,
  Edit,
  Image as ImageIcon,
  Paperclip
} from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import { useSimpleProfileImage } from '../hooks/useSimpleProfileImage';
import { Profile } from '../types/supabase';
import ProfileImage from './ProfileImage';

interface Message {
  id: string;
  chat_id: string;
  sender_id: string;
  content: string;
  image_url?: string;
  view_once?: boolean;
  view_count?: number;
  max_views?: number;
  created_at: string;
  updated_at?: string;
  is_edited?: boolean;
  is_deleted?: boolean;
  reply_to_id?: string;
  reply_message?: string;
  read?: boolean;
  sender?: Profile;
}

interface Chat {
  id: string;
  last_message: string;
  last_message_at: string;
  updated_at: string;
  participants: Profile[];
  unread_count?: number;
}

function MessagesPage() {
  const { user } = useAuth();
  const [isVip, setIsVip] = useState<boolean>(false);
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [chatLoading, setChatLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showVipPrompt, setShowVipPrompt] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sendingMessage, setSendingMessage] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [uploadingImage, setUploadingImage] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [selectedImageModal, setSelectedImageModal] = useState<string | null>(null);
  const [viewOnceMode, setViewOnceMode] = useState<boolean>(false);
  const [maxViewsMode, setMaxViewsMode] = useState<number | null>(null);

  // Handle keyboard events for image modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && selectedImageModal) {
        setSelectedImageModal(null);
      }
    };

    if (selectedImageModal) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
      // Add blur effect to main content
      const mainContent = document.querySelector('.messages-main-content');
      if (mainContent) {
        mainContent.classList.add('blur-sm');
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
      // Remove blur effect
      const mainContent = document.querySelector('.messages-main-content');
      if (mainContent) {
        mainContent.classList.remove('blur-sm');
      }
    };
  }, [selectedImageModal]);

  // Check if user is VIP and get user metadata
  useEffect(() => {
    const checkVipStatus = async () => {
      if (!user?.id) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking VIP status:', error);
          return;
        }

        setIsVip(data?.is_vip || false);

        // Store user metadata in session storage for easy access
        sessionStorage.setItem('current_user_metadata', JSON.stringify({
          id: data.id,
          username: data.username,
          is_online: data.is_online,
          is_vip: data.is_vip
        }));

        // Initialize storage buckets
        initializeStorage();

        // If not VIP, show the VIP prompt
        if (!data?.is_vip) {
          setShowVipPrompt(true);
        }
      } catch (err) {
        console.error('Error checking VIP status:', err);
      }
    };

    checkVipStatus();
  }, [user?.id]);

  // Fetch chats
  useEffect(() => {
    if (!user?.id) return;

    // Check if we have a selected chat from sessionStorage
    const selectedChatId = sessionStorage.getItem('selected_chat_id');
    const selectedChatProfileStr = sessionStorage.getItem('selected_chat_profile');

    if (selectedChatId && selectedChatProfileStr) {
      try {
        const selectedChatProfile = JSON.parse(selectedChatProfileStr);
        console.log('Found selected chat in sessionStorage:', { id: selectedChatId, profile: selectedChatProfile });

        // Create a chat object with the profile data
        const chat = {
          id: selectedChatId,
          last_message: '',
          last_message_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          unread_count: 0,
          participants: [selectedChatProfile]
        };

        // Set the selected chat
        setSelectedChat(chat);

        // Fetch messages for this chat
        fetchMessages(selectedChatId);

        // Clear the sessionStorage
        sessionStorage.removeItem('selected_chat_id');
        sessionStorage.removeItem('selected_chat_profile');
      } catch (err) {
        console.error('Error parsing selected chat profile:', err);
      }
    }

    const fetchChats = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching chats for user ID:', user.id);

        // Get all chats where the user is a participant
        const { data: chatParticipantsData, error: chatParticipantsError } = await supabase
          .from('chat_participants')
          .select('chat_id')
          .eq('user_id', user.id);

        console.log('Chat participants data:', { data: chatParticipantsData, error: chatParticipantsError });

        if (chatParticipantsError) {
          console.error('Error fetching chat participants:', chatParticipantsError);
          throw chatParticipantsError;
        }

        if (!chatParticipantsData || chatParticipantsData.length === 0) {
          console.log('No chat participants found');
          setChats([]);
          setLoading(false);
          return;
        }

        const chatIds = chatParticipantsData.map(cp => cp.chat_id);
        console.log('Chat IDs:', chatIds);

        // Get chat details
        const { data: chatsData, error: chatsError } = await supabase
          .from('chats')
          .select('*')
          .in('id', chatIds)
          .order('updated_at', { ascending: false });

        console.log('Chats data:', { data: chatsData, error: chatsError });

        if (chatsError) {
          console.error('Error fetching chats:', chatsError);
          throw chatsError;
        }

        if (!chatsData || chatsData.length === 0) {
          console.log('No chats found');
          setChats([]);
          setLoading(false);
          return;
        }

        // For each chat, get the other participants - simplified approach
        const formattedChats = [];

        for (const chat of chatsData) {
          console.log(`Processing chat ${chat.id}`);

          // Get other participants (not current user)
          const { data: otherParticipants, error: participantsError } = await supabase
            .from('chat_participants')
            .select('user_id')
            .eq('chat_id', chat.id)
            .neq('user_id', user.id);

          console.log(`Other participants for chat ${chat.id}:`, otherParticipants, participantsError);

          if (participantsError || !otherParticipants || otherParticipants.length === 0) {
            console.log(`No other participants found for chat ${chat.id}`);
            continue; // Skip this chat
          }

          const otherUserId = otherParticipants[0].user_id;
          console.log(`Getting profile for user ${otherUserId}`);

          // Get profile for the other user
          const { data: otherUserProfile, error: profileError } = await supabase
            .from('profiles')
            .select('id, username, is_online')
            .eq('id', otherUserId)
            .single();

          console.log(`Profile for user ${otherUserId}:`, otherUserProfile, profileError);

          // Create participant object with fallback values
          const participant = {
            id: otherUserId,
            username: otherUserProfile?.username || 'משתמש',
            is_online: otherUserProfile?.is_online || false,
            profile_image_url: ''
          };

          formattedChats.push({
            id: chat.id,
            last_message: chat.last_message || '',
            last_message_at: chat.last_message_at || chat.updated_at || new Date().toISOString(),
            updated_at: chat.updated_at || new Date().toISOString(),
            unread_count: 0,
            participants: [participant]
          });
        }

        console.log('Formatted chats:', formattedChats);

        // Add a small delay to prevent flickering
        setTimeout(() => {
          setChats(formattedChats);
        }, 100);
      } catch (err) {
        console.error('Error fetching chats:', err);
        setError('אירעה שגיאה בטעינת הצ\'אטים. אנא נסה שוב מאוחר יותר.');
      } finally {
        setLoading(false);
      }
    };

    fetchChats();

    // Set up realtime subscription for chat updates
    const chatsSubscription = supabase
      .channel('chats_changes')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chats'
        },
        (payload) => {
          console.log('Chat updated:', payload);
          // Refresh chats when a chat is updated
          fetchChats();
        }
      )
      .subscribe();

    // Set up realtime subscription for new messages
    const messagesSubscription = supabase
      .channel('messages_changes')
      .on('postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages'
        },
        (payload) => {
          console.log('🔄 New message received via real-time:', payload);
          console.log('📨 Message data:', {
            id: payload.new?.id,
            content: payload.new?.content,
            image_url: payload.new?.image_url,
            sender_id: payload.new?.sender_id
          });

          // If the message is for the selected chat, add it to the messages
          if (selectedChat && payload.new.chat_id === selectedChat.id) {
            fetchMessages(selectedChat.id);
          }
          // Refresh chats to update last message
          fetchChats();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(chatsSubscription);
      supabase.removeChannel(messagesSubscription);
    };
  }, [user?.id, selectedChat?.id]);

  // Fetch messages for selected chat
  const fetchMessages = async (chatId: string) => {
    if (!user?.id || !chatId) return;

    try {
      setChatLoading(true);
      console.log('Fetching messages for chat ID:', chatId);

      // Get messages directly from the messages table
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });

      console.log('📨 Messages data:', { data: messagesData, error: messagesError });

      if (messagesError) {
        console.error('Error fetching messages:', messagesError);
        throw messagesError;
      }

      if (!messagesData || messagesData.length === 0) {
        console.log('No messages found');
        setMessages([]);
        setChatLoading(false);
        return;
      }

      // Debug: Log messages with images
      console.log('🖼️ Messages with images:', messagesData?.filter(m => m.image_url).length || 0);
      messagesData?.forEach(msg => {
        if (msg.image_url) {
          console.log('🖼️ Message with image:', {
            id: msg.id,
            content: msg.content,
            image_url: msg.image_url,
            created_at: msg.created_at
          });
        }
      });

      // Get sender details for each message - simplified approach
      const formattedMessages = [];

      for (const msg of messagesData) {
        console.log(`Processing message ${msg.id} from sender ${msg.sender_id}`);

        const { data: senderData, error: senderError } = await supabase
          .from('profiles')
          .select('id, username, is_online')
          .eq('id', msg.sender_id)
          .single();

        console.log(`Sender data for message ${msg.id}:`, senderData, senderError);

        // Create sender object with fallback values
        const sender = {
          id: msg.sender_id,
          username: senderData?.username || 'משתמש',
          is_online: senderData?.is_online || false,
          profile_image_url: ''
        };

        // Check for localStorage image if no image_url in DB
        let imageUrl = msg.image_url;
        if (!imageUrl) {
          const messageImageKey = `message_image_${msg.id}`;
          const localImage = localStorage.getItem(messageImageKey);
          if (localImage) {
            console.log('🔄 Found image in localStorage for message:', msg.id);
            imageUrl = localImage;
          }
        }

        formattedMessages.push({
          id: msg.id,
          chat_id: msg.chat_id,
          sender_id: msg.sender_id,
          content: msg.content,
          image_url: imageUrl,
          created_at: msg.created_at,
          updated_at: msg.updated_at || msg.created_at,
          is_edited: msg.is_edited || false,
          is_deleted: msg.is_deleted || false,
          reply_to_id: msg.reply_to_id,
          reply_message: '',
          read: msg.read || false,
          sender: sender
        });
      }

      console.log('Formatted messages:', formattedMessages);
      setMessages(formattedMessages);

      // Update the unread count in the chat list
      const updatedChats = chats.map(c => {
        if (c.id === chatId) {
          return { ...c, unread_count: 0 };
        }
        return c;
      });

      setChats(updatedChats);
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError('אירעה שגיאה בטעינת ההודעות. אנא נסה שוב מאוחר יותר.');
    } finally {
      setChatLoading(false);
      // Scroll to bottom
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  };

  // Handle chat selection
  const handleSelectChat = (chat: Chat) => {
    setSelectedChat(chat);
    fetchMessages(chat.id);
  };

  // Handle message deletion
  const [messageToDelete, setMessageToDelete] = useState<string | null>(null);
  const [deletingMessage, setDeletingMessage] = useState<boolean>(false);

  const handleDeleteMessage = async (messageId: string) => {
    if (!user?.id || !messageId) return;

    try {
      setDeletingMessage(true);

      // Call the function to delete the message
      const { data, error } = await supabase.rpc(
        'delete_message',
        {
          p_message_id: messageId,
          p_user_id: user.id
        }
      );

      if (error) {
        console.error('Error deleting message:', error);
        throw error;
      }

      console.log('Message deleted:', data);

      // Update the message in the local state
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, is_deleted: true, content: 'הודעה זו נמחקה' }
            : msg
        )
      );

      // Close the delete confirmation
      setMessageToDelete(null);
    } catch (err) {
      console.error('Error deleting message:', err);
      setError('אירעה שגיאה במחיקת ההודעה. אנא נסה שוב מאוחר יותר.');
    } finally {
      setDeletingMessage(false);
    }
  };

  // Handle chat deletion
  const [chatToDelete, setChatToDelete] = useState<string | null>(null);
  const [deletingChat, setDeletingChat] = useState<boolean>(false);

  const handleDeleteChat = async (chatId: string) => {
    if (!user?.id || !chatId) return;

    try {
      setDeletingChat(true);

      // Call the function to delete the chat
      const { data, error } = await supabase.rpc(
        'delete_chat',
        {
          p_chat_id: chatId,
          p_user_id: user.id
        }
      );

      if (error) {
        console.error('Error deleting chat:', error);
        throw error;
      }

      console.log('Chat deleted:', data);

      // Remove the chat from the local state
      setChats(prev => prev.filter(c => c.id !== chatId));

      // If the deleted chat was selected, clear the selection
      if (selectedChat?.id === chatId) {
        setSelectedChat(null);
        setMessages([]);
      }

      // Close the delete confirmation
      setChatToDelete(null);
    } catch (err) {
      console.error('Error deleting chat:', err);
      setError('אירעה שגיאה במחיקת הצ\'אט. אנא נסה שוב מאוחר יותר.');
    } finally {
      setDeletingChat(false);
    }
  };

  // Handle reply to message
  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);

  // Handle view once functionality
  const handleImageView = async (message: Message) => {
    const isViewOnceFallback = sessionStorage.getItem('view_once_fallback') === 'true';

    // If in fallback mode or no view restrictions, just open modal
    if (isViewOnceFallback || (!message.view_once && !message.max_views)) {
      setSelectedImageModal(message.image_url || null);
      return;
    }

    // Check if user can still view this image
    const currentViews = message.view_count || 0;
    const maxViews = message.max_views || 1;

    if (currentViews >= maxViews) {
      alert('התמונה הזו כבר נצפתה והוסרה');
      return;
    }

    // Update view count
    try {
      const { error } = await supabase
        .from('messages')
        .update({
          view_count: currentViews + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', message.id);

      if (error) {
        console.error('Error updating view count:', error);
        // If update fails, try without view_count column
        try {
          await supabase
            .from('messages')
            .update({ updated_at: new Date().toISOString() })
            .eq('id', message.id);
        } catch (fallbackError) {
          console.warn('Could not update message at all:', fallbackError);
        }
      }

      // Open modal
      setSelectedImageModal(message.image_url || null);

      // If this was the last view, remove the image after a delay
      if (currentViews + 1 >= maxViews) {
        setTimeout(() => {
          setSelectedImageModal(null);
          // Refresh messages to show updated state
          if (selectedChat) {
            fetchMessages(selectedChat.id);
          }
        }, 5000); // 5 seconds to view
      }

    } catch (error) {
      console.error('Error handling image view:', error);
      // Fallback: just show the image
      setSelectedImageModal(message.image_url || null);
    }
  };

  // Initialize storage buckets and test functionality
  const initializeStorage = async () => {
    try {
      console.log('🔧 Initializing storage and testing functionality...');

      // Check if buckets exist instead of trying to create them
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.warn('⚠️ Could not list storage buckets:', listError);
      } else {
        const photosExists = buckets.some(b => b.id === 'photos');
        const chatImagesExists = buckets.some(b => b.id === 'chat-images');

        console.log('📦 Available buckets:', {
          photos: photosExists,
          'chat-images': chatImagesExists,
          total: buckets.length
        });

        // Store which buckets are available
        sessionStorage.setItem('available_buckets', JSON.stringify({
          photos: photosExists,
          chatImages: chatImagesExists
        }));
      }

      // Test if we can add view-once columns to messages table
      try {
        console.log('🧪 Testing view-once functionality...');

        // Try to select with new columns to see if they exist
        const { data: testData, error: testError } = await supabase
          .from('messages')
          .select('id, view_once, view_count, max_views')
          .limit(1);

        if (testError && testError.message.includes('does not exist')) {
          console.log('⚠️ View-once columns not available, using fallback mode');
          // Set a flag to indicate we're in fallback mode
          sessionStorage.setItem('view_once_fallback', 'true');
        } else {
          console.log('✅ View-once functionality available');
          sessionStorage.removeItem('view_once_fallback');
        }
      } catch (error) {
        console.log('⚠️ View-once test failed, using fallback mode');
        sessionStorage.setItem('view_once_fallback', 'true');
      }

    } catch (error) {
      console.warn('⚠️ Storage initialization failed:', error);
    }
  };

  // Debug function to check database structure
  const checkDatabaseStructure = async () => {
    try {
      console.log('🔍 Checking database structure...');

      // Try to get a sample message to see the structure
      const { data: sampleMessage, error: sampleError } = await supabase
        .from('messages')
        .select('*')
        .limit(1)
        .single();

      if (sampleError && !sampleError.message.includes('No rows')) {
        console.error('❌ Error getting sample message:', sampleError);
      } else if (sampleMessage) {
        console.log('📝 Sample message structure:', Object.keys(sampleMessage));
        console.log('🖼️ Has image_url column:', 'image_url' in sampleMessage);
      }

      // Try to insert a test message with image_url
      const testImageUrl = 'data:image/png;base64,test';
      const { error: testError } = await supabase
        .from('messages')
        .insert({
          chat_id: '00000000-0000-0000-0000-000000000000',
          sender_id: user?.id || '00000000-0000-0000-0000-000000000000',
          content: 'test',
          image_url: testImageUrl
        })
        .select()
        .single();

      const supportsImageUrl = !testError || !testError.message.includes('image_url');

      // Clean up test message if it was inserted
      if (!testError) {
        await supabase
          .from('messages')
          .delete()
          .eq('content', 'test')
          .eq('sender_id', user?.id);
      }

      return supportsImageUrl;

    } catch (error) {
      console.error('💥 Error checking database structure:', error);
      return false;
    }
  };

  // Debug function to check storage
  const checkStorage = async () => {
    try {
      console.log('🔍 Checking storage buckets...');

      // List all buckets
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

      if (bucketsError) {
        console.error('❌ Error listing buckets:', bucketsError);
        alert('שגיאה בבדיקת Storage: ' + bucketsError.message);
        return;
      }

      console.log('📦 Available buckets:', buckets);

      // Check specific buckets
      const photosExists = buckets.some(b => b.id === 'photos');
      const chatImagesExists = buckets.some(b => b.id === 'chat-images');

      // Test upload permissions
      let uploadPermissions = 'לא נבדק';
      try {
        const testBlob = new Blob(['test'], { type: 'text/plain' });
        const testFileName = `test_${Date.now()}.txt`;

        if (photosExists) {
          const { error: uploadError } = await supabase.storage
            .from('photos')
            .upload(testFileName, testBlob);

          if (!uploadError) {
            uploadPermissions = '✅ photos bucket';
            // Clean up
            await supabase.storage.from('photos').remove([testFileName]);
          } else if (chatImagesExists) {
            const { error: uploadError2 } = await supabase.storage
              .from('chat-images')
              .upload(testFileName, testBlob);

            if (!uploadError2) {
              uploadPermissions = '✅ chat-images bucket';
              // Clean up
              await supabase.storage.from('chat-images').remove([testFileName]);
            } else {
              uploadPermissions = '❌ אין הרשאות העלאה';
            }
          }
        }
      } catch (testError) {
        uploadPermissions = '❌ שגיאה בבדיקה';
      }

      // Check database structure
      const supportsImages = await checkDatabaseStructure();
      const isViewOnceFallback = sessionStorage.getItem('view_once_fallback') === 'true';

      const status = [
        `📦 Storage Buckets:`,
        `• photos: ${photosExists ? '✅ קיים' : '❌ לא קיים'}`,
        `• chat-images: ${chatImagesExists ? '✅ קיים' : '❌ לא קיים'}`,
        `• הרשאות העלאה: ${uploadPermissions}`,
        ``,
        `🗄️ Database:`,
        `• image_url column: ${supportsImages ? '✅ נתמך' : '❌ לא נתמך'}`,
        `• view-once columns: ${isViewOnceFallback ? '❌ לא נתמך (fallback)' : '✅ נתמך'}`,
        ``,
        `🔗 Supabase URL: ${supabase.supabaseUrl}`,
        `👤 User ID: ${user?.id}`,
        `🌐 Online: ${navigator.onLine ? 'כן' : 'לא'}`
      ].join('\n');

      alert(status);

    } catch (error) {
      console.error('💥 Error checking storage:', error);
      alert('שגיאה בבדיקת Storage: ' + error);
    }
  };

  // Test Supabase connection
  const testSupabaseConnection = async () => {
    try {
      console.log('🔗 Testing Supabase connection...');

      // Test basic connection
      const { data, error } = await supabase.from('profiles').select('id').limit(1);

      if (error) {
        console.error('❌ Supabase connection failed:', error);
        return false;
      }

      console.log('✅ Supabase connection successful');
      return true;
    } catch (error) {
      console.error('💥 Supabase connection error:', error);
      return false;
    }
  };

  // Handle image selection
  const handleImageSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('גודל התמונה חייב להיות קטן מ-5MB');
        return;
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        alert('ניתן להעלות רק קבצי תמונה');
        return;
      }

      // Check if storage buckets are available
      const availableBuckets = JSON.parse(sessionStorage.getItem('available_buckets') || '{}');
      if (!availableBuckets.photos && !availableBuckets.chatImages) {
        alert('אין buckets זמינים להעלאת תמונות. נסה שוב מאוחר יותר או פנה למנהל המערכת.');
        return;
      }

      // Test connection before setting image
      const connectionOk = await testSupabaseConnection();
      if (!connectionOk) {
        alert('בעיה בחיבור לשרת. בדוק את החיבור לאינטרנט ונסה שוב.');
        return;
      }

      setSelectedImage(file);
      console.log('✅ Image selected:', file.name);
    }
  };

  // Upload image to Supabase storage
  const uploadImage = async (file: File): Promise<string | null> => {
    try {
      setUploadingImage(true);

      console.log('🖼️ Starting image upload:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        userId: user?.id
      });

      // Validate file
      if (!file.type.startsWith('image/')) {
        console.error('❌ Invalid file type:', file.type);
        alert('ניתן להעלות רק קבצי תמונה');
        return null;
      }

      if (file.size > 5 * 1024 * 1024) {
        console.error('❌ File too large:', file.size);
        alert('גודל התמונה חייב להיות קטן מ-5MB');
        return null;
      }

      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${user?.id}_${Date.now()}.${fileExt}`;
      console.log('📝 Generated filename:', fileName);

      // Get available buckets from session storage
      const availableBuckets = JSON.parse(sessionStorage.getItem('available_buckets') || '{}');
      console.log('📦 Available buckets for upload:', availableBuckets);

      let uploadSuccess = false;
      let finalUrl = null;

      // Method 1: Try photos bucket if available
      if (availableBuckets.photos) {
        console.log('📤 Trying photos bucket...');
        const { data, error } = await supabase.storage
          .from('photos')
          .upload(fileName, file);

        if (!error && data) {
          console.log('✅ Upload to photos bucket successful:', data);

          // Get public URL from photos bucket
          const { data: urlData } = supabase.storage
            .from('photos')
            .getPublicUrl(fileName);

          console.log('🔗 Generated URL:', urlData.publicUrl);
          finalUrl = urlData.publicUrl;
          uploadSuccess = true;
        } else {
          console.log('⚠️ Photos bucket upload failed:', error);
        }
      }

      // Method 2: Try chat-images bucket if available and photos failed
      if (!uploadSuccess && availableBuckets.chatImages) {
        console.log('📤 Trying chat-images bucket...');
        const { data, error } = await supabase.storage
          .from('chat-images')
          .upload(fileName, file);

        if (!error && data) {
          console.log('✅ Upload to chat-images bucket successful:', data);

          // Get public URL from chat-images bucket
          const { data: urlData } = supabase.storage
            .from('chat-images')
            .getPublicUrl(fileName);

          console.log('🔗 Generated URL:', urlData.publicUrl);
          finalUrl = urlData.publicUrl;
          uploadSuccess = true;
        } else {
          console.log('⚠️ Chat-images bucket upload failed:', error);
        }
      }

      // If we got a successful upload, return the URL
      if (uploadSuccess && finalUrl) {
        return finalUrl;
      }

      // Method 4: Try base64 encoding as last resort
      console.log('🔄 All storage methods failed, trying base64...');
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const base64 = e.target?.result as string;
          console.log('✅ Base64 encoding successful, length:', base64.length);
          resolve(base64);
        };
        reader.onerror = (e) => {
          console.error('❌ Base64 encoding failed:', e);
          resolve(null);
        };
        reader.readAsDataURL(file);
      });

    } catch (error) {
      console.error('💥 Unexpected error in uploadImage:', error);
      return null;
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle sending a message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id || !selectedChat || (!newMessage.trim() && !selectedImage)) {
      if (!isVip) {
        setShowVipPrompt(true);
      }
      return;
    }

    // Check internet connection
    if (!navigator.onLine) {
      setError('אין חיבור לאינטרנט. בדוק את החיבור ונסה שוב.');
      return;
    }

    try {
      setSendingMessage(true);
      setError(null); // Clear any previous errors

      console.log('📤 Sending message to chat:', selectedChat.id);

      let messageContent = newMessage.trim();
      let imageUrl = null;

      // Upload image if selected
      if (selectedImage) {
        console.log('🖼️ Uploading selected image...');
        imageUrl = await uploadImage(selectedImage);
        if (!imageUrl) {
          console.error('❌ Image upload failed completely');
          setError('שגיאה בהעלאת התמונה. בדוק את החיבור לאינטרנט ונסה שוב.');
          return;
        }
        console.log('✅ Image uploaded successfully:', imageUrl);
      }

      console.log('Sending message with content:', messageContent, 'and image:', imageUrl);

      let insertedMessage;

      // Always use direct insert approach for maximum compatibility
      console.log('📤 Using direct insert approach...');

      // Create message object
      const messageObj = {
        chat_id: selectedChat.id,
        sender_id: user.id,
        content: messageContent || (imageUrl ? 'שלח תמונה' : ''),
        read: false
      };

      // Add reply_to_id if replying to a message
      if (replyToMessage) {
        messageObj['reply_to_id'] = replyToMessage.id;
      }

      // Try to add image_url if we have an image
      if (imageUrl) {
        try {
          // Check if we're in fallback mode
          const isViewOnceFallback = sessionStorage.getItem('view_once_fallback') === 'true';

          // Test if image_url column exists by trying to insert with it
          const testObj = {
            ...messageObj,
            image_url: imageUrl
          };

          // Add view-once fields only if not in fallback mode
          if (!isViewOnceFallback) {
            testObj.view_once = viewOnceMode;
            testObj.max_views = maxViewsMode;
            testObj.view_count = 0;
          }

          console.log('🧪 Testing insert with image_url column and view options...', {
            fallbackMode: isViewOnceFallback,
            viewOnce: viewOnceMode,
            maxViews: maxViewsMode
          });

          const { data: testMessage, error: testError } = await supabase
            .from('messages')
            .insert(testObj)
            .select('*')
            .single();

          if (testError) {
            console.warn('❌ image_url column not supported, inserting without it:', testError);
            // Insert without image_url
            const { data: directMessage, error: directError } = await supabase
              .from('messages')
              .insert(messageObj)
              .select('*')
              .single();

            if (directError) {
              console.error('❌ Direct insert failed:', directError);
              // If even basic insert fails, try with minimal data
              const minimalObj = {
                chat_id: selectedChat.id,
                sender_id: user.id,
                content: messageContent || 'הודעה',
                read: false
              };

              const { data: minimalMessage, error: minimalError } = await supabase
                .from('messages')
                .insert(minimalObj)
                .select('*')
                .single();

              if (minimalError) {
                throw minimalError;
              }

              insertedMessage = minimalMessage;
            } else {
              insertedMessage = directMessage;
            }
          } else {
            console.log('✅ Insert with image_url successful');
            insertedMessage = testMessage;
          }
        } catch (error) {
          console.warn('❌ Image insert failed, trying without image:', error);
          // Fallback to insert without image
          const { data: directMessage, error: directError } = await supabase
            .from('messages')
            .insert(messageObj)
            .select('*')
            .single();

          if (directError) {
            console.error('❌ Fallback insert failed:', directError);
            throw directError;
          }

          insertedMessage = directMessage;
        }
      } else {
        // No image, simple insert
        console.log('📝 Inserting text-only message...');
        const { data: directMessage, error: directError } = await supabase
          .from('messages')
          .insert(messageObj)
          .select('*')
          .single();

        if (directError) {
          console.error('❌ Text message insert failed:', directError);
          throw directError;
        }

        insertedMessage = directMessage;
      }

      // Update chat manually
      console.log('📝 Updating chat last message...');
      const chatDisplayMessage = imageUrl && !messageContent ? 'שלח תמונה' : messageContent;
      await supabase
        .from('chats')
        .update({
          last_message: chatDisplayMessage,
          last_message_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedChat.id);

      if (!insertedMessage) {
        console.error('No message was inserted');
        throw new Error('Failed to insert message');
      }

      console.log('Message processed successfully:', insertedMessage);

      // The send_message function already updates the chat, no need to do it manually

      // Clear the input, image, reply state, and view options
      setNewMessage('');
      setSelectedImage(null);
      setReplyToMessage(null);
      setViewOnceMode(false);
      setMaxViewsMode(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Get current user metadata
      let userMetadata;
      try {
        userMetadata = JSON.parse(sessionStorage.getItem('current_user_metadata') || '{}');
      } catch (e) {
        console.error('Error parsing user metadata:', e);
        userMetadata = {};
      }

      // Debug: Check what was actually inserted
      console.log('🔍 Inserted message data:', insertedMessage);
      console.log('🖼️ Image URL from insert:', insertedMessage.image_url);
      console.log('🖼️ Original image URL:', imageUrl);

      // If image wasn't saved to DB, save it locally for this session
      let finalImageUrl = insertedMessage.image_url || imageUrl;

      if (imageUrl && !insertedMessage.image_url) {
        console.log('⚠️ Image not saved to DB, using localStorage fallback');
        const messageImageKey = `message_image_${insertedMessage.id}`;
        localStorage.setItem(messageImageKey, imageUrl);
        finalImageUrl = imageUrl;
      }

      // Create a new message object for the UI
      const newMessageObj = {
        id: insertedMessage.id,
        chat_id: insertedMessage.chat_id,
        sender_id: insertedMessage.sender_id,
        content: insertedMessage.content,
        image_url: finalImageUrl,
        created_at: insertedMessage.created_at,
        updated_at: insertedMessage.updated_at || insertedMessage.created_at,
        is_edited: false,
        is_deleted: false,
        reply_to_id: insertedMessage.reply_to_id,
        reply_message: replyToMessage?.content,
        read: insertedMessage.read || false,
        sender: {
          id: user.id,
          username: userMetadata.username || user.email || 'אני',
          is_online: true,
          profile_image_url: ''
        }
      };

      console.log('📝 Message object for UI:', newMessageObj);

      // Update messages state
      setMessages(prev => [...prev, newMessageObj]);

      // Update the chat in the chats list
      const displayMessage = imageUrl && !messageContent ? 'שלח תמונה' : messageContent;
      setChats(prevChats =>
        prevChats.map(chat =>
          chat.id === selectedChat.id
            ? {
                ...chat,
                last_message: displayMessage,
                last_message_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              }
            : chat
        )
      );

      // Scroll to bottom
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } catch (err) {
      console.error('Error sending message:', err);

      // More detailed error message
      let errorMessage = 'אירעה שגיאה בשליחת ההודעה.';
      if (err instanceof Error) {
        console.error('Error details:', err.message);
        if (err.message.includes('image')) {
          errorMessage = 'שגיאה בהעלאת התמונה. נסה תמונה קטנה יותר.';
        } else if (err.message.includes('content')) {
          errorMessage = 'שגיאה בתוכן ההודעה. נסה שוב.';
        } else if (err.message.includes('participant')) {
          errorMessage = 'אין הרשאה לשלוח הודעה בצ\'אט זה.';
        }
      }

      setError(errorMessage + ' אנא נסה שוב מאוחר יותר.');
    } finally {
      setSendingMessage(false);
    }
  };

  // Filter chats by search term
  const filteredChats = chats.filter(chat => {
    if (!searchTerm) return true;

    // Check if any participant's name includes the search term
    return chat.participants.some(p =>
      p.username?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Format date to relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return 'עכשיו';
    } else if (diffMins < 60) {
      return `לפני ${diffMins} דקות`;
    } else if (diffHours < 24) {
      return `לפני ${diffHours} שעות`;
    } else if (diffDays === 1) {
      return 'אתמול';
    } else {
      return date.toLocaleDateString('he-IL');
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm overflow-hidden messages-main-content transition-all duration-300">
      {/* Delete Message Confirmation */}
      <AnimatePresence>
        {messageToDelete && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-lg p-6 max-w-sm w-full"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-3">מחיקת הודעה</h3>
              <p className="text-gray-600 mb-4">האם אתה בטוח שברצונך למחוק הודעה זו? פעולה זו אינה ניתנת לביטול.</p>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setMessageToDelete(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  ביטול
                </button>
                <button
                  onClick={() => messageToDelete && handleDeleteMessage(messageToDelete)}
                  disabled={deletingMessage}
                  className="px-4 py-2 text-white bg-red-500 rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {deletingMessage ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      מוחק...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      מחק הודעה
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Delete Chat Confirmation */}
      <AnimatePresence>
        {chatToDelete && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-lg p-6 max-w-sm w-full"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-3">מחיקת שיחה</h3>
              <p className="text-gray-600 mb-4">האם אתה בטוח שברצונך למחוק שיחה זו? פעולה זו אינה ניתנת לביטול.</p>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setChatToDelete(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  ביטול
                </button>
                <button
                  onClick={() => chatToDelete && handleDeleteChat(chatToDelete)}
                  disabled={deletingChat}
                  className="px-4 py-2 text-white bg-red-500 rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {deletingChat ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      מוחק...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      מחק שיחה
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* VIP Prompt */}
      <AnimatePresence>
        {showVipPrompt && !isVip && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gradient-to-r from-yellow-50 to-amber-50 border border-amber-200 p-4 relative"
          >
            <button
              onClick={() => setShowVipPrompt(false)}
              className="absolute top-2 right-2 text-amber-500 hover:text-amber-700"
            >
              <X className="w-5 h-5" />
            </button>

            <div className="flex items-start gap-3">
              <div className="bg-amber-100 rounded-full p-2 mt-1">
                <Crown className="w-5 h-5 text-amber-600" />
              </div>
              <div>
                <h3 className="font-semibold text-amber-800 mb-1">שדרג ל-VIP כדי לשלוח הודעות</h3>
                <p className="text-sm text-amber-700">
                  רק משתמשי VIP יכולים לשלוח ולקבל הודעות. שדרג עכשיו כדי לפתוח את כל האפשרויות!
                </p>
                <button
                  onClick={() => window.location.href = '/vip'}
                  className="mt-2 bg-gradient-to-r from-amber-500 to-amber-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-amber-600 hover:to-amber-700 transition-colors"
                >
                  שדרג ל-VIP
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="flex h-[calc(100vh-12rem)] max-h-[600px]">
        {/* Chat List */}
        <div className={`w-full md:w-1/3 border-r border-gray-100 ${selectedChat ? 'hidden md:block' : 'block'}`}>
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-pink-500" />
                הודעות
              </h2>

              {/* Debug button */}
              <button
                onClick={checkStorage}
                className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 px-2 py-1 rounded"
                title="בדיקת Storage"
              >
                🔧
              </button>
            </div>

            <div className="relative">
              <input
                type="text"
                placeholder="חיפוש..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full p-2 pl-10 border border-gray-200 rounded-lg text-sm"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="w-8 h-8 text-pink-500 animate-spin" />
            </div>
          ) : error ? (
            <div className="p-4 text-center text-red-500">
              <AlertTriangle className="w-8 h-8 mx-auto mb-2" />
              <p>{error}</p>
            </div>
          ) : filteredChats.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <MessageSquare className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p className="text-lg font-medium">אין הודעות</p>
              <p className="text-sm">התחל שיחה חדשה עם משתמשים אחרים</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100 overflow-y-auto max-h-[calc(100vh-16rem)]">
              {filteredChats.map((chat) => {
                const otherParticipant = chat.participants[0];
                const isOnline = otherParticipant?.is_online;

                console.log(`Chat list - chat ${chat.id} participant:`, otherParticipant);

                return (
                  <div
                    key={chat.id}
                    onClick={() => handleSelectChat(chat)}
                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer flex items-center gap-4 group ${
                      selectedChat?.id === chat.id ? 'bg-pink-50' : ''
                    }`}
                  >
                    <ProfileImage
                      userId={otherParticipant?.id || ''}
                      username={otherParticipant?.username}
                      size="xl"
                      showOnlineStatus={true}
                      isOnline={isOnline}
                    />

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900 truncate">
                          {otherParticipant?.username || 'משתמש'}
                        </h3>
                        <span className="text-sm text-gray-500">
                          {formatRelativeTime(chat.updated_at)}
                        </span>
                      </div>

                      <p className={`text-sm truncate ${
                        chat.unread_count ? 'text-gray-900 font-medium' : 'text-gray-500'
                      }`}>
                        {chat.last_message || 'אין הודעות'}
                      </p>
                    </div>

                    <div className="flex items-center">
                      {chat.unread_count > 0 && (
                        <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white text-xs mr-2">
                          {chat.unread_count}
                        </div>
                      )}

                      {/* Delete chat button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setChatToDelete(chat.id);
                        }}
                        className="p-1 text-gray-400 hover:text-red-500 rounded-full hover:bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity"
                        title="מחק שיחה"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Chat Messages */}
        <div className={`w-full md:w-2/3 flex flex-col ${!selectedChat ? 'hidden md:flex' : 'flex'}`}>
          {!selectedChat ? (
            <div className="flex-1 flex flex-col items-center justify-center p-6 text-center text-gray-500">
              <MessageSquare className="w-16 h-16 mb-4 text-gray-300" />
              <h3 className="text-xl font-medium mb-2">בחר שיחה</h3>
              <p className="text-sm max-w-md">בחר שיחה מהרשימה כדי להציג את ההודעות</p>
            </div>
          ) : (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-gray-100 flex items-center">
                <button
                  onClick={() => setSelectedChat(null)}
                  className="md:hidden mr-2 text-gray-500"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>

                <ProfileImage
                  userId={selectedChat.participants[0]?.id || ''}
                  username={selectedChat.participants[0]?.username}
                  size="lg"
                  showOnlineStatus={true}
                  isOnline={selectedChat.participants[0]?.is_online}
                />

                <div className="ml-3">
                  <h3 className="font-medium text-gray-900">
                    {(() => {
                      console.log('Chat header - selected chat participant:', selectedChat.participants[0]);
                      return selectedChat.participants[0]?.username || 'משתמש';
                    })()}
                  </h3>
                  <p className="text-xs text-gray-500">
                    {selectedChat.participants[0]?.is_online ? 'מחובר/ת' : 'לא מחובר/ת'}
                  </p>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                {chatLoading ? (
                  <div className="flex justify-center items-center h-full">
                    <Loader2 className="w-8 h-8 text-pink-500 animate-spin" />
                  </div>
                ) : messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-center text-gray-500">
                    <MessageSquare className="w-12 h-12 mb-2 text-gray-300" />
                    <p>אין הודעות עדיין</p>
                    <p className="text-sm">התחל שיחה עם {selectedChat.participants[0]?.username}</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {messages.map((message) => {
                      const isSentByMe = message.sender_id === user?.id;

                      return (
                        <div
                          key={message.id}
                          className={`flex items-end gap-2 ${isSentByMe ? 'justify-end' : 'justify-start'}`}
                        >
                          {/* Show sender's profile image for messages not sent by me */}
                          {!isSentByMe && (
                            <div className="flex-shrink-0">
                              <ProfileImage
                                userId={message.sender_id}
                                username={message.sender?.username}
                                size="md"
                                showOnlineStatus={false}
                              />
                            </div>
                          )}

                          <div className="relative group">
                            {/* Message actions menu for my messages */}
                            {isSentByMe && !message.is_deleted && (
                              <div className="absolute top-0 left-0 -translate-x-full opacity-0 group-hover:opacity-100 transition-opacity">
                                <div className="flex items-center gap-1 bg-white rounded-lg shadow-md p-1">
                                  <button
                                    onClick={() => setMessageToDelete(message.id)}
                                    className="p-1 text-gray-500 hover:text-red-500 rounded-full hover:bg-gray-100"
                                    title="מחק הודעה"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>
                            )}

                            <div
                              className={`max-w-[70%] min-w-[200px] p-3 rounded-lg ${
                                message.is_deleted
                                  ? 'bg-gray-100 text-gray-500 italic'
                                  : isSentByMe
                                    ? 'bg-pink-500 text-white rounded-br-none'
                                    : 'bg-white text-gray-800 rounded-bl-none shadow-sm'
                              }`}
                            >
                              {/* Show sender's username for messages not sent by me */}
                              {!isSentByMe && (
                                <p className="text-xs font-medium text-gray-500 mb-1">
                                  {message.sender?.username || 'משתמש'}
                                </p>
                              )}

                              {/* Reply info */}
                              {message.reply_to_id && message.reply_message && (
                                <div className={`text-xs mb-2 p-2 rounded ${
                                  isSentByMe ? 'bg-pink-600 text-pink-100' : 'bg-gray-100 text-gray-600'
                                }`}>
                                  <p className="font-medium">תגובה להודעה:</p>
                                  <p className="truncate">{message.reply_message}</p>
                                </div>
                              )}

                              {/* Message content */}
                              {message.content && (
                                <p className="break-words whitespace-pre-wrap">{message.content}</p>
                              )}

                              {/* Message image */}
                              {(() => {
                                if (!message.image_url) {
                                  return null;
                                }

                                // Check if image has been viewed too many times
                                const currentViews = message.view_count || 0;
                                const maxViews = message.max_views || 0;
                                const isViewLimited = maxViews > 0;
                                const isExpired = isViewLimited && currentViews >= maxViews;

                                if (isExpired) {
                                  return (
                                    <div className="mt-2 p-3 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                                      <div className="text-center text-gray-500">
                                        <div className="text-2xl mb-1">🔒</div>
                                        <div className="text-sm">התמונה נצפתה ונמחקה</div>
                                        <div className="text-xs text-gray-400">
                                          {maxViews === 1 ? 'צפיה פעם אחת' : `צפיה ${maxViews} פעמים`}
                                        </div>
                                      </div>
                                    </div>
                                  );
                                }

                                return (
                                  <div className="mt-2">
                                    {/* View limit indicator */}
                                    {isViewLimited && (
                                      <div className="text-xs text-blue-600 mb-1 flex items-center gap-1">
                                        🔒 {maxViews === 1 ? 'צפיה פעם אחת' : `צפיה ${maxViews} פעמים`}
                                        {currentViews > 0 && (
                                          <span className="text-gray-500">
                                            (נצפה {currentViews}/{maxViews})
                                          </span>
                                        )}
                                      </div>
                                    )}

                                    <img
                                      src={message.image_url}
                                      alt="תמונה שנשלחה"
                                      className="max-w-xs max-h-64 rounded-lg object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                      onClick={() => handleImageView(message)}
                                      onError={(e) => {
                                        console.error('❌ Error loading image:', message.image_url);
                                        e.currentTarget.style.display = 'none';
                                        // Show error message
                                        const errorDiv = document.createElement('div');
                                        errorDiv.className = 'text-red-500 text-sm p-2 bg-red-50 rounded';
                                        errorDiv.textContent = 'שגיאה בטעינת התמונה';
                                        e.currentTarget.parentNode?.appendChild(errorDiv);
                                      }}
                                    />
                                  </div>
                                );
                              })()}

                              <div className={`flex items-center justify-between text-xs mt-1 ${
                                isSentByMe ? 'text-pink-200' : 'text-gray-500'
                              }`}>
                                <span>{formatRelativeTime(message.created_at)}</span>

                                {/* Show edited indicator */}
                                {message.is_edited && (
                                  <span className="ml-1">(נערך)</span>
                                )}

                                {/* Reply button for non-deleted messages */}
                                {!message.is_deleted && (
                                  <button
                                    onClick={() => setReplyToMessage(message)}
                                    className={`ml-2 ${
                                      isSentByMe ? 'text-pink-200 hover:text-white' : 'text-gray-400 hover:text-gray-600'
                                    }`}
                                    title="השב להודעה זו"
                                  >
                                    <Reply className="w-3 h-3" />
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Show my profile image for messages sent by me */}
                          {isSentByMe && (
                            <div className="flex-shrink-0">
                              <img
                                src={(() => {
                                  // Try to get profile image from session storage
                                  try {
                                    const userMetadata = JSON.parse(sessionStorage.getItem('current_user_metadata') || '{}');
                                    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE1IiBmaWxsPSIjOUI5QkEzIi8+CjxwYXRoIGQ9Ik0yNSA3NUMyNSA2NS4zMzUgMzMuMzM1IDU3IDQzIDU3SDE1N0M2Ni42NjUgNTcgNzUgNjUuMzM1IDc1IDc1VjgwSDI1Vjc1WiIgZmlsbD0iIzlCOUJBMyIvPgo8L3N2Zz4K';
                                  } catch (err) {
                                    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE1IiBmaWxsPSIjOUI5QkEzIi8+CjxwYXRoIGQ9Ik0yNSA3NUMyNSA2NS4zMzUgMzMuMzM1IDU3IDQzIDU3SDE1N0M2Ni42NjUgNTcgNzUgNjUuMzM1IDc1IDc1VjgwSDI1Vjc1WiIgZmlsbD0iIzlCOUJBMyIvPgo8L3N2Zz4K';
                                  }
                                })()}
                                alt="אני"
                                className="w-8 h-8 rounded-full object-cover"
                                onError={(e) => {
                                  // If image fails to load, use default avatar
                                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE1IiBmaWxsPSIjOUI5QkEzIi8+CjxwYXRoIGQ9Ik0yNSA3NUMyNSA2NS4zMzUgMzMuMzM1IDU3IDQzIDU3SDE1N0M2Ni42NjUgNTcgNzUgNjUuMzM1IDc1IDc1VjgwSDI1Vjc1WiIgZmlsbD0iIzlCOUJBMyIvPgo8L3N2Zz4K';
                                }}
                              />
                            </div>
                          )}
                        </div>
                      );
                    })}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </div>

              {/* Message Input */}
              <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-100 bg-white">
                {/* Recipient Info */}
                <div className="flex items-center mb-3">
                  <div className="mr-2">
                    <ProfileImage
                      userId={selectedChat.participants[0]?.id || ''}
                      username={selectedChat.participants[0]?.username}
                      size="md"
                      showOnlineStatus={true}
                      isOnline={selectedChat.participants[0]?.is_online}
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">
                      שליחת הודעה אל: {selectedChat.participants[0]?.username || 'משתמש'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {selectedChat.participants[0]?.is_online ? 'מחובר/ת' : 'לא מחובר/ת'}
                    </p>
                  </div>
                </div>

                {/* Reply indicator */}
                {replyToMessage && (
                  <div className="mb-2 p-2 bg-gray-100 rounded-lg flex items-start">
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-700">
                        תגובה להודעה מאת: {replyToMessage.sender?.username || 'משתמש'}
                      </p>
                      <p className="text-sm text-gray-600 truncate">{replyToMessage.content}</p>
                    </div>
                    <button
                      onClick={() => setReplyToMessage(null)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                )}

                {/* Selected Image Preview */}
                {selectedImage && (
                  <div className="mb-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <ImageIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-700">{selectedImage.name}</span>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedImage(null);
                          setViewOnceMode(false);
                          setMaxViewsMode(null);
                          if (fileInputRef.current) fileInputRef.current.value = '';
                        }}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>

                    {/* View mode options */}
                    <div className="space-y-2">
                      <div className="text-xs font-medium text-gray-600 mb-2">
                        אפשרויות צפיה:
                        {sessionStorage.getItem('view_once_fallback') === 'true' && (
                          <span className="text-orange-500 mr-2">(מצב fallback)</span>
                        )}
                      </div>

                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="viewMode"
                          checked={!viewOnceMode && !maxViewsMode}
                          onChange={() => {
                            setViewOnceMode(false);
                            setMaxViewsMode(null);
                          }}
                          className="text-blue-500"
                        />
                        <span className="text-sm text-gray-700">רגיל - ללא הגבלה</span>
                      </label>

                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="viewMode"
                          checked={viewOnceMode}
                          onChange={() => {
                            setViewOnceMode(true);
                            setMaxViewsMode(1);
                          }}
                          className="text-blue-500"
                        />
                        <span className="text-sm text-gray-700">🔒 צפיה פעם אחת</span>
                      </label>

                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="viewMode"
                          checked={maxViewsMode === 2}
                          onChange={() => {
                            setViewOnceMode(false);
                            setMaxViewsMode(2);
                          }}
                          className="text-blue-500"
                        />
                        <span className="text-sm text-gray-700">🔒 צפיה פעמיים</span>
                      </label>
                    </div>
                  </div>
                )}

                {/* Message Input */}
                <div className="flex items-center gap-2">
                  {/* Hidden file input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                  />

                  {/* Image upload button */}
                  <button
                    type="button"
                    onClick={() => {
                      const availableBuckets = JSON.parse(sessionStorage.getItem('available_buckets') || '{}');
                      if (!availableBuckets.photos && !availableBuckets.chatImages) {
                        alert('העלאת תמונות לא זמינה כרגע. אין buckets זמינים.');
                        return;
                      }
                      fileInputRef.current?.click();
                    }}
                    disabled={!isVip || uploadingImage}
                    className={`p-2 rounded-lg ${
                      isVip && !uploadingImage
                        ? 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                    title={(() => {
                      if (!isVip) return 'שדרג ל-VIP להעלאת תמונות';
                      if (uploadingImage) return 'מעלה תמונה...';
                      const availableBuckets = JSON.parse(sessionStorage.getItem('available_buckets') || '{}');
                      if (!availableBuckets.photos && !availableBuckets.chatImages) {
                        return 'העלאת תמונות לא זמינה';
                      }
                      return 'הוסף תמונה';
                    })()}
                  >
                    {uploadingImage ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <ImageIcon className="w-5 h-5" />
                    )}
                  </button>

                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder={isVip ? "הקלד הודעה..." : "שדרג ל-VIP כדי לשלוח הודעות"}
                    disabled={!isVip || sendingMessage}
                    className="flex-1 p-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  />
                  <button
                    type="submit"
                    disabled={!isVip || (!newMessage.trim() && !selectedImage) || sendingMessage || uploadingImage}
                    className={`p-2 rounded-lg ${
                      isVip && (newMessage.trim() || selectedImage) && !sendingMessage && !uploadingImage
                        ? 'bg-pink-500 text-white hover:bg-pink-600'
                        : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {sendingMessage ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <Send className="w-5 h-5" />
                    )}
                  </button>
                </div>
                {!isVip && (
                  <p className="mt-2 text-xs text-amber-600 flex items-center gap-1">
                    <Crown className="w-3 h-3" />
                    שדרג ל-VIP כדי לשלוח הודעות
                  </p>
                )}
              </form>
            </>
          )}
        </div>
      </div>

      {/* Image Modal */}
      <AnimatePresence>
        {selectedImageModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedImageModal(null)}
          >
            <motion.div
              initial={{ scale: 0.7, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.7, opacity: 0, y: 50 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="relative max-w-full max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close button */}
              <button
                onClick={() => setSelectedImageModal(null)}
                className="absolute -top-12 right-0 bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-full p-3 hover:bg-opacity-30 transition-all duration-200 z-10 shadow-lg"
                title="סגור (ESC)"
              >
                <X className="w-6 h-6" />
              </button>

              {/* Image */}
              <img
                src={selectedImageModal}
                alt="תמונה מוגדלת"
                className="max-w-full max-h-[90vh] object-contain rounded-lg shadow-2xl cursor-pointer"
                onClick={() => setSelectedImageModal(null)}
                style={{ maxWidth: '90vw' }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default MessagesPage;
