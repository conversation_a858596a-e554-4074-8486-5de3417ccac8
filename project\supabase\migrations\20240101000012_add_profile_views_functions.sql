-- Add profile views functions
-- This migration adds the remaining functions for profile views

-- Create function to get profiles that viewed a user
CREATE OR REPLACE FUNCTION get_profiles_who_viewed_me(user_id uuid)
RETURNS TABLE (
  id uuid,
  username text,
  gender text,
  birth_date date,
  partner_birth_date date,
  city text,
  area text,
  phone text,
  height numeric,
  weight numeric,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children text,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text,
  meeting_times text,
  created_at timestamptz,
  updated_at timestamptz,
  user_metadata jsonb,
  profile_data jsonb,
  points integer,
  is_online boolean,
  view_date timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.*,
    pv.created_at as view_date
  FROM profiles p
  JOIN profile_views pv ON p.id = pv.viewer_id
  WHERE pv.viewed_id = user_id
  ORDER BY pv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get profiles that a user viewed
CREATE OR REPLACE FUNCTION get_profiles_i_viewed(user_id uuid)
RETURNS TABLE (
  id uuid,
  username text,
  gender text,
  birth_date date,
  partner_birth_date date,
  city text,
  area text,
  phone text,
  height numeric,
  weight numeric,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children text,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text,
  meeting_times text,
  created_at timestamptz,
  updated_at timestamptz,
  user_metadata jsonb,
  profile_data jsonb,
  points integer,
  is_online boolean,
  view_date timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.*,
    pv.created_at as view_date
  FROM profiles p
  JOIN profile_views pv ON p.id = pv.viewed_id
  WHERE pv.viewer_id = user_id
  ORDER BY pv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to count profiles that viewed a user
CREATE OR REPLACE FUNCTION count_profiles_who_viewed_me(user_id uuid)
RETURNS integer AS $$
DECLARE
  view_count integer;
BEGIN
  SELECT COUNT(*)
  INTO view_count
  FROM profile_views
  WHERE viewed_id = user_id;
  
  RETURN view_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to count profiles that a user viewed
CREATE OR REPLACE FUNCTION count_profiles_i_viewed(user_id uuid)
RETURNS integer AS $$
DECLARE
  view_count integer;
BEGIN
  SELECT COUNT(*)
  INTO view_count
  FROM profile_views
  WHERE viewer_id = user_id;
  
  RETURN view_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for realtime notifications
CREATE OR REPLACE FUNCTION refresh_profile_views()
RETURNS TRIGGER AS $$
BEGIN
  -- Notify clients about the change
  PERFORM pg_notify(
    'profile_views_changes',
    json_build_object(
      'table', 'profile_views',
      'type', TG_OP,
      'id', NEW.id,
      'viewer_id', NEW.viewer_id,
      'viewed_id', NEW.viewed_id,
      'created_at', NEW.created_at,
      'record', row_to_json(NEW)
    )::text
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for realtime notifications
CREATE TRIGGER on_profile_view_change
AFTER INSERT OR UPDATE ON profile_views
FOR EACH ROW
EXECUTE FUNCTION refresh_profile_views();
