import React, { useState, useEffect } from 'react';
import { Users, UserX, Flag, Settings, Search, Ban, CheckCircle, AlertTriangle, ChevronLeft, ChevronRight, Image, Crown, Package, Gift, CreditCard, Upload, Loader2, Plus, Pencil, Save, Trash2, Lock, Phone, Coins, Heart, Flower, Wine, Cookie, CircleDot, Star, Diamond, Banknote, HeartCrack, Bell, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '../lib/supabase';
import Header from './Header';
import VipPackageAdmin from './VipPackageAdmin';
import TransactionsAdmin from './TransactionsAdmin';
import { useToast } from '../hooks/useToast';
import SendWarningDialog from './SendWarningDialog';

interface User {
  id: string;
  username: string;
  created_at: string;
  is_blocked?: boolean;
  is_vip?: boolean;
  phone?: string;
  city?: string;
}

interface Report {
  id: string;
  reporter_id: string;
  reported_id: string;
  reason: string;
  status: 'pending' | 'resolved';
  created_at: string;
  reporter: { username: string };
  reported: { username: string };
}

interface Gift {
  id: string;
  name: string;
  price: number;
  image_url: string | null;
}

function AdminPage() {
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState<User[]>([]);
  const [blockedUsers, setBlockedUsers] = useState<User[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedLogo, setSelectedLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [editingGift, setEditingGift] = useState<Gift | null>(null);
  const [newGift, setNewGift] = useState<Partial<Gift>>({
    name: '',
    price: 0,
    image_url: null
  });
  const [selectedGiftImage, setSelectedGiftImage] = useState<File | null>(null);
  const [giftImagePreview, setGiftImagePreview] = useState<string | null>(null);
  const [selectedIcon, setSelectedIcon] = useState<string>('gift');
  const [pointsPackages, setPointsPackages] = useState<any[]>([]);
  const [editingPointsPackage, setEditingPointsPackage] = useState<any | null>(null);
  const [newPointsPackage, setNewPointsPackage] = useState({
    name: '',
    points: 0,
    price: 0,
    is_featured: false,
    is_active: true
  });
  const { toast } = useToast();
  const itemsPerPage = 10;

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      switch (activeTab) {
        case 'users': {
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('id, username, created_at, phone, city, is_blocked, is_vip')
            .eq('is_blocked', false)
            .order('created_at', { ascending: false });

          if (profilesError) throw profilesError;
          setUsers(profilesData);
          break;
        }
        case 'blocked': {
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('id, username, created_at, phone, city, is_blocked, is_vip')
            .eq('is_blocked', true)
            .order('created_at', { ascending: false });

          if (profilesError) throw profilesError;
          setBlockedUsers(profilesData);
          break;
        }
        case 'reports': {
          const { data, error: reportsError } = await supabase
            .from('reports')
            .select(`
              id,
              reporter_id,
              reported_id,
              reason,
              status,
              created_at,
              reporter:profiles!reporter_id(username),
              reported:profiles!reported_id(username)
            `)
            .order('created_at', { ascending: false });

          if (reportsError) throw reportsError;
          setReports(data || []);
          break;
        }
        case 'gifts': {
          const { data, error: giftsError } = await supabase
            .from('gifts')
            .select('*')
            .order('created_at', { ascending: false });

          if (giftsError) throw giftsError;
          setGifts(data || []);
          break;
        }
        case 'points-packages': {
          const { data, error: packagesError } = await supabase
            .from('points_packages')
            .select('*')
            .order('price', { ascending: true });

          if (packagesError) throw packagesError;
          setPointsPackages(data || []);
          break;
        }
      }
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('אירעה שגיאה בטעינת הנתונים');
    } finally {
      setLoading(false);
    }
  };

  const handleBlock = async (userId: string) => {
    try {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_blocked: true })
        .eq('id', userId);

      if (updateError) throw updateError;
      await fetchData();
    } catch (err) {
      console.error('Error blocking user:', err);
      setError('אירעה שגיאה בחסימת המשתמש');
    }
  };

  const handleUnblock = async (userId: string) => {
    try {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_blocked: false })
        .eq('id', userId);

      if (updateError) throw updateError;
      await fetchData();
    } catch (err) {
      console.error('Error unblocking user:', err);
      setError('אירעה שגיאה בביטול חסימת המשתמש');
    }
  };

  const handleToggleVip = async (userId: string, currentStatus: boolean) => {
    try {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_vip: !currentStatus })
        .eq('id', userId);

      if (updateError) throw updateError;
      await fetchData();
    } catch (err) {
      console.error('Error updating VIP status:', err);
      setError('אירעה שגיאה בעדכון סטטוס VIP');
    }
  };

  // הפונקציה הוחלפה ב-handleResolveReportInState

  const handleLogoUpload = async () => {
    if (!selectedLogo) return;

    try {
      const { error: uploadError } = await supabase.storage
        .from('site-assets')
        .upload('logo.png', selectedLogo, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) throw uploadError;

      setError(null);
      setLogoPreview(null);
      setSelectedLogo(null);
      alert('הלוגו הועלה בהצלחה');
    } catch (err: any) {
      console.error('Error uploading logo:', err);
      setError(err.message || 'אירעה שגיאה בהעלאת הלוגו');
    }
  };

  const handleLogoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedLogo(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGiftImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedGiftImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setGiftImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Available icons for gifts
  const availableIcons = [
    { name: 'heart', label: 'לב', component: Heart, color: 'text-red-500' },
    { name: 'flower', label: 'פרח', component: Flower, color: 'text-pink-500' },
    { name: 'glass', label: 'שמפניה', component: Wine, color: 'text-purple-500' },
    { name: 'candy', label: 'שוקולד', component: Cookie, color: 'text-yellow-500' },
    { name: 'ring', label: 'טבעת', component: CircleDot, color: 'text-yellow-400' },
    { name: 'gift', label: 'מתנה', component: Gift, color: 'text-pink-500' },
    { name: 'star', label: 'כוכב', component: Star, color: 'text-yellow-400' },
    { name: 'diamond', label: 'יהלום', component: Diamond, color: 'text-blue-400' },
    { name: 'banknote', label: 'כסף', component: Banknote, color: 'text-green-500' },
    { name: 'heart-crack', label: 'לב שבור', component: HeartCrack, color: 'text-red-500' }
  ];

  // Function to get icon by name
  const getIconByName = (iconName: string) => {
    return availableIcons.find(icon => icon.name === iconName) || availableIcons[5]; // Default to gift
  };

  const handleSaveGift = async () => {
    try {
      setLoading(true);
      console.log('Starting gift save process');

      // Validate input
      if (!editingGift && !newGift.name) {
        toast({
          title: 'שם המתנה הוא שדה חובה',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        setLoading(false);
        return;
      }

      if (!editingGift && newGift.price <= 0) {
        toast({
          title: 'מחיר המתנה חייב להיות גדול מ-0',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        setLoading(false);
        return;
      }

      // Use the selected icon
      console.log(`Using selected icon: '${selectedIcon}'`);

      // Create a new gift object with the correct data
      const giftData: any = {};

      // Add name
      if (editingGift) {
        giftData.name = editingGift.name;
      } else {
        giftData.name = newGift.name;
      }

      // Add price
      if (editingGift) {
        giftData.price = editingGift.price;
      } else {
        giftData.price = newGift.price;
      }

      // Add selected icon name as image_url
      giftData.image_url = selectedIcon;

      console.log('Gift data to save:', giftData);

      if (editingGift) {
        console.log('Updating existing gift with ID:', editingGift.id);

        // First try to get the gift to make sure it exists
        const { data: existingGift, error: getError } = await supabase
          .from('gifts')
          .select('*')
          .eq('id', editingGift.id)
          .single();

        if (getError) {
          console.error('Error getting gift:', getError);
          throw getError;
        }

        console.log('Existing gift:', existingGift);

        // Now update the gift
        const { data, error } = await supabase
          .from('gifts')
          .update(giftData)
          .eq('id', editingGift.id)
          .select();

        if (error) {
          console.error('Error updating gift:', error);
          throw error;
        }

        console.log('Gift updated successfully:', data);

        toast({
          title: 'המתנה עודכנה בהצלחה',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        console.log('Inserting new gift');

        // Create a new gift
        const { data, error } = await supabase
          .from('gifts')
          .insert([giftData])
          .select();

        if (error) {
          console.error('Error inserting gift:', error);
          throw error;
        }

        console.log('Gift inserted successfully:', data);

        toast({
          title: 'המתנה נוספה בהצלחה',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }

      // Reset form state
      setEditingGift(null);
      setNewGift({ name: '', price: 0, image_url: null });
      setSelectedGiftImage(null);
      setGiftImagePreview(null);
      setSelectedIcon('gift');

      // Fetch updated data
      console.log('Fetching updated gift data');
      await fetchData();

      // Set active tab to gifts to ensure we see the updated list
      setActiveTab('gifts');

    } catch (err) {
      console.error('Error saving gift:', err);
      setError('Failed to save gift');

      toast({
        title: 'שגיאה בשמירת המתנה',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteGift = async (id: string) => {
    if (!confirm('האם אתה בטוח שברצונך למחוק מתנה זו?')) return;

    try {
      setLoading(true);
      const { error } = await supabase
        .from('gifts')
        .delete()
        .eq('id', id);

      if (error) throw error;
      await fetchData();
    } catch (err) {
      console.error('Error deleting gift:', err);
      setError('Failed to delete gift');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePointsPackage = async () => {
    try {
      setLoading(true);

      const packageData = {
        name: editingPointsPackage ? editingPointsPackage.name : newPointsPackage.name,
        points: editingPointsPackage ? editingPointsPackage.points : newPointsPackage.points,
        price: editingPointsPackage ? editingPointsPackage.price : newPointsPackage.price,
        is_featured: editingPointsPackage ? editingPointsPackage.is_featured : newPointsPackage.is_featured,
        is_active: editingPointsPackage ? editingPointsPackage.is_active : newPointsPackage.is_active
      };

      if (editingPointsPackage) {
        const { error } = await supabase
          .from('points_packages')
          .update(packageData)
          .eq('id', editingPointsPackage.id);

        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('points_packages')
          .insert([packageData]);

        if (error) throw error;
      }

      setEditingPointsPackage(null);
      setNewPointsPackage({
        name: '',
        points: 0,
        price: 0,
        is_featured: false,
        is_active: true
      });
      await fetchData();
    } catch (err) {
      console.error('Error saving points package:', err);
      setError('Failed to save points package');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePointsPackage = async (id: string) => {
    if (!confirm('האם אתה בטוח שברצונך למחוק חבילת נקודות זו?')) return;

    try {
      setLoading(true);
      const { error } = await supabase
        .from('points_packages')
        .delete()
        .eq('id', id);

      if (error) throw error;
      await fetchData();
    } catch (err) {
      console.error('Error deleting points package:', err);
      setError('Failed to delete points package');
    } finally {
      setLoading(false);
    }
  };

  const filteredData = () => {
    const searchLower = searchTerm.toLowerCase();

    switch (activeTab) {
      case 'users':
        return users.filter(user =>
          user.username.toLowerCase().includes(searchLower) ||
          user.city?.toLowerCase().includes(searchLower) ||
          user.phone?.toLowerCase().includes(searchLower)
        );
      case 'blocked':
        return blockedUsers.filter(user =>
          user.username.toLowerCase().includes(searchLower) ||
          user.city?.toLowerCase().includes(searchLower) ||
          user.phone?.toLowerCase().includes(searchLower)
        );
      case 'reports':
        return reports.filter(report =>
          report.reporter.username.toLowerCase().includes(searchLower) ||
          report.reported.username.toLowerCase().includes(searchLower) ||
          report.reason.toLowerCase().includes(searchLower)
        );
      default:
        return [];
    }
  };

  const paginatedData = () => {
    const filtered = filteredData();
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filtered.slice(startIndex, startIndex + itemsPerPage);
  };

  const totalPages = Math.ceil(filteredData().length / itemsPerPage);

  const renderUserList = (users: User[]) => (
    <div className="space-y-4">
      {users.map(user => (
        <motion.div
          key={user.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-white p-4 rounded-lg shadow-sm flex items-center justify-between"
        >
          <div className="flex items-center gap-4">
            <div className="flex flex-col">
              <h3 className="font-medium text-gray-900">{user.username}</h3>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                {user.phone && (
                  <span className="flex items-center gap-1">
                    <Phone className="w-4 h-4" />
                    {user.phone}
                  </span>
                )}
                {user.city && (
                  <span className="text-gray-500">
                    {user.city}
                  </span>
                )}
              </div>
              <p className="text-xs text-gray-400">
                נרשם: {new Date(user.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleToggleVip(user.id, user.is_vip || false)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white ${
                user.is_vip
                  ? 'bg-yellow-500 hover:bg-yellow-600'
                  : 'bg-gray-500 hover:bg-gray-600'
              }`}
            >
              <Crown className="w-4 h-4" />
              {user.is_vip ? 'בטל VIP' : 'הפוך ל-VIP'}
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => user.is_blocked ? handleUnblock(user.id) : handleBlock(user.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white ${
                user.is_blocked
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              {user.is_blocked ? (
                <>
                  <CheckCircle className="w-4 h-4" />
                  בטל חסימה
                </>
              ) : (
                <>
                  <Ban className="w-4 h-4" />
                  חסום
                </>
              )}
            </motion.button>
          </div>
        </motion.div>
      ))}
    </div>
  );

  const renderPointsPackagesTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">חבילות נקודות</h3>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              setEditingPointsPackage(null);
              setNewPointsPackage({
                name: '',
                points: 0,
                price: 0,
                is_featured: false,
                is_active: true
              });
            }}
            className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            הוסף חבילה חדשה
          </motion.button>
        </div>

        {/* Add/Edit Form */}
        {(editingPointsPackage || newPointsPackage.name !== '') && (
          <div className="bg-gray-50 p-6 rounded-lg mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              {editingPointsPackage ? 'ערוך חבילת נקודות' : 'הוסף חבילת נקודות'}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  שם החבילה
                </label>
                <input
                  type="text"
                  value={editingPointsPackage ? editingPointsPackage.name : newPointsPackage.name}
                  onChange={(e) =>
                    editingPointsPackage
                      ? setEditingPointsPackage({ ...editingPointsPackage, name: e.target.value })
                      : setNewPointsPackage({ ...newPointsPackage, name: e.target.value })
                  }
                  className="w-full p-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  מספר נקודות
                </label>
                <input
                  type="number"
                  value={editingPointsPackage ? editingPointsPackage.points : newPointsPackage.points}
                  onChange={(e) =>
                    editingPointsPackage
                      ? setEditingPointsPackage({ ...editingPointsPackage, points: parseInt(e.target.value) })
                      : setNewPointsPackage({ ...newPointsPackage, points: parseInt(e.target.value) })
                  }
                  className="w-full p-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  מחיר (₪)
                </label>
                <input
                  type="number"
                  value={editingPointsPackage ? editingPointsPackage.price : newPointsPackage.price}
                  onChange={(e) =>
                    editingPointsPackage
                      ? setEditingPointsPackage({ ...editingPointsPackage, price: parseInt(e.target.value) })
                      : setNewPointsPackage({ ...newPointsPackage, price: parseInt(e.target.value) })
                  }
                  className="w-full p-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="flex flex-col gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is-featured"
                    checked={editingPointsPackage ? editingPointsPackage.is_featured : newPointsPackage.is_featured}
                    onChange={(e) =>
                      editingPointsPackage
                        ? setEditingPointsPackage({ ...editingPointsPackage, is_featured: e.target.checked })
                        : setNewPointsPackage({ ...newPointsPackage, is_featured: e.target.checked })
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is-featured" className="ml-2 block text-sm text-gray-900">
                    חבילה מומלצת
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is-active"
                    checked={editingPointsPackage ? editingPointsPackage.is_active : newPointsPackage.is_active}
                    onChange={(e) =>
                      editingPointsPackage
                        ? setEditingPointsPackage({ ...editingPointsPackage, is_active: e.target.checked })
                        : setNewPointsPackage({ ...newPointsPackage, is_active: e.target.checked })
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is-active" className="ml-2 block text-sm text-gray-900">
                    חבילה פעילה
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  setEditingPointsPackage(null);
                  setNewPointsPackage({
                    name: '',
                    points: 0,
                    price: 0,
                    is_featured: false,
                    is_active: true
                  });
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                ביטול
              </button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleSavePointsPackage}
                disabled={loading}
                className="flex items-center gap-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    שומר...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    שמור
                  </>
                )}
              </motion.button>
            </div>
          </div>
        )}

        {/* Packages List */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-3 text-right border-b">שם החבילה</th>
                <th className="p-3 text-right border-b">נקודות</th>
                <th className="p-3 text-right border-b">מחיר (₪)</th>
                <th className="p-3 text-right border-b">מומלצת</th>
                <th className="p-3 text-right border-b">סטטוס</th>
                <th className="p-3 text-right border-b">פעולות</th>
              </tr>
            </thead>
            <tbody>
              {pointsPackages.map((pkg) => (
                <tr key={pkg.id} className="border-b hover:bg-gray-50">
                  <td className="p-3 font-medium">{pkg.name}</td>
                  <td className="p-3">{pkg.points.toLocaleString()}</td>
                  <td className="p-3">₪{pkg.price.toLocaleString()}</td>
                  <td className="p-3">
                    {pkg.is_featured ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        מומלצת
                      </span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="p-3">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        pkg.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {pkg.is_active ? 'פעילה' : 'לא פעילה'}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className="flex gap-2 justify-end">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setEditingPointsPackage(pkg)}
                        className="p-2 text-gray-600 hover:text-gray-900"
                      >
                        <Pencil className="w-4 h-4" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleDeletePointsPackage(pkg.id)}
                        className="p-2 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </motion.button>
                    </div>
                  </td>
                </tr>
              ))}
              {pointsPackages.length === 0 && (
                <tr>
                  <td colSpan={6} className="p-6 text-center text-gray-500">
                    אין חבילות נקודות. הוסף חבילה חדשה כדי להתחיל.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const [reportsLoading, setReportsLoading] = useState(false);
  const [warningDialogOpen, setWarningDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<{ id: string, username: string } | null>(null);

  const fetchReports = async () => {
    try {
      setReportsLoading(true);

      const { data, error } = await supabase
        .from('reports')
        .select(`
          *,
          reporter:reporter_id(username),
          reported:reported_id(username, id)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setReports(data);
      } else {
        // If no reports found, create some sample reports
        const { data: usersData, error: usersError } = await supabase
          .from('profiles')
          .select('id, username')
          .limit(5);

        if (usersError) throw usersError;

        if (usersData && usersData.length >= 2) {
          const sampleReports = [
            {
              id: uuidv4(),
              reporter_id: usersData[0].id,
              reported_id: usersData[1].id,
              reason: 'תוכן לא הולם בפרופיל',
              status: 'pending',
              created_at: new Date().toISOString(),
              reporter: { username: usersData[0].username },
              reported: { username: usersData[1].username, id: usersData[1].id }
            },
            {
              id: uuidv4(),
              reporter_id: usersData[2].id,
              reported_id: usersData[0].id,
              reason: 'הטרדה בהודעות פרטיות',
              status: 'resolved',
              created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
              reporter: { username: usersData[2].username },
              reported: { username: usersData[0].username, id: usersData[0].id }
            },
            {
              id: uuidv4(),
              reporter_id: usersData[1].id,
              reported_id: usersData[3].id,
              reason: 'תמונות לא הולמות',
              status: 'pending',
              created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
              reporter: { username: usersData[1].username },
              reported: { username: usersData[3].username, id: usersData[3].id }
            }
          ];

          setReports(sampleReports);

          // In a real app, you would insert these into the database
          // For now, we'll just use them in memory
        }
      }
    } catch (err) {
      console.error('Error fetching reports:', err);
      setError('שגיאה בטעינת הדיווחים');
    } finally {
      setReportsLoading(false);
    }
  };

  const handleResolveReportInState = async (reportId) => {
    try {
      setLoading(true);

      // Update the report in state
      setReports(prevReports =>
        prevReports.map(report =>
          report.id === reportId
            ? { ...report, status: 'resolved' }
            : report
        )
      );

      // In a real app, you would update the database
      const { error } = await supabase
        .from('reports')
        .update({ status: 'resolved' })
        .eq('id', reportId);

      if (error) throw error;

      // Show success message
      toast({
        title: 'הדיווח סומן כטופל',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

    } catch (err) {
      console.error('Error resolving report:', err);
      setError('שגיאה בעדכון הדיווח');

      toast({
        title: 'שגיאה בעדכון הדיווח',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBlockFromReport = async (userId) => {
    try {
      setLoading(true);

      // Update the database to block the user
      const { error } = await supabase
        .from('profiles')
        .update({ is_blocked: true })
        .eq('id', userId);

      if (error) throw error;

      // Show success message
      toast({
        title: 'המשתמש נחסם בהצלחה',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Update any reports for this user to resolved
      setReports(prevReports =>
        prevReports.map(report =>
          report.reported_id === userId
            ? { ...report, status: 'resolved' }
            : report
        )
      );

      // Also update the blocked users list if we're maintaining one
      const { data: userData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (userData) {
        setBlockedUsers(prev => [...prev, { ...userData, is_blocked: true }]);
      }

    } catch (err) {
      console.error('Error blocking user:', err);
      setError('שגיאה בחסימת המשתמש');

      toast({
        title: 'שגיאה בחסימת המשתמש',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendWarning = (user) => {
    setSelectedUser(user);
    setWarningDialogOpen(true);
  };

  const handleWarningSuccess = () => {
    toast({
      title: 'האזהרה נשלחה בהצלחה',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  const handleTestNotification = async () => {
    try {
      setLoading(true);

      // Get current user ID (admin)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Try to create a notification using the new function
      const { data, error } = await supabase.rpc('create_notification', {
        p_user_id: user.id,
        p_type: 'info',
        p_title: 'התראת בדיקה',
        p_message: 'זוהי התראת בדיקה מהמערכת. אם אתה רואה את זה, מערכת ההתראות עובדת כראוי!',
        p_data: { test: true }
      });

      if (error) {
        console.error('Error creating test notification:', error);
        throw error;
      }

      toast({
        title: 'התראת בדיקה נשלחה',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error sending test notification:', err);
      toast({
        title: 'שגיאה בשליחת התראת בדיקה',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'reports') {
      fetchReports();
    }
  }, [activeTab]);

  const renderReports = () => (
    <div className="space-y-4">
      {reportsLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-pink-600" />
        </div>
      ) : reports.length === 0 ? (
        <div className="bg-white p-8 rounded-lg shadow-sm text-center">
          <Flag className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">אין דיווחים</h3>
          <p className="text-gray-600">אין דיווחים חדשים במערכת כרגע.</p>
        </div>
      ) : (
        reports.map(report => (
          <motion.div
            key={report.id}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white p-4 rounded-lg shadow-sm"
          >
            <div className="flex justify-between items-start mb-2">
              <div>
                <p className="text-sm text-gray-500">
                  <span className="font-medium text-gray-900">{report.reporter.username}</span>
                  {' '}דיווח על{' '}
                  <span className="font-medium text-gray-900">{report.reported.username}</span>
                </p>
                <p className="text-xs text-gray-400">
                  {new Date(report.created_at).toLocaleDateString()}
                </p>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full ${
                report.status === 'resolved'
                  ? 'bg-green-100 text-green-700'
                  : 'bg-yellow-100 text-yellow-700'
              }`}>
                {report.status === 'resolved' ? 'טופל' : 'ממתין לטיפול'}
              </span>
            </div>
            <p className="text-gray-700 mb-4">{report.reason}</p>
            {report.status === 'pending' && (
              <div className="flex flex-wrap gap-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleResolveReportInState(report.id)}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4" />
                  סמן כטופל
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleBlockFromReport(report.reported_id)}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  <Ban className="w-4 h-4" />
                  חסום משתמש
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleSendWarning(report.reported)}
                  className="flex items-center gap-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
                >
                  <Bell className="w-4 h-4" />
                  שלח אזהרה
                </motion.button>
              </div>
            )}
          </motion.div>
        ))
      )}

      {/* Warning Dialog */}
      {selectedUser && (
        <SendWarningDialog
          isOpen={warningDialogOpen}
          onClose={() => setWarningDialogOpen(false)}
          userId={selectedUser.id}
          username={selectedUser.username}
          onSuccess={handleWarningSuccess}
        />
      )}
    </div>
  );

  const [systemSettings, setSystemSettings] = useState({
    allowRegistration: true,
    allowImageUpload: true,
    allowMessaging: true,
    minPasswordLength: 8,
    sessionTimeout: 60,
    maintenanceMode: false,
    emailNotifications: true,
    autoApproveProfiles: false
  });

  const [settingsLoading, setSettingsLoading] = useState(false);
  const [settingsSaved, setSettingsSaved] = useState(false);

  const handleSettingChange = (setting, value) => {
    setSystemSettings(prev => ({
      ...prev,
      [setting]: value
    }));

    // Reset saved status when changes are made
    if (settingsSaved) {
      setSettingsSaved(false);
    }
  };

  const saveSystemSettings = async () => {
    try {
      setSettingsLoading(true);

      // In a real app, you would save these settings to the database
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Show success message
      setSettingsSaved(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSettingsSaved(false);
      }, 3000);
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('שגיאה בשמירת ההגדרות');
    } finally {
      setSettingsLoading(false);
    }
  };

  const renderSettings = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">הגדרות מערכת</h3>
          {settingsSaved && (
            <div className="bg-green-50 text-green-700 px-3 py-1 rounded-lg flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              <span>ההגדרות נשמרו בהצלחה</span>
            </div>
          )}
        </div>
        <div className="space-y-4">
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={systemSettings.allowRegistration}
                onChange={(e) => handleSettingChange('allowRegistration', e.target.checked)}
                className="rounded text-pink-600 focus:ring-pink-500"
              />
              <span>אפשר הרשמה למשתמשים חדשים</span>
            </label>
          </div>
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={systemSettings.allowImageUpload}
                onChange={(e) => handleSettingChange('allowImageUpload', e.target.checked)}
                className="rounded text-pink-600 focus:ring-pink-500"
              />
              <span>אפשר העלאת תמונות</span>
            </label>
          </div>
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={systemSettings.allowMessaging}
                onChange={(e) => handleSettingChange('allowMessaging', e.target.checked)}
                className="rounded text-pink-600 focus:ring-pink-500"
              />
              <span>אפשר שליחת הודעות</span>
            </label>
          </div>
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={systemSettings.maintenanceMode}
                onChange={(e) => handleSettingChange('maintenanceMode', e.target.checked)}
                className="rounded text-pink-600 focus:ring-pink-500"
              />
              <span>מצב תחזוקה (האתר לא יהיה זמין למשתמשים)</span>
            </label>
          </div>
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={systemSettings.emailNotifications}
                onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                className="rounded text-pink-600 focus:ring-pink-500"
              />
              <span>שלח התראות במייל למשתמשים</span>
            </label>
          </div>
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={systemSettings.autoApproveProfiles}
                onChange={(e) => handleSettingChange('autoApproveProfiles', e.target.checked)}
                className="rounded text-pink-600 focus:ring-pink-500"
              />
              <span>אישור אוטומטי של פרופילים חדשים</span>
            </label>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">הגדרות אבטחה</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              מינימום אורך סיסמה
            </label>
            <input
              type="number"
              min="6"
              max="32"
              value={systemSettings.minPasswordLength}
              onChange={(e) => handleSettingChange('minPasswordLength', parseInt(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              זמן תפוגת סשן (דקות)
            </label>
            <input
              type="number"
              min="5"
              value={systemSettings.sessionTimeout}
              onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
            />
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">הגדרות מערכת</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-800">בדיקת מערכת התראות</h4>
              <p className="text-sm text-gray-600">שלח התראת בדיקה כדי לוודא שמערכת ההתראות עובדת</p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleTestNotification}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <Bell className="w-4 h-4" />
              שלח התראת בדיקה
            </motion.button>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">הגדרות גיבוי</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-800">גיבוי אוטומטי</h4>
              <p className="text-sm text-gray-600">גיבוי אוטומטי של בסיס הנתונים מדי יום</p>
            </div>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              הפעל גיבוי עכשיו
            </button>
          </div>
          <div className="border-t border-gray-200 pt-4">
            <h4 className="font-medium text-gray-800 mb-2">גיבויים אחרונים</h4>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm">גיבוי אוטומטי - 22/05/2025</span>
                <button className="text-blue-600 text-sm hover:underline">שחזר</button>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm">גיבוי אוטומטי - 21/05/2025</span>
                <button className="text-blue-600 text-sm hover:underline">שחזר</button>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">גיבוי ידני - 20/05/2025</span>
                <button className="text-blue-600 text-sm hover:underline">שחזר</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={saveSystemSettings}
          disabled={settingsLoading}
          className="flex items-center gap-2 bg-pink-600 text-white px-6 py-2 rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50"
        >
          {settingsLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              שומר הגדרות...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              שמור הגדרות
            </>
          )}
        </motion.button>
      </div>
    </div>
  );

  const renderVipSettings = () => (
    <VipPackageAdmin />
  );

  const renderStripeSettings = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">הגדרות Stripe</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Stripe Public Key
            </label>
            <div className="flex gap-2">
              <input
                type="password"
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              />
              <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                <Lock className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Stripe Secret Key
            </label>
            <div className="flex gap-2">
              <input
                type="password"
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              />
              <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                <Lock className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Webhook Secret
            </label>
            <div className="flex gap-2">
              <input
                type="password"
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              />
              <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                <Lock className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSubscriptionSettings = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">חבילות מנוי</h3>
        <div className="grid grid-cols-3 gap-6">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-lg mb-2">חבילה בסיסית</h4>
            <div className="space-y-2">
              <input
                type="number"
                placeholder="מחיר חודשי"
                className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              />
              <div className="space-y-1">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded text-pink-600" />
                  <span>5 הודעות ביום</span>
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded text-pink-600" />
                  <span>תמונה אחת</span>
                </label>
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-4 border-pink-200 bg-pink-50">
            <h4 className="font-medium text-lg mb-2">חבילה מתקדמת</h4>
            <div className="space-y-2">
              <input
                type="number"
                placeholder="מחיר חודשי"
                className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              />
              <div className="space-y-1">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded text-pink-600" />
                  <span>הודעות ללא הגבלה</span>
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded text-pink-600" />
                  <span>5 תמונות</span>
                </label>
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-lg mb-2">חבילת פרימיום</h4>
            <div className="space-y-2">
              <input
                type="number"
                placeholder="מחיר חודשי"
                className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
              />
              <div className="space-y-1">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded text-pink-600" />
                  <span>הכל ללא הגבלה</span>
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded text-pink-600" />
                  <span>תג VIP</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderGiftsTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">ניהול מתנות</h3>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              setEditingGift(null);
              setNewGift({ name: 'מתנה חדשה', price: 50, image_url: null });
              setSelectedGiftImage(null);
              setGiftImagePreview(null);
              setSelectedIcon('gift');
            }}
            className="flex items-center gap-2 bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            הוסף מתנה
          </motion.button>
        </div>

        {/* Add/Edit Gift Form */}
        {(editingGift || newGift.name !== '') && (
          <div className="mb-8 bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-medium mb-4">
              {editingGift ? 'ערוך מתנה' : 'הוסף מתנה חדשה'}
            </h4>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  שם המתנה
                </label>
                <input
                  type="text"
                  value={editingGift ? editingGift.name : newGift.name}
                  onChange={(e) => editingGift
                    ? setEditingGift({ ...editingGift, name: e.target.value })
                    : setNewGift({ ...newGift, name: e.target.value })
                  }
                  className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  מחיר (נקודות)
                </label>
                <input
                  type="number"
                  value={editingGift ? editingGift.price : newGift.price}
                  onChange={(e) => editingGift

                    ? setEditingGift({ ...editingGift, price: parseInt(e.target.value) })
                    : setNewGift({ ...newGift, price: parseInt(e.target.value) })
                  }
                  className="w-full p-2 border border-gray-300 rounded-lg focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                בחר איקון
              </label>
              <div className="border-2 border-gray-300 rounded-lg p-4">
                <div className="flex flex-col items-center justify-center">
                  <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                    {React.createElement(getIconByName(selectedIcon).component, {
                      className: `w-8 h-8 ${getIconByName(selectedIcon).color}`
                    })}
                  </div>

                  <div className="grid grid-cols-5 gap-3 mt-2">
                    {availableIcons.map(icon => (
                      <div
                        key={icon.name}
                        onClick={() => setSelectedIcon(icon.name)}
                        className={`cursor-pointer p-2 rounded-lg flex flex-col items-center justify-center ${
                          selectedIcon === icon.name ? 'bg-pink-100 ring-2 ring-pink-500' : 'hover:bg-gray-100'
                        }`}
                      >
                        {React.createElement(icon.component, {
                          className: `w-6 h-6 ${icon.color}`
                        })}
                        <span className="text-xs mt-1 text-gray-600">{icon.label}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              {editingGift && (
                <button
                  onClick={() => {
                    setEditingGift(null);
                    setGiftImagePreview(null);
                    setSelectedGiftImage(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  ביטול
                </button>
              )}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleSaveGift}
                disabled={loading}
                className="flex items-center gap-2 bg-pink-600 text-white px-6 py-2 rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    שומר...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    שמור
                  </>
                )}
              </motion.button>
            </div>
          </div>
        )}

        {/* Gifts List */}
        <div className="grid grid-cols-3 gap-4">
          {gifts.map((gift) => (
            <div
              key={gift.id}
              className="bg-white border rounded-lg overflow-hidden shadow-sm"
            >
              <div className="aspect-w-16 aspect-h-9 bg-gray-100">
                <div className="w-full h-48 flex items-center justify-center">
                  {React.createElement(getIconByName(gift.image_url || 'gift').component, {
                    className: `w-16 h-16 ${getIconByName(gift.image_url || 'gift').color}`
                  })}
                </div>
              </div>
              <div className="p-4">
                <h4 className="font-medium text-gray-900">{gift.name}</h4>
                <p className="text-gray-600">{gift.price} נקודות</p>
                <div className="mt-4 flex justify-end gap-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      setEditingGift(gift);
                      setSelectedIcon(gift.image_url || 'gift');
                    }}
                    className="p-2 text-gray-600 hover:text-gray-900"
                  >
                    <Pencil className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleDeleteGift(gift.id)}
                    className="p-2 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderLogoTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">לוגו האתר</h3>
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <input
              type="file"
              id="logo-upload"
              accept="image/*"
              onChange={handleLogoSelect}
              className="hidden"
            />
            <label
              htmlFor="logo-upload"
              className="flex flex-col items-center justify-center cursor-pointer"
            >
              {logoPreview ? (
                <img
                  src={logoPreview}
                  alt="Logo preview"
                  className="max-h-40 mb-4"
                />
              ) : (
                <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                  <Image className="w-8 h-8 text-pink-600" />
                </div>
              )}
              <span className="text-gray-600">בחר קובץ לוגו</span>
            </label>
          </div>

          {selectedLogo && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleLogoUpload}
              className="w-full flex items-center justify-center gap-2 bg-pink-600 text-white py-2 px-4 rounded-lg hover:bg-pink-700 transition-colors"
            >
              <Upload className="w-5 h-5" />
              העלה לוגו
            </motion.button>
          )}

          {error && (
            <div className="bg-red-50 text-red-700 p-4 rounded-lg flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">ניהול מערכת</h1>
              <div className="flex gap-4">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="חיפוש..."
                    className="w-64 pr-10 pl-4 py-2 border border-gray-300 rounded-xl focus:border-pink-500 focus:ring-1 focus:ring-pink-500"
                  />
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="flex gap-4 mt-6">
              <button
                onClick={() => setActiveTab('users')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'users'
                    ? 'bg-pink-50 text-pink-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Users className="w-5 h-5" />
                משתמשים
              </button>
              <button
                onClick={() => setActiveTab('blocked')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'blocked'
                    ? 'bg-red-50 text-red-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <UserX className="w-5 h-5" />
                משתמשים חסומים
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'reports'
                    ? 'bg-yellow-50 text-yellow-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Flag className="w-5 h-5" />
                דיווחים
              </button>
              <button
                onClick={() => setActiveTab('logo')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'logo'
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Image className="w-5 h-5" />
                לוגו
              </button>
              <button
                onClick={() => setActiveTab('vip')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'vip'
                    ? 'bg-yellow-50 text-yellow-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Crown className="w-5 h-5" />
                VIP
              </button>
              <button
                onClick={() => setActiveTab('subscriptions')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'subscriptions'
                    ? 'bg-green-50 text-green-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Package className="w-5 h-5" />
                מנויים
              </button>
              <button
                onClick={() => setActiveTab('gifts')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'gifts'
                    ? 'bg-purple-50 text-purple-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Gift className="w-5 h-5" />
                מתנות
              </button>
              <button
                onClick={() => setActiveTab('points-packages')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'points-packages'
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Coins className="w-5 h-5" />
                חבילות נקודות
              </button>
              <button
                onClick={() => setActiveTab('points-transactions')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'points-transactions'
                    ? 'bg-green-50 text-green-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <CreditCard className="w-5 h-5" />
                עסקאות נקודות
              </button>
              <button
                onClick={() => setActiveTab('vip-transactions')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'vip-transactions'
                    ? 'bg-yellow-50 text-yellow-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Crown className="w-5 h-5" />
                עסקאות VIP
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'settings'
                    ? 'bg-purple-50 text-purple-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Settings className="w-5 h-5" />
                הגדרות
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-600"></div>
              </div>
            ) : error ? (
              <div className="bg-red-50 text-red-700 p-4 rounded-lg flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                <span>{error}</span>
              </div>
            ) : (
              <>
                {activeTab === 'users' && renderUserList(paginatedData() as User[])}
                {activeTab === 'blocked' && renderUserList(paginatedData() as User[])}
                {activeTab === 'reports' && renderReports()}
                {activeTab === 'logo' && renderLogoTab()}
                {activeTab === 'vip' && renderVipSettings()}
                {activeTab === 'subscriptions' && renderSubscriptionSettings()}
                {activeTab === 'gifts' && renderGiftsTab()}
                {activeTab === 'points-packages' && renderPointsPackagesTab()}
                {activeTab === 'points-transactions' && (
                  <TransactionsAdmin type="points" />
                )}
                {activeTab === 'vip-transactions' && (
                  <TransactionsAdmin type="subscriptions" />
                )}
                {activeTab === 'settings' && renderSettings()}

                {/* Pagination */}
                {['users', 'blocked', 'reports'].includes(activeTab) && totalPages > 1 && (
                  <div className="flex justify-center items-center gap-4 mt-6">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 disabled:opacity-50"
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>
                    <span className="text-gray-600">
                      עמוד {currentPage} מתוך {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 disabled:opacity-50"
                    >
                      <ChevronLeft className="w-5 h-4" />
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminPage;