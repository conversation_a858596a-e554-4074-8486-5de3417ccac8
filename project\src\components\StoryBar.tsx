import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, ChevronRight, ChevronLeft, Camera, Loader2 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useStories } from '../hooks/useStories';

function StoryBar() {
  const { user } = useAuth();
  const { stories, loading, error, uploadStory } = useStories();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [viewingStory, setViewingStory] = useState<{
    images: { url: string }[];
    currentImageIndex: number;
    startTime?: number;
  } | null>(null);
  const storyDuration = 5000; // 5 seconds per story
  const storyTimerRef = useRef<number>();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const newFiles: File[] = [];
    Array.from(files).forEach(file => {
      if (!file.type.startsWith('image/')) return;
      if (file.size > 5 * 1024 * 1024) return; // 5MB limit
      newFiles.push(file);
    });

    setSelectedFiles(newFiles);
  };

  const handleUpload = async () => {
    if (!selectedFiles.length) return;
    setUploading(true);

    try {
      await Promise.all(selectedFiles.map(file => uploadStory(file)));
      setSelectedFiles([]);
    } catch (error) {
      console.error('Error uploading stories:', error);
    } finally {
      setUploading(false);
    }
  };

  const nextStory = () => {
    if (currentIndex < stories.length - 4) {
      setCurrentIndex(prev => prev + 1);
      scrollToCurrentIndex();
    }
  };

  const prevStory = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      scrollToCurrentIndex();
    }
  };

  const scrollToCurrentIndex = () => {
    if (scrollContainerRef.current) {
      const cardWidth = 128; // w-32 = 8rem = 128px
      const gap = 16; // gap-4 = 1rem = 16px
      const scrollPosition = (cardWidth + gap) * currentIndex;
      scrollContainerRef.current.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  };

  const startStoryTimer = () => {
    if (!viewingStory) return;
    
    clearStoryTimer();
    
    viewingStory.startTime = Date.now();
    storyTimerRef.current = window.setTimeout(() => {
      if (viewingStory.currentImageIndex < viewingStory.images.length - 1) {
        setViewingStory({
          ...viewingStory,
          currentImageIndex: viewingStory.currentImageIndex + 1,
          startTime: Date.now()
        });
      } else {
        setViewingStory(null);
      }
    }, storyDuration);
  };

  const clearStoryTimer = () => {
    if (storyTimerRef.current) {
      clearTimeout(storyTimerRef.current);
    }
  };

  const handleStoryClick = (story: any) => {
    setViewingStory({
      images: story.images,
      currentImageIndex: 0,
      startTime: Date.now()
    });
  };

  useEffect(() => {
    if (viewingStory) {
      startStoryTimer();
    }
    return clearStoryTimer;
  }, [viewingStory?.currentImageIndex]);

  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow-sm p-4 md:p-6 mb-4 md:mb-8">
        <div className="animate-pulse flex space-x-4">
          <div className="h-32 md:h-48 w-24 md:w-32 bg-gray-200 rounded-xl"></div>
          <div className="h-32 md:h-48 w-24 md:w-32 bg-gray-200 rounded-xl"></div>
          <div className="h-32 md:h-48 w-24 md:w-32 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-2xl shadow-sm p-4 md:p-6 mb-4 md:mb-8">
        <div className="text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm p-4 md:p-6 mb-4 md:mb-8">
      <div className="relative">
        <div 
          ref={scrollContainerRef}
          className="flex gap-4 overflow-x-auto pb-2 scrollbar-hide snap-x snap-mandatory"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {/* Add Story Card */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="relative flex-shrink-0 w-24 md:w-32 h-32 md:h-48 rounded-xl overflow-hidden cursor-pointer group snap-start"
          >
            <div className="absolute inset-0 bg-gradient-to-b from-black-400 to-black-600 group-hover:opacity-90 transition-opacity" />
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
              id="story-upload"
            />
            <label
              htmlFor="story-upload"
              className="absolute inset-0 flex flex-col items-center justify-center text-black"
            >
              <div className="w-8 md:w-10 h-8 md:h-10 rounded-full bg-black/20 flex items-center justify-center mb-2 md:mb-3">
                <Plus className="w-5 md:w-6 h-5 md:h-6" />
              </div>
              <span className="text-xs md:text-sm font-medium">צור סטורי</span>
            </label>
          </motion.div>

          {/* Story Cards */}
          {stories.map((story) => (
            <motion.div
              key={story.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleStoryClick(story)}
              className="relative flex-shrink-0 w-24 md:w-32 h-32 md:h-48 rounded-xl overflow-hidden cursor-pointer group snap-start"
            >
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/60" />
              <img
                src={story.images[0].url}
                alt="סטורי"
                className="w-full h-full object-cover"
              />
              {story.images.length > 1 && (
                <div className="absolute top-2 left-2 right-2 flex justify-center gap-1">
                  {story.images.map((_, index) => (
                    <div
                      key={index}
                      className="h-1 flex-1 rounded-full bg-white/50"
                    />
                  ))}
                </div>
              )}
              <div className="absolute inset-x-0 bottom-0 p-2 md:p-4">
                <span className="text-white text-xs md:text-sm font-medium line-clamp-1">
                  {story.username}
                </span>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Navigation Arrows - Desktop Only */}
        {stories.length > 4 && (
          <>
            {currentIndex > 0 && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={prevStory}
                className="hidden md:block absolute left-2 top-1/2 -translate-y-1/2 bg-white/90 rounded-full p-2 shadow-lg hover:bg-white transition-colors"
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </motion.button>
            )}
            {currentIndex < stories.length - 4 && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={nextStory}
                className="hidden md:block absolute right-2 top-1/2 -translate-y-1/2 bg-white/90 rounded-full p-2 shadow-lg hover:bg-white transition-colors"
              >
                <ChevronRight className="w-5 h-5 text-gray-600" />
              </motion.button>
            )}
          </>
        )}
      </div>

      {/* Upload Selected Files */}
      <AnimatePresence>
        {selectedFiles.length > 0 && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="mt-4"
          >
            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-4">
                <div className="flex -space-x-2">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="w-8 md:w-10 h-8 md:h-10 rounded-lg border-2 border-white overflow-hidden"
                    >
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`תמונה ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                <span className="text-xs md:text-sm text-gray-600">
                  {selectedFiles.length} תמונות נבחרו
                </span>
                <button
                  onClick={() => setSelectedFiles([])}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleUpload}
                disabled={uploading}
                className="bg-pink-600 text-white px-4 md:px-6 py-2 rounded-lg text-xs md:text-sm font-medium hover:bg-pink-700 transition-colors disabled:opacity-50"
              >
                {uploading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>מעלה...</span>
                  </div>
                ) : (
                  'העלה סטורי'
                )}
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Story Viewer */}
      <AnimatePresence>
        {viewingStory && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center"
            onClick={() => {
              clearStoryTimer();
              setViewingStory(null);
            }}
          >
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="absolute top-4 right-4 text-white/80 hover:text-white"
            >
              <X className="w-6 h-6" />
            </motion.button>

            {/* Progress Bars */}
            <div className="absolute top-8 left-8 right-8 flex gap-2">
              {viewingStory.images.map((_, index) => {
                const isActive = index === viewingStory.currentImageIndex;
                const isPast = index < viewingStory.currentImageIndex;
                let progress = 0;

                if (isActive && viewingStory.startTime) {
                  const elapsed = Date.now() - viewingStory.startTime;
                  progress = Math.min(elapsed / storyDuration, 1);
                }

                return (
                  <div
                    key={index}
                    className="h-1 flex-1 rounded-full overflow-hidden bg-white/30"
                  >
                    <div
                      className={`h-full rounded-full transition-all duration-100 ${
                        isPast ? 'w-full bg-white/80' : isActive ? 'bg-white' : 'w-0'
                      }`}
                      style={{
                        width: isActive ? `${progress * 100}%` : undefined
                      }}
                    />
                  </div>
                );
              })}
            </div>

            {/* Navigation Arrows */}
            {viewingStory.currentImageIndex > 0 && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation();
                  setViewingStory({
                    ...viewingStory,
                    currentImageIndex: viewingStory.currentImageIndex - 1,
                    startTime: Date.now()
                  });
                }}
                className="absolute left-4 top-1/2 -translate-y-1/2 text-white/80 hover:text-white"
              >
                <ChevronLeft className="w-6 md:w-8 h-6 md:h-8" />
              </motion.button>
            )}
            {viewingStory.currentImageIndex < viewingStory.images.length - 1 && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation();
                  setViewingStory({
                    ...viewingStory,
                    currentImageIndex: viewingStory.currentImageIndex + 1,
                    startTime: Date.now()
                  });
                }}
                className="absolute right-4 top-1/2 -translate-y-1/2 text-white/80 hover:text-white"
              >
                <ChevronRight className="w-6 md:w-8 h-6 md:h-8" />
              </motion.button>
            )}

            <img
              src={viewingStory.images[viewingStory.currentImageIndex].url}
              alt="סטורי"
              className="max-h-[90vh] max-w-[90vw] object-contain"
              onClick={(e) => e.stopPropagation()}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default StoryBar;