-- Fix message sending
-- This migration fixes issues with sending messages

-- Drop existing policies on messages table
DROP POLICY IF EXISTS "VIP users can send messages" ON messages;
DROP POLICY IF EXISTS "Users can send messages to their chats" ON messages;

-- Create a simpler policy for sending messages
CREATE POLICY "Anyone can send messages"
ON messages
FOR INSERT
TO authenticated
WITH CHECK (
  sender_id = auth.uid()
);

-- Create a function to get user profile image
CREATE OR REPLACE FUNCTION get_user_profile_image(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  profile_image TEXT;
BEGIN
  SELECT profile_image_url
  INTO profile_image
  FROM profiles
  WHERE id = user_id;
  
  RETURN COALESCE(profile_image, '');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user metadata
CREATE OR REPLACE FUNCTION get_user_metadata(user_id UUID)
RETURNS JSONB AS $$
DECLARE
  user_data JSONB;
BEGIN
  SELECT jsonb_build_object(
    'username', username,
    'profile_image_url', profile_image_url,
    'is_online', is_online,
    'is_vip', is_vip
  )
  INTO user_data
  FROM profiles
  WHERE id = user_id;
  
  RETURN COALESCE(user_data, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
