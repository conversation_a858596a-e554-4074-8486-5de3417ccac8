/*
  # Update Chat RLS Policies

  1. Changes
    - Remove existing INSERT policy and create a new one with proper checks
    - Ensure authenticated users can create chats
    - Maintain existing SELECT and UPDATE policies

  2. Security
    - Only authenticated users can create chats
    - Users can only view and update chats they are participants in
*/

-- Drop existing INSERT policy
DROP POLICY IF EXISTS "Users can create chats" ON chats;

-- Create new INSERT policy with proper checks
CREATE POLICY "Users can create chats" ON chats
  FOR INSERT 
  TO authenticated
  WITH CHECK (
    -- Allow authenticated users to create chats
    -- The chat_participants table will handle participant validation
    auth.uid() IS NOT NULL
  );

-- Note: Keeping existing SELECT and UPDATE policies as they are correct:
-- Users can only view and update chats they participate in through chat_participants table