import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Crown,
  Check,
  CreditCard,
  Calendar,
  Clock,
  Shield,
  Gift,
  Star,
  AlertTriangle,
  Loader2,
  Heart,
  MessageSquare,
  Users,
  Sparkles,
  Award,
  Zap,
  Gem
} from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import Header from './Header';

interface VipPackage {
  id: string;
  name: string;
  description: string;
  price: number;
  duration_days: number;
  features: string[];
  is_active: boolean;
}

interface UserSubscription {
  id: string;
  package_id: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  package?: VipPackage;
}

function VipSubscriptionPage() {
  const { user } = useAuth();
  const [packages, setPackages] = useState<VipPackage[]>([]);
  const [userSubscription, setUserSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  useEffect(() => {
    fetchPackages();
    if (user?.id) {
      fetchUserSubscription();
    }
  }, [user?.id]);

  const fetchPackages = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('vip_packages')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setPackages(data || []);
    } catch (err) {
      console.error('Error fetching VIP packages:', err);
      setError('אירעה שגיאה בטעינת חבילות VIP. אנא נסה שוב מאוחר יותר.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserSubscription = async () => {
    if (!user?.id) return;

    try {
      const { data, error: fetchError } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          package:package_id(*)
        `)
        .eq('user_id', user.id)
        .eq('is_active', true)
        .gt('end_date', new Date().toISOString())
        .order('end_date', { ascending: false })
        .limit(1)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        throw fetchError;
      }

      setUserSubscription(data || null);
    } catch (err) {
      console.error('Error fetching user subscription:', err);
      // Don't show error to user for this one
    }
  };

  const handleSelectPackage = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handlePurchase = async () => {
    if (!user?.id || !selectedPackage) return;

    try {
      setPaymentProcessing(true);
      setError(null);

      // In a real app, you would integrate with a payment processor here
      // For this demo, we'll simulate a successful payment

      const selectedPkg = packages.find(pkg => pkg.id === selectedPackage);
      if (!selectedPkg) {
        throw new Error('חבילה לא נמצאה');
      }

      // Calculate end date based on duration_days
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + selectedPkg.duration_days);

      // Create subscription record
      const { error: insertError } = await supabase
        .from('user_subscriptions')
        .insert([{
          user_id: user.id,
          package_id: selectedPackage,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          payment_id: `demo-${Date.now()}` // In a real app, this would be the payment ID from your payment processor
        }]);

      if (insertError) {
        throw insertError;
      }

      // Refresh user subscription
      await fetchUserSubscription();

      // Reset selected package
      setSelectedPackage(null);

    } catch (err) {
      console.error('Error purchasing subscription:', err);
      setError('אירעה שגיאה ברכישת המנוי. אנא נסה שוב מאוחר יותר.');
    } finally {
      setPaymentProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('he-IL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = (endDateString: string) => {
    const endDate = new Date(endDateString);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getFeatureIcon = (feature: string) => {
    if (feature.includes('צ\'אט')) return <MessageSquare className="w-4 h-4" />;
    if (feature.includes('פנוי היום')) return <Calendar className="w-4 h-4" />;
    if (feature.includes('צפייה')) return <Users className="w-4 h-4" />;
    if (feature.includes('הודעות')) return <Zap className="w-4 h-4" />;
    if (feature.includes('תג')) return <Crown className="w-4 h-4" />;
    if (feature.includes('תמיכה')) return <Shield className="w-4 h-4" />;
    if (feature.includes('מועדפות')) return <Star className="w-4 h-4" />;
    if (feature.includes('מתנות')) return <Gift className="w-4 h-4" />;
    if (feature.includes('התראות')) return <Bell className="w-4 h-4" />;
    return <Check className="w-4 h-4" />;
  };

  return (
    <div className="w-full">
      {/* Header */}
      <Header title="מנוי VIP" />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-purple-600 via-pink-500 to-yellow-500 text-white">
        <div className="max-w-7xl mx-auto px-4 py-16 md:py-24">
          <div className="flex flex-col items-center text-center mb-12">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <Crown className="w-16 h-16 md:w-24 md:h-24 text-yellow-300" />
            </motion.div>
            <h1 className="text-3xl md:text-5xl font-bold mb-4">הצטרף ל-VIP</h1>
            <p className="text-lg md:text-xl max-w-2xl opacity-90">
              חווית היכרויות מתקדמת עם יתרונות בלעדיים, פיצ'רים מיוחדים וגישה מועדפת
            </p>
          </div>

          {/* Benefits Icons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="flex flex-col items-center text-center">
              <div className="bg-white/20 p-3 rounded-full mb-3">
                <MessageSquare className="w-8 h-8" />
              </div>
              <h3 className="font-semibold mb-1">צ'אט ללא הגבלה</h3>
              <p className="text-sm opacity-80">תקשורת חופשית עם כל המשתמשים</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-white/20 p-3 rounded-full mb-3">
                <Users className="w-8 h-8" />
              </div>
              <h3 className="font-semibold mb-1">צפייה בפרופילים</h3>
              <p className="text-sm opacity-80">גישה מלאה לכל הפרופילים</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-white/20 p-3 rounded-full mb-3">
                <Sparkles className="w-8 h-8" />
              </div>
              <h3 className="font-semibold mb-1">תג VIP</h3>
              <p className="text-sm opacity-80">בולט בחיפושים ובהודעות</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-white/20 p-3 rounded-full mb-3">
                <Shield className="w-8 h-8" />
              </div>
              <h3 className="font-semibold mb-1">תמיכה מועדפת</h3>
              <p className="text-sm opacity-80">סיוע אישי ומענה מהיר</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-12 h-12 text-pink-600 animate-spin" />
          </div>
        ) : error ? (
          <div className="bg-red-50 text-red-700 p-6 rounded-xl flex items-center gap-3 mb-8 max-w-2xl mx-auto">
            <AlertTriangle className="w-8 h-8 flex-shrink-0" />
            <span className="text-lg">{error}</span>
          </div>
        ) : (
          <>
            {/* Current Subscription */}
            {userSubscription && (
              <div className="mb-16">
                <h2 className="text-2xl font-bold mb-6 flex items-center gap-2 text-center justify-center">
                  <Crown className="w-8 h-8 text-yellow-500" />
                  <span>המנוי הנוכחי שלך</span>
                </h2>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 text-white max-w-4xl mx-auto shadow-xl"
                >
                  <div className="flex flex-col md:flex-row justify-between items-center">
                    <div className="mb-6 md:mb-0">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-white/20 p-2 rounded-full">
                          <Award className="w-6 h-6" />
                        </div>
                        <h3 className="text-2xl font-bold">{userSubscription.package?.name}</h3>
                      </div>
                      <p className="opacity-90 mb-6 text-lg">{userSubscription.package?.description}</p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center gap-3 bg-white/10 p-3 rounded-lg">
                          <Calendar className="w-5 h-5 text-yellow-300 flex-shrink-0" />
                          <div>
                            <div className="text-sm opacity-80">תאריך התחלה</div>
                            <div>{formatDate(userSubscription.start_date)}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 bg-white/10 p-3 rounded-lg">
                          <Calendar className="w-5 h-5 text-yellow-300 flex-shrink-0" />
                          <div>
                            <div className="text-sm opacity-80">תאריך סיום</div>
                            <div>{formatDate(userSubscription.end_date)}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="bg-white/20 rounded-full p-6 mb-3 relative">
                        <Clock className="w-10 h-10 text-yellow-300" />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-3xl font-bold">{getDaysRemaining(userSubscription.end_date)}</div>
                        </div>
                      </div>
                      <div className="text-sm opacity-80">ימים שנותרו</div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="mt-6 bg-white text-purple-700 px-6 py-3 rounded-xl font-medium hover:bg-yellow-100 transition-colors shadow-md"
                      >
                        שדרג מנוי
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              </div>
            )}

            {/* Available Packages */}
            <div className="mb-16">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4 flex items-center gap-2 justify-center">
                  <Gem className="w-8 h-8 text-pink-500" />
                  <span>חבילות VIP</span>
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  בחר את החבילה המתאימה לך ותהנה מכל היתרונות שיש לנו להציע. כל החבילות כוללות את כל הפיצ'רים הבסיסיים.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                {packages.map((pkg) => (
                  <motion.div
                    key={pkg.id}
                    whileHover={{ y: -5 }}
                    className={`rounded-2xl overflow-hidden shadow-lg transition-all duration-300 ${
                      selectedPackage === pkg.id
                        ? 'ring-4 ring-pink-500 transform scale-105'
                        : 'border border-gray-200'
                    }`}
                  >
                    <div className={`p-6 ${
                      pkg.name.includes('בסיסית') ? 'bg-gradient-to-r from-blue-500 to-cyan-500' :
                      pkg.name.includes('מתקדמת') ? 'bg-gradient-to-r from-purple-500 to-pink-500' :
                      'bg-gradient-to-r from-yellow-500 to-amber-500'
                    } text-white`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-xl font-bold mb-1">{pkg.name}</h3>
                          <p className="opacity-90">{pkg.description}</p>
                        </div>
                        <div className={`p-2 rounded-full ${
                          pkg.name.includes('בסיסית') ? 'bg-blue-400/30' :
                          pkg.name.includes('מתקדמת') ? 'bg-purple-400/30' :
                          'bg-yellow-400/30'
                        }`}>
                          {pkg.name.includes('בסיסית') ? <Shield className="w-6 h-6" /> :
                           pkg.name.includes('מתקדמת') ? <Crown className="w-6 h-6" /> :
                           <Award className="w-6 h-6" />}
                        </div>
                      </div>

                      <div className="mt-6 flex items-baseline">
                        <span className="text-4xl font-bold">₪{pkg.price}</span>
                        <span className="ml-2 opacity-80">/ {pkg.duration_days} ימים</span>
                      </div>
                    </div>

                    <div className="p-6 bg-white">
                      <ul className="space-y-4 mb-8">
                        {pkg.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-3 text-gray-700">
                            <span className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                              pkg.name.includes('בסיסית') ? 'bg-blue-100 text-blue-600' :
                              pkg.name.includes('מתקדמת') ? 'bg-purple-100 text-purple-600' :
                              'bg-amber-100 text-amber-600'
                            }`}>
                              {getFeatureIcon(feature)}
                            </span>
                            <span className="text-gray-800">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => handleSelectPackage(pkg.id)}
                        className={`w-full py-3 rounded-xl font-medium transition-all duration-300 ${
                          selectedPackage === pkg.id
                            ? pkg.name.includes('בסיסית') ? 'bg-blue-600 text-white' :
                              pkg.name.includes('מתקדמת') ? 'bg-purple-600 text-white' :
                              'bg-amber-600 text-white'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {selectedPackage === pkg.id ? 'נבחר ✓' : 'בחר חבילה'}
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* FAQ Section */}
              <div className="max-w-3xl mx-auto bg-gray-50 rounded-2xl p-8 mb-12">
                <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
                  <Zap className="w-6 h-6 text-purple-500" />
                  <span>שאלות נפוצות</span>
                </h3>

                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold text-lg mb-2">איך אני מבטל את המנוי?</h4>
                    <p className="text-gray-600">ניתן לבטל את המנוי בכל עת דרך הגדרות החשבון. לא יינתן החזר כספי עבור התקופה שנותרה.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg mb-2">האם המנוי מתחדש אוטומטית?</h4>
                    <p className="text-gray-600">לא, המנוי אינו מתחדש אוטומטית. בסיום תקופת המנוי תקבל התראה ותוכל לחדש אותו.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg mb-2">האם אפשר לשדרג חבילה באמצע תקופת מנוי?</h4>
                    <p className="text-gray-600">כן, ניתן לשדרג את החבילה בכל עת. תשלם רק את ההפרש בין החבילות.</p>
                  </div>
                </div>
              </div>

              {/* Purchase Button */}
              {selectedPackage && (
                <div className="flex justify-center">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handlePurchase}
                    disabled={paymentProcessing}
                    className="flex items-center gap-3 bg-gradient-to-r from-pink-600 to-purple-600 text-white px-10 py-4 rounded-xl font-medium text-lg shadow-xl hover:from-pink-700 hover:to-purple-700 transition-colors"
                  >
                    {paymentProcessing ? (
                      <>
                        <Loader2 className="w-6 h-6 animate-spin" />
                        <span>מעבד תשלום...</span>
                      </>
                    ) : (
                      <>
                        <CreditCard className="w-6 h-6" />
                        <span>רכוש מנוי עכשיו</span>
                      </>
                    )}
                  </motion.button>
                </div>
              )}

              {/* Testimonials */}
              <div className="mt-20">
                <h3 className="text-2xl font-bold mb-8 text-center">מה חברי ה-VIP שלנו אומרים</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
                        <span className="text-pink-600 font-bold">ד</span>
                      </div>
                      <div>
                        <div className="font-semibold">דני</div>
                        <div className="text-sm text-gray-500">חבר VIP כבר 6 חודשים</div>
                      </div>
                    </div>
                    <p className="text-gray-700">"מאז שהצטרפתי ל-VIP, חווית ההיכרויות שלי השתפרה פלאים. אני מקבל הרבה יותר תגובות והצלחתי למצוא קשרים איכותיים."</p>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-purple-600 font-bold">מ</span>
                      </div>
                      <div>
                        <div className="font-semibold">מיכל ודן</div>
                        <div className="text-sm text-gray-500">חברי VIP כבר 3 חודשים</div>
                      </div>
                    </div>
                    <p className="text-gray-700">"היכולת לראות מי צפה בפרופיל שלנו ולשלוח הודעות ללא הגבלה שינתה את כל החוויה. ממליצים בחום על חבילת VIP המתקדמת!"</p>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                        <span className="text-amber-600 font-bold">ר</span>
                      </div>
                      <div>
                        <div className="font-semibold">רונית</div>
                        <div className="text-sm text-gray-500">חברת VIP כבר שנה</div>
                      </div>
                    </div>
                    <p className="text-gray-700">"החבילה השנתית היא הכי משתלמת! אני נהנית מכל היתרונות ומהתמיכה המעולה. מצאתי הרבה קשרים איכותיים בזכות הפיצ'רים המיוחדים."</p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default VipSubscriptionPage;
