import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';
import { Profile } from '../types/supabase';
import { useProfile } from './useProfile';

interface PhotosHookProps {
  profile?: Profile;
}

export function usePhotos({ profile }: PhotosHookProps = {}) {
  const { user } = useAuth();
  const { updateProfile } = useProfile();
  const [photos, setPhotos] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);

  useEffect(() => {
    if (!profile?.id) {
      setLoading(false);
      return;
    }

    const fetchPhotos = async () => {
      try {
        const { data, error } = await supabase.storage
          .from('photos')
          .list(profile.id + '/', {
            limit: 100,
            offset: 0,
            sortBy: { column: 'name', order: 'asc' },
          });

        if (error) {
          console.error('Error listing files:', error);
          throw error;
        }

        if (!data || data.length === 0) {
          setPhotos([]);
          return;
        }

        const photoUrls = await Promise.all(
          data.map(async (file) => {
            const { data } = supabase.storage
              .from('photos')
              .getPublicUrl(`${profile.id}/${file.name}`);
            return data.publicUrl;
          })
        );

        setPhotos(photoUrls.filter(url => url));

        // Check if there's a profile image set in user metadata
        if (user?.user_metadata?.profile_image_url) {
          setProfileImageUrl(user.user_metadata.profile_image_url);
        }
      } catch (err) {
        console.error('Error fetching photos:', err);
        setError(err instanceof Error ? err.message : 'אירעה שגיאה בטעינת התמונות');
      } finally {
        setLoading(false);
      }
    };

    fetchPhotos();
  }, [profile?.id, user?.user_metadata?.profile_image_url]);

  const uploadPhoto = async (file: File) => {
    if (!user) return;

    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('photos')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('photos')
        .getPublicUrl(fileName);

      const publicUrl = data.publicUrl;

      setPhotos(prev => [...prev, publicUrl]);
      return { error: null };
    } catch (err) {
      console.error('Upload error:', err);
      return { error: err };
    }
  };

  const deletePhoto = async (url: string) => {
    if (!user) return;

    try {
      const fileName = url.split('/').pop();
      if (!fileName) throw new Error('Invalid file name');

      const { error } = await supabase.storage
        .from('photos')
        .remove([`${user.id}/${fileName}`]);

      if (error) {
        console.error('Error deleting file:', error);
        throw error;
      }

      setPhotos(prev => prev.filter(photo => photo !== url));
      return { error: null };
    } catch (err) {
      console.error('Error deleting photo:', err);
      return { error: err };
    }
  };

  const setAsProfileImage = async (url: string) => {
    if (!user) return { error: 'User not authenticated' };

    try {
      // Update user metadata with the profile image URL
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          profile_image_url: url
        }
      });

      if (updateError) throw updateError;

      // Note: We don't update the profiles table directly since user_metadata
      // is stored in auth.users table, not in profiles table

      setProfileImageUrl(url);

      // Trigger a custom event to notify other components about the profile image change
      console.log('usePhotos: Dispatching profileImageChanged event for user:', user.id, 'new URL:', url);
      window.dispatchEvent(new CustomEvent('profileImageChanged', {
        detail: { userId: user.id, newImageUrl: url }
      }));

      return { error: null };
    } catch (err) {
      console.error('Error setting profile image:', err);
      return { error: err };
    }
  };

  return {
    photos,
    loading,
    error,
    uploadPhoto,
    deletePhoto,
    setAsProfileImage,
    profileImageUrl
  };
}