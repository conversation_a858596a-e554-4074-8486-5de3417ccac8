/*
  # Add Chat Participants Insert Policy

  1. Changes
    - Add policy to allow authenticated users to insert into chat_participants table
    - Maintain existing security model
    
  2. Security
    - Only authenticated users can add chat participants
    - Users can only add themselves or be added to chats
*/

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON chat_participants;

-- Create policy to allow authenticated users to insert chat participants
CREATE POLICY "Allow insert for authenticated users"
ON chat_participants
FOR INSERT
TO public
WITH CHECK (
  -- Allow authenticated users to create chat participants
  auth.uid() IS NOT NULL
);