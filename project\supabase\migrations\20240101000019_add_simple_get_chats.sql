-- Add simple get chats function
-- This migration adds a simpler function to get chats as a fallback

-- Create a simpler function to get user chats
CREATE OR REPLACE FUNCTION get_user_chats_simple(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS chat_id,
    c.last_message,
    c.last_message_at,
    c.updated_at
  FROM 
    chats c
  JOIN 
    chat_participants cp ON c.id = cp.chat_id
  WHERE 
    cp.user_id = p_user_id
  ORDER BY 
    c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
