import React, { useState, useEffect } from 'react';
import ReactD<PERSON> from 'react-dom';
import { motion } from 'framer-motion';
import { MapPin, Gift, Crown, Users, MessageSquare, Calendar } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from '../hooks/useNavigate';

import { Profile } from '../types/supabase';
import { supabase } from '../lib/supabase';
import SendGiftDialog from './SendGiftDialog';

interface CompactUserCardProps {
  profile: Profile;
}

function CompactUserCard({ profile }: CompactUserCardProps) {
  const { navigateToProfile } = useNavigate();
  const [isGiftDialogOpen, setIsGiftDialogOpen] = useState(false);

  // Get the current profile image (will update automatically when changed)
  const [currentProfileImage, setCurrentProfileImage] = useState<string | null>(null);

  // Initialize profile image
  useEffect(() => {
    let initialImage = profile.user_metadata?.profile_image_url;
    if (!initialImage && profile.photos && profile.photos.length > 0) {
      initialImage = profile.photos[0];
    }
    setCurrentProfileImage(initialImage || null);
  }, [profile]);

  // Listen for profile image changes
  useEffect(() => {
    const handleProfileImageChange = (event: CustomEvent) => {
      const { userId: changedUserId, newImageUrl } = event.detail;

      // Only update if this is for the current profile
      if (changedUserId === profile.id) {
        console.log('✅ CompactUserCard: Updating image for profile:', profile.id, 'new URL:', newImageUrl);
        setCurrentProfileImage(newImageUrl);
      }
    };

    window.addEventListener('profileImageChanged', handleProfileImageChange as EventListener);

    return () => {
      window.removeEventListener('profileImageChanged', handleProfileImageChange as EventListener);
    };
  }, [profile.id]);

  // Log profile data for debugging
  useEffect(() => {
    console.log('CompactUserCard rendering for profile:', profile.username,
      'with metadata:', profile.user_metadata);
  }, [profile]);

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  const getGenderIcon = () => {
    switch (profile.gender) {
      case 'male':
        return <FontAwesomeIcon icon={faMars} className="text-blue-600" />;
      case 'female':
        return <FontAwesomeIcon icon={faVenus} className="text-pink-600" />;
      case 'couple':
        return <Users className="h-4 w-4 text-purple-600" />;
      default:
        return null;
    }
  };

  const { navigateToChat } = useNavigate();

  const handleChat = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    // Navigate to chat with this user
    navigateToChat(profile.id);
  };

  const handleGift = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsGiftDialogOpen(true);
  };

  // Translation functions
  const translateSexualPreference = (preference: string) => {
    const translations: Record<string, string> = {
      straight: 'סטרייט',
      bisexual: 'דו מיני/ת',
      gay: 'הומו',
      lesbian: 'לסבית',
      pansexual: 'פאנסקסואל/ית',
      asexual: 'א-מיני/ת',
      queer: 'קוויר',
      questioning: 'בתהליך בירור',
      heteroflexible: 'הטרו-גמיש/ה',
      homoflexible: 'הומו-גמיש/ה'
    };
    return translations[preference] || preference;
  };

  const translateBodyType = (bodyType: string) => {
    const translations: Record<string, string> = {
      slim: 'רזה',
      athletic: 'אתלטי/ת',
      average: 'ממוצע/ת',
      curvy: 'מלא/ה',
      muscular: 'שרירי/ת',
      heavy: 'כבד/ה',
      'plus-size': 'מידות גדולות'
    };
    return translations[bodyType] || bodyType;
  };

  const translateHabits = (habit: string) => {
    const translations: Record<string, string> = {
      'non-smoker': 'לא מעשן/ת',
      'light-smoker': 'מעשן/ת לעיתים',
      'regular-smoker': 'מעשן/ת באופן קבוע',
      'non-drinker': 'לא שותה',
      'social-drinker': 'שותה חברתי/ת',
      'regular-drinker': 'שותה באופן קבוע'
    };
    return translations[habit] || habit;
  };

  const translateMeetingTime = (time: string) => {
    const translations: Record<string, string> = {
      morning: 'בוקר',
      afternoon: 'צהריים',
      evening: 'ערב'
    };
    return translations[time] || time;
  };

  // Check if the profile is online or free today
  const isOnline = true; // This would be determined by your app's logic
  const isFreeToday = true; // This would be determined by your app's logic

  // Function to handle profile navigation with complete data
  const handleProfileClick = async () => {
    try {
      console.log('Fetching complete profile data for:', profile.id);

      // Try to get complete profile data using RPC
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('get_complete_profile', { profile_id: profile.id });

        if (!rpcError && rpcData) {
          console.log('Complete profile data fetched with RPC:', rpcData);

          // Make sure we have user_metadata
          if (!rpcData.user_metadata) {
            rpcData.user_metadata = {};
          }

          // Make sure we have profile_data in user_metadata
          if (!rpcData.user_metadata.profile_data) {
            rpcData.user_metadata.profile_data = {};
          }

          // If we have profile_data directly on the profile, copy it to user_metadata
          if (rpcData.profile_data && Object.keys(rpcData.profile_data).length > 0) {
            console.log('Copying profile_data to user_metadata.profile_data');
            rpcData.user_metadata.profile_data = {
              ...rpcData.user_metadata.profile_data,
              ...rpcData.profile_data
            };
          }

          // Navigate with the complete profile data
          navigateToProfile(rpcData);
          return;
        }
      } catch (rpcErr) {
        console.error('Error fetching with RPC:', rpcErr);
      }

      // Fallback to regular query
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profile.id)
        .single();

      if (error) {
        console.error('Error fetching complete profile data:', error);
        // Fall back to using the profile we have
        navigateToProfile(profile);
        return;
      }

      if (data) {
        console.log('Complete profile data fetched:', data);

        // Make sure we have user_metadata
        if (!data.user_metadata) {
          data.user_metadata = {};
        }

        // Make sure we have profile_data in user_metadata
        if (!data.user_metadata.profile_data) {
          data.user_metadata.profile_data = {};
        }

        // If we have profile_data directly on the profile, copy it to user_metadata
        if (data.profile_data && Object.keys(data.profile_data).length > 0) {
          console.log('Copying profile_data to user_metadata.profile_data');
          data.user_metadata.profile_data = {
            ...data.user_metadata.profile_data,
            ...data.profile_data
          };
        }

        // Navigate with the complete profile data
        navigateToProfile(data);
      } else {
        // Fall back to using the profile we have
        navigateToProfile(profile);
      }
    } catch (err) {
      console.error('Error in handleProfileClick:', err);
      // Fall back to using the profile we have
      navigateToProfile(profile);
    }
  };

  return (
    <div
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden mb-4 cursor-pointer"
      onClick={handleProfileClick}
    >
      <div className="flex p-4 items-start">
        {/* Right side - Profile Image */}
        <div className="relative w-24 h-24 md:w-28 md:h-28 overflow-hidden rounded-lg flex-shrink-0 border-2 border-pink-100 ml-4">
          {currentProfileImage || profile.user_metadata?.profile_image_url ? (
            <img
              src={currentProfileImage || profile.user_metadata?.profile_image_url}
              alt={profile.username}
              className="w-full h-full object-cover"
            />
          ) : profile.photos && profile.photos.length > 0 ? (
            <img
              src={profile.photos[0]}
              alt={profile.username}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center">
              <Users className="w-10 h-10 text-pink-300" />
            </div>
          )}

          {/* VIP Badge */}
          {profile.is_vip && (
            <div className="absolute top-0 right-0 bg-gradient-to-r from-yellow-500 to-amber-500 text-white px-2 py-0.5 rounded-full flex items-center gap-0.5 text-xs transform translate-x-1/3 -translate-y-1/3 shadow-sm">
              <Crown className="w-3 h-3" />
              <span className="font-medium">VIP</span>
            </div>
          )}
        </div>

        {/* Middle - User Info and Details */}
        <div className="flex-1">
          {/* User Basic Info */}
          <div className="mb-2">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-semibold text-lg text-gray-800">{profile.username}</span>
              <div className="flex items-center gap-1.5 bg-gray-100 px-2 py-0.5 rounded-full">
                <span className="text-sm font-medium text-gray-700">{calculateAge(profile.birth_date)}</span>
                <span className="text-gray-400">|</span>
                <span className="flex items-center">{getGenderIcon()}</span>
              </div>
            </div>

            <div className="flex items-center gap-1 text-gray-500 text-sm mb-2">
              <MapPin className="w-4 h-4 flex-shrink-0" />
              <span>{profile.city}</span>
            </div>

            {/* Status badges */}
            <div className="flex flex-wrap gap-2 mb-3">
              {isOnline && (
                <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                  <span>פנוי עכשיו</span>
                </div>
              )}
              {isFreeToday && (
                <div className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-sm flex items-center">
                  <Calendar className="w-4 h-4 mr-0.5" />
                  <span>פנוי היום</span>
                </div>
              )}
            </div>
          </div>

          {/* User Details - In the same row as image */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {/* About Us */}
            {profile.profile_data?.aboutUs && (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-1 flex items-center">
                  <span className="w-1.5 h-1.5 bg-pink-400 rounded-full mr-1"></span>
                  קצת עלינו:
                </h4>
                <p className="text-sm text-gray-600 line-clamp-2">{profile.profile_data.aboutUs}</p>
              </div>
            )}

            {/* Looking For */}
            {profile.profile_data?.lookingFor && (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-1 flex items-center">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-1"></span>
                  מה אנחנו מחפשים:
                </h4>
                <p className="text-sm text-gray-600 line-clamp-2">{profile.profile_data.lookingFor}</p>
              </div>
            )}

            {/* Preferred Meeting Times */}
            {profile.profile_data?.meetingTimes && (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-1 flex items-center">
                  <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-1"></span>
                  זמני מפגש מועדפים:
                </h4>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {Array.isArray(profile.profile_data.meetingTimes)
                    ? profile.profile_data.meetingTimes.map(time => translateMeetingTime(time)).join(', ')
                    : translateMeetingTime(profile.profile_data.meetingTimes)}
                </p>
              </div>
            )}

            {/* If no profile data is available */}
            {!profile.profile_data?.aboutUs && !profile.profile_data?.lookingFor &&
             !profile.profile_data?.meetingTimes && (
              <div className="text-sm text-gray-500 italic text-center py-2">לחץ לצפייה בפרופיל המלא</div>
            )}
          </div>
        </div>

        {/* Left side - Action buttons */}
        <div className="flex flex-col gap-2 mr-0 ml-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={(e) => {
              e.stopPropagation();
              handleChat(e);
            }}
            className="flex items-center justify-center gap-1.5 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-pink-600 hover:to-pink-700 transition-colors text-sm shadow-sm"
          >
            <MessageSquare className="w-4 h-4" />
            <span>צ'אט</span>
          </motion.button>
          <div
            onClick={(e) => {
              e.stopPropagation();
              handleGift(e);
            }}
            className="flex items-center justify-center p-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-colors shadow-sm cursor-pointer"
          >
            <Gift className="w-5 h-5" />
          </div>
        </div>
      </div>

      {/* Gift Dialog - Render outside the card to prevent event bubbling issues */}
      {isGiftDialogOpen && ReactDOM.createPortal(
        <SendGiftDialog
          isOpen={isGiftDialogOpen}
          onClose={() => setIsGiftDialogOpen(false)}
          receiverId={profile.id}
          receiverName={profile.username}
        />,
        document.body
      )}
    </div>
  );
}

export default CompactUserCard;
