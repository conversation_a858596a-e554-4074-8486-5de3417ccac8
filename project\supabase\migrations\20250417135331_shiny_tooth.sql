/*
  # Authentication Schema Setup

  1. New Tables
    - `profiles` table with all user profile fields
    - Links to Supabase auth.users table
  
  2. Security
    - Enable RLS on profiles table
    - Add policies for authenticated users
    - Add trigger for automatic profile creation

  Note: Added checks to prevent duplicate policy creation
*/

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  username text UNIQUE NOT NULL,
  gender text NOT NULL,
  birth_date date NOT NULL,
  partner_birth_date date,
  city text NOT NULL,
  area text NOT NULL,
  phone text NOT NULL,
  height integer,
  weight integer,
  hair_color text,
  hair_style text,
  eye_color text,
  body_type text,
  marital_status text,
  children integer,
  ethnicity text,
  sexual_preference text,
  swinging_experience text,
  smoking_habits text,
  drinking_habits text,
  about_us text,
  looking_for text,
  seeking_gender text[],
  meeting_times text[],
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT username_length CHECK (char_length(username) >= 3)
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies with safety checks
DO $$ 
BEGIN
  -- Check and create "Users can view their own profile" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can view their own profile'
  ) THEN
    CREATE POLICY "Users can view their own profile"
      ON profiles
      FOR SELECT
      TO authenticated
      USING (auth.uid() = id);
  END IF;

  -- Check and create "Users can update their own profile" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can update their own profile'
  ) THEN
    CREATE POLICY "Users can update their own profile"
      ON profiles
      FOR UPDATE
      TO authenticated
      USING (auth.uid() = id);
  END IF;

  -- Check and create "Users can view other profiles" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can view other profiles'
  ) THEN
    CREATE POLICY "Users can view other profiles"
      ON profiles
      FOR SELECT
      TO authenticated
      USING (true);
  END IF;
END $$;

-- Create or replace function to handle user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, gender, birth_date, city, area, phone)
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.raw_user_meta_data->>'gender',
    (new.raw_user_meta_data->>'birth_date')::date,
    new.raw_user_meta_data->>'city',
    new.raw_user_meta_data->>'area',
    new.raw_user_meta_data->>'phone'
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop trigger if exists and create new one
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();