import { useState } from 'react';
import { useAuth } from './useAuth';

interface SignupState {
  step: number;
  data: {
    // Basic info
    email: string;
    password: string;
    username: string;
    gender: 'male' | 'female' | 'couple' | null;
    birthDate: string;
    partnerBirthDate?: string;
    city: string;
    area: string;
    phone: string;
    
    // Profile details - Single
    name?: string;
    height?: string;
    weight?: string;
    hairColor?: string;
    hairStyle?: string;
    eyeColor?: string;
    bodyType?: string;
    maritalStatus?: string;
    children?: string;
    ethnicity?: string;
    sexualPreference?: string;
    swingingExperience?: string;
    smokingHabits?: string;
    drinkingHabits?: string;
    aboutUs?: string;
    lookingFor?: string;
    seekingGender?: string[];
    meetingTimes?: string[];

    // Profile details - Couple
    name1?: string;
    height1?: string;
    weight1?: string;
    hairColor1?: string;
    hairStyle1?: string;
    eyeColor1?: string;
    bodyType1?: string;
    ethnicity1?: string;
    sexualPreference1?: string;
    swingingExperience1?: string;
    smokingHabits1?: string;
    drinkingHabits1?: string;
    
    name2?: string;
    height2?: string;
    weight2?: string;
    hairColor2?: string;
    hairStyle2?: string;
    eyeColor2?: string;
    bodyType2?: string;
    ethnicity2?: string;
    sexualPreference2?: string;
    swingingExperience2?: string;
    smokingHabits2?: string;
    drinkingHabits2?: string;
  };
}

export function useSignup() {
  const { signUp } = useAuth();
  const [state, setState] = useState<SignupState>({
    step: 1,
    data: {
      email: '',
      password: '',
      username: '',
      gender: null,
      birthDate: '',
      city: '',
      area: '',
      phone: '',
    },
  });

  const setStep = (step: number) => {
    setState(prev => ({ ...prev, step }));
  };

  const updateData = (newData: Partial<SignupState['data']>) => {
    setState(prev => ({
      ...prev,
      data: { ...prev.data, ...newData },
    }));
  };

  const handleSignup = async () => {
    try {
      const metadata: any = {
        username: state.data.username,
        gender: state.data.gender,
        birth_date: state.data.birthDate,
        city: state.data.city,
        area: state.data.area,
        phone: state.data.phone,
      };

      // Add partner birth date for couples
      if (state.data.gender === 'couple' && state.data.partnerBirthDate) {
        metadata.partner_birth_date = state.data.partnerBirthDate;
      }

      // Prepare profile data based on gender
      const profileData: any = {
        maritalStatus: state.data.maritalStatus,
        children: state.data.children,
        aboutUs: state.data.aboutUs,
        lookingFor: state.data.lookingFor,
        seekingGender: state.data.seekingGender || [],
        meetingTimes: state.data.meetingTimes || []
      };

      if (state.data.gender === 'couple') {
        // Partner 1 (Male) details
        Object.assign(profileData, {
          name1: state.data.name1,
          height1: state.data.height1,
          weight1: state.data.weight1,
          hairColor1: state.data.hairColor1,
          hairStyle1: state.data.hairStyle1,
          eyeColor1: state.data.eyeColor1,
          bodyType1: state.data.bodyType1,
          ethnicity1: state.data.ethnicity1,
          sexualPreference1: state.data.sexualPreference1,
          swingingExperience1: state.data.swingingExperience1,
          smokingHabits1: state.data.smokingHabits1,
          drinkingHabits1: state.data.drinkingHabits1,
          
          // Partner 2 (Female) details
          name2: state.data.name2,
          height2: state.data.height2,
          weight2: state.data.weight2,
          hairColor2: state.data.hairColor2,
          hairStyle2: state.data.hairStyle2,
          eyeColor2: state.data.eyeColor2,
          bodyType2: state.data.bodyType2,
          ethnicity2: state.data.ethnicity2,
          sexualPreference2: state.data.sexualPreference2,
          swingingExperience2: state.data.swingingExperience2,
          smokingHabits2: state.data.smokingHabits2,
          drinkingHabits2: state.data.drinkingHabits2
        });
      } else {
        // Single person details
        Object.assign(profileData, {
          name: state.data.name,
          height: state.data.height,
          weight: state.data.weight,
          hairColor: state.data.hairColor,
          hairStyle: state.data.hairStyle,
          eyeColor: state.data.eyeColor,
          bodyType: state.data.bodyType,
          ethnicity: state.data.ethnicity,
          sexualPreference: state.data.sexualPreference,
          swingingExperience: state.data.swingingExperience,
          smokingHabits: state.data.smokingHabits,
          drinkingHabits: state.data.drinkingHabits
        });
      }

      // Add profile data to metadata
      metadata.profile_data = profileData;

      const { error } = await signUp(state.data.email, state.data.password, metadata);

      if (error) throw error;

      return { error: null };
    } catch (error) {
      return { error };
    }
  };

  return {
    step: state.step,
    data: state.data,
    setStep,
    updateData,
    handleSignup,
  };
}