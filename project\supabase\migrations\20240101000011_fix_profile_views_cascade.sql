-- Fix profile views cascade issues
-- This migration drops and recreates the profile_views table, policies, and functions with CASCADE option

-- Drop everything with <PERSON><PERSON>AD<PERSON> to ensure clean state
DROP TRIGGER IF EXISTS on_profile_view_change ON profile_views CASCADE;
DROP FUNCTION IF EXISTS refresh_profile_views() CASCADE;
DROP FUNCTION IF EXISTS get_profiles_who_viewed_me(uuid) CASCADE;
DROP FUNCTION IF EXISTS get_profiles_i_viewed(uuid) CASCADE;
DROP FUNCTION IF EXISTS count_profiles_who_viewed_me(uuid) CASCADE;
DROP FUNCTION IF EXISTS count_profiles_i_viewed(uuid) CASCADE;
DROP FUNCTION IF EXISTS record_profile_view(uuid, uuid) CASCADE;
DROP FUNCTION IF EXISTS insert_profile_view_bypass_rls(uuid, uuid) CASCADE;

-- Drop the table (this will also drop all policies)
DROP TABLE IF EXISTS profile_views CASCADE;

-- Create the table
CREATE TABLE profile_views (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  viewer_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  viewed_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE (viewer_id, viewed_id)
);

-- Enable RLS
ALTER TABLE profile_views ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Policy for SELECT: Users can view their own profile views (as viewer or viewed)
CREATE POLICY "Users can view their own profile views"
  ON profile_views
  FOR SELECT
  TO authenticated
  USING (auth.uid() = viewer_id OR auth.uid() = viewed_id);

-- Policy for INSERT: Users can insert profile views where they are the viewer
CREATE POLICY "Users can insert profile views"
  ON profile_views
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = viewer_id);

-- Policy for UPDATE: Users can update their own profile views
CREATE POLICY "Users can update their own profile views"
  ON profile_views
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = viewer_id)
  WITH CHECK (auth.uid() = viewer_id);

-- Policy for DELETE: Users can delete their own profile views
CREATE POLICY "Users can delete their own profile views"
  ON profile_views
  FOR DELETE
  TO authenticated
  USING (auth.uid() = viewer_id);

-- Policy for admins: Admins can do anything with profile views
CREATE POLICY "Admins can manage all profile views"
  ON profile_views
  FOR ALL
  TO authenticated
  USING (auth.email() = '<EMAIL>')
  WITH CHECK (auth.email() = '<EMAIL>');

-- Create function to record profile view (with SECURITY DEFINER to bypass RLS)
CREATE OR REPLACE FUNCTION record_profile_view(viewer uuid, viewed uuid)
RETURNS uuid AS $$
DECLARE
  view_id uuid;
BEGIN
  -- Don't record if viewing own profile
  IF viewer = viewed THEN
    RETURN NULL;
  END IF;

  -- Insert or update the profile view
  INSERT INTO profile_views (viewer_id, viewed_id, created_at)
  VALUES (viewer, viewed, now())
  ON CONFLICT (viewer_id, viewed_id)
  DO UPDATE SET created_at = now()
  RETURNING id INTO view_id;
  
  -- Log the view for debugging
  RAISE NOTICE 'Profile view recorded: viewer % viewed % (id: %)', viewer, viewed, view_id;
  
  RETURN view_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
