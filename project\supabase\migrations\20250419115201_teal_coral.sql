/*
  # Add Chat Deletion Policy

  1. Changes
    - Add policy to allow users to delete their chats
    - Cascade deletion to related records
    
  2. Security
    - Users can only delete chats they participate in
    - Automatic cleanup of related messages and participants
*/

-- Add policy for chat deletion
CREATE POLICY "Users can delete their chats"
ON chats
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = id
    AND chat_participants.user_id = auth.uid()
  )
);