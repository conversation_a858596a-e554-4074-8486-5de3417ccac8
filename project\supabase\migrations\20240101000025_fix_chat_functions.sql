-- Fix chat functions
-- This migration fixes issues with chat functions

-- Fix get_chat_messages function
DROP FUNCTION IF EXISTS get_chat_messages(UUID, UUID);

CREATE OR REPLACE FUNCTION get_chat_messages(p_chat_id UUID, p_user_id UUID)
RETURNS TABLE (
  id UUID,
  chat_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  is_edited BOOLEAN,
  is_deleted BOOLEAN,
  reply_to_id UUID,
  read <PERSON><PERSON><PERSON><PERSON><PERSON>,
  sender_username TEXT,
  sender_profile_image TEXT,
  sender_is_online BOOLEAN,
  reply_message TEXT
) AS $$
BEGIN
  -- Mark messages as read
  UPDATE messages
  SET read = TRUE
  WHERE 
    chat_id = p_chat_id
    AND sender_id != p_user_id
    AND (read IS NULL OR read = FALSE);
    
  -- Update last_read_at for the user
  UPDATE chat_participants
  SET last_read_at = NOW()
  WHERE 
    chat_id = p_chat_id
    AND user_id = p_user_id;
  
  -- Return messages with sender details
  RETURN QUERY
  SELECT 
    m.id,
    m.chat_id,
    m.sender_id,
    m.content,
    m.created_at,
    m.updated_at,
    COALESCE(m.is_edited, FALSE),
    COALESCE(m.is_deleted, FALSE),
    m.reply_to_id,
    COALESCE(m.read, FALSE),
    p.username AS sender_username,
    p.profile_image_url AS sender_profile_image,
    COALESCE(p.is_online, FALSE) AS sender_is_online,
    rm.content AS reply_message
  FROM 
    messages m
  JOIN 
    profiles p ON m.sender_id = p.id
  LEFT JOIN
    messages rm ON m.reply_to_id = rm.id
  WHERE 
    m.chat_id = p_chat_id
  ORDER BY 
    m.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix get_user_chats function
DROP FUNCTION IF EXISTS get_user_chats(UUID);

CREATE OR REPLACE FUNCTION get_user_chats(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  other_user_id UUID,
  other_username TEXT,
  other_profile_image TEXT,
  other_is_online BOOLEAN,
  unread_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS chat_id,
    c.created_at,
    c.updated_at,
    c.last_message,
    c.last_message_at,
    p.id AS other_user_id,
    p.username AS other_username,
    p.profile_image_url AS other_profile_image,
    COALESCE(p.is_online, FALSE) AS other_is_online,
    COUNT(m.id) FILTER (
      WHERE m.sender_id != p_user_id 
      AND (m.read IS NULL OR m.read = FALSE)
    ) AS unread_count
  FROM 
    chats c
  JOIN 
    chat_participants cp1 ON c.id = cp1.chat_id 
    AND cp1.user_id = p_user_id
    AND COALESCE(cp1.is_active, TRUE) = TRUE
  JOIN 
    chat_participants cp2 ON c.id = cp2.chat_id 
    AND cp2.user_id != p_user_id
    AND COALESCE(cp2.is_active, TRUE) = TRUE
  JOIN 
    profiles p ON cp2.user_id = p.id
  LEFT JOIN 
    messages m ON c.id = m.chat_id
  WHERE
    COALESCE(c.is_deleted, FALSE) = FALSE
  GROUP BY 
    c.id, p.id
  ORDER BY 
    c.updated_at DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
