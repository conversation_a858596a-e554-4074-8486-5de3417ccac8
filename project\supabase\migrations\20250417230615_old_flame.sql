/*
  # Fix Profile Data Storage

  1. Changes
    - Update handle_new_user function to store profile_data
    - Add profile_data column to profiles table
    - Ensure all signup data is properly saved

  2. Security
    - Maintain existing RLS policies
    - No changes to security model
*/

-- Add profile_data column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'profile_data'
  ) THEN
    ALTER TABLE profiles ADD COLUMN profile_data jsonb;
  END IF;
END $$;

-- Update the handle_new_user function to include profile_data
CREATE OR REPLACE FUNCTION handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    username,
    gender,
    birth_date,
    partner_birth_date,
    city,
    area,
    phone,
    profile_data
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.raw_user_meta_data->>'gender',
    (new.raw_user_meta_data->>'birth_date')::date,
    CASE 
      WHEN new.raw_user_meta_data->>'partner_birth_date' IS NOT NULL 
      THEN (new.raw_user_meta_data->>'partner_birth_date')::date
      ELSE NULL
    END,
    new.raw_user_meta_data->>'city',
    new.raw_user_meta_data->>'area',
    new.raw_user_meta_data->>'phone',
    CASE
      WHEN new.raw_user_meta_data->>'profile_data' IS NOT NULL
      THEN (new.raw_user_meta_data->>'profile_data')::jsonb
      ELSE '{}'::jsonb
    END
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();