import { React, useState, useEffect } from 'react';
import SignupForm from './components/SignupForm';
import SignInForm from './components/SignInForm';
import CoupleProfileForm from './components/CoupleProfileForm';
import SingleProfileForm from './components/SingleProfileForm';
import ImageUploadForm from './components/ImageUploadForm';
import HomePage from './components/HomePage';
import ProfilePage from './components/ProfilePage';
import SimpleProfilePage from './components/SimpleProfilePage';
import AdminPage from './components/AdminPage';
import VipSubscriptionPage from './components/VipSubscriptionPage';
import PointsPage from './components/PointsPage';
import { ToastContainer } from './components/Toast';
import { useAdmin } from './hooks/useAdmin';
import { useAuthContext } from './contexts/AuthContext';
import { useSignup } from './hooks/useSignup';
import { useToast } from './hooks/useToast';
import { Profile } from './types/supabase';

function App() {
  const { user, loading } = useAuthContext();
  const isAdmin = useAdmin();
  const [showSignIn, setShowSignIn] = useState(true);
  const [currentPage, setCurrentPage] = useState<'home' | 'profile' | 'admin' | 'vip' | 'points'>('home');
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  const { step, data, setStep, updateData, handleSignup } = useSignup();
  const { toast, toasts, closeToast } = useToast();

  useEffect(() => {
    const handleNavigation = (event: CustomEvent) => {
      setCurrentPage(event.detail.page);
      // Check if profile is explicitly null (for viewing own profile)
      // or if it's an actual profile object (for viewing other profiles)
      if (event.detail.hasOwnProperty('profile')) {
        setSelectedProfile(event.detail.profile);
      }
    };

    window.addEventListener('navigation', handleNavigation as EventListener);

    return () => {
      window.removeEventListener('navigation', handleNavigation as EventListener);
    };
  }, []);

  // Set global state for gender
  if (typeof window !== 'undefined') {
    (window as any).__APP_STATE = {
      gender: data.gender
    };
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-600"></div>
      </div>
    );
  }

  // If user is authenticated, show HomePage, ProfilePage, or AdminPage
  if (user) {
    if (isAdmin && currentPage === 'admin') {
      return <AdminPage />;
    }

    switch (currentPage) {
      case 'profile':
        return <ProfilePage profile={selectedProfile} />;
      case 'vip':
        return <VipSubscriptionPage />;
      case 'points':
        return <PointsPage />;
      default:
        return <HomePage />;
    }
  }

  // If user is not authenticated, show SignIn or Signup flow
  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-pink-100">
      <ToastContainer toasts={toasts} closeToast={closeToast} />

      {showSignIn ? (
        <SignInForm onSignUpClick={() => setShowSignIn(false)} />
      ) : (
        <>
          {step === 1 && (
            <SignupForm
              onNext={(formData) => {
                updateData(formData);
                setStep(2);
              }}
              onSignInClick={() => setShowSignIn(true)}
              initialData={data}
            />
          )}
          {step === 2 && data.gender === 'couple' && (
            <CoupleProfileForm
              onNext={(formData) => {
                updateData(formData);
                setStep(3);
              }}
              onBack={() => setStep(1)}
              initialData={data}
            />
          )}
          {step === 2 && (data.gender === 'male' || data.gender === 'female') && (
            <SingleProfileForm
              onNext={(formData) => {
                updateData(formData);
                setStep(3);
              }}
              onBack={() => setStep(1)}
              initialData={data}
            />
          )}
          {step === 3 && (
            <ImageUploadForm
              onBack={() => setStep(2)}
              onComplete={async () => {
                const { error } = await handleSignup();
                if (!error) {
                  // Signup successful, user will be automatically redirected
                  // to HomePage due to auth state change
                }
                return { error };
              }}
            />
          )}
        </>
      )}
    </div>
  );
}

export default App;