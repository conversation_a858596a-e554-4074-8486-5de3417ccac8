/*
  # Add chat creation policies

  1. Security Changes
    - Add RLS policy to allow authenticated users to create chats
    - Add RLS policy to allow chat participants to update chats
  
  Note: These policies ensure users can create new chats and update existing ones
  while maintaining security.
*/

-- Policy to allow authenticated users to create chats
CREATE POLICY "Users can create chats"
ON chats
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Policy to allow chat participants to update chats
CREATE POLICY "Chat participants can update chats"
ON chats
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = id
    AND chat_participants.user_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_id = id
    AND chat_participants.user_id = auth.uid()
  )
);