-- Create a simple table for profile images
CREATE TABLE IF NOT EXISTS profile_images (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  image_url TEXT NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_profile_images_user_id ON profile_images(user_id);

-- Enable RLS
ALTER TABLE profile_images ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all profile images" ON profile_images
  FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile image" ON profile_images
  FOR ALL USING (user_id = auth.uid());

-- Create function to upsert profile image
CREATE OR REPLACE FUNCTION upsert_profile_image(p_user_id uuid, p_image_url text)
R<PERSON>URNS void AS $$
BEGIN
  INSERT INTO profile_images (user_id, image_url, updated_at)
  VALUES (p_user_id, p_image_url, now())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    image_url = EXCLUDED.image_url,
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Migrate existing profile images from auth.users if any
INSERT INTO profile_images (user_id, image_url)
SELECT 
  au.id,
  au.raw_user_meta_data->>'profile_image_url'
FROM auth.users au
WHERE au.raw_user_meta_data->>'profile_image_url' IS NOT NULL
ON CONFLICT (user_id) DO NOTHING;
