/*
  # Create Points Transactions Table

  1. New Tables
    - `points_transactions` table for storing points transactions
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `amount` (integer)
      - `description` (text)
      - `payment_id` (text)
      - `package_id` (uuid, references points_packages)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - Users can view their own transactions
      - <PERSON><PERSON> can view all transactions
*/

-- Create points_transactions table
CREATE TABLE IF NOT EXISTS points_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  amount integer NOT NULL,
  description text,
  payment_id text,
  package_id uuid REFERENCES points_packages(id) ON DELETE SET NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE points_transactions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own transactions"
  ON points_transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "<PERSON><PERSON> can view all transactions"
  ON points_transactions
  FOR SELECT
  TO authenticated
  USING (auth.email() = '<EMAIL>');

CREATE POLICY "Admins can insert transactions"
  ON points_transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.email() = '<EMAIL>' OR
    auth.uid() = user_id
  );

-- Create function to update user points when a transaction is inserted
CREATE OR REPLACE FUNCTION update_user_points()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE profiles
  SET points = points + NEW.amount
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update user points
CREATE TRIGGER on_points_transaction_inserted
  AFTER INSERT ON points_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_user_points();
