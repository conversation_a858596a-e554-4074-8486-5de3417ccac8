import { useState } from 'react';
import { supabase } from '../lib/supabase';
import { Profile } from '../types/supabase';
import { useAuth } from './useAuth';

export function useProfile() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateProfile = async (profileData: Partial<Profile['profile_data']>) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('Updating profile for user:', user.id, 'with data:', profileData);

      // First, get the current user data to ensure we have the latest metadata
      const { data: { user: currentUser }, error: getUserError } = await supabase.auth.getUser();

      if (getUserError) {
        throw getUserError;
      }

      if (!currentUser) {
        throw new Error('Failed to get current user');
      }

      // Merge the new profile data with existing profile data
      const mergedProfileData = {
        ...(currentUser.user_metadata?.profile_data || {}),
        ...profileData
      };

      console.log('Merged profile data:', mergedProfileData);

      // Update user metadata with merged data
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          profile_data: mergedProfileData
        }
      });

      if (updateError) throw updateError;

      console.log('Auth user metadata updated successfully');

      // Get the updated user metadata
      const { data: { user: updatedUser } } = await supabase.auth.getUser();

      if (!updatedUser) {
        throw new Error('Failed to get updated user after update');
      }

      console.log('Updated user metadata:', updatedUser.user_metadata);

      // Update profiles table to trigger realtime updates and sync profile_data
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({
          updated_at: new Date().toISOString(),
          profile_data_updated: true,
          // Important: Sync the profile_data from auth.users to profiles table
          user_metadata: updatedUser.user_metadata
        })
        .eq('id', user.id);

      if (profileUpdateError) {
        console.error('Error updating profile in profiles table:', profileUpdateError);
      } else {
        console.log('Profile updated in profiles table successfully');
      }

      // Don't force page refresh - let the modal handle the close properly
      return { error: null };
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('אירעה שגיאה בעדכון הפרופיל');
      return { error: err };
    } finally {
      setLoading(false);
    }
  };

  return {
    updateProfile,
    loading,
    error
  };
}