import { useState } from 'react';
import { supabase } from '../lib/supabase';
import { Profile } from '../types/supabase';
import { useAuth } from './useAuth';

export function useProfile() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateProfile = async (profileData: Partial<Profile['profile_data']>) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('=== PROFILE UPDATE START ===');
      console.log('Updating profile for user:', user.id, 'with data:', profileData);
      console.log('Birth date in profileData:', profileData.birth_date);

      // First, get the current user data to ensure we have the latest metadata
      const { data: { user: currentUser }, error: getUserError } = await supabase.auth.getUser();

      if (getUserError) {
        throw getUserError;
      }

      if (!currentUser) {
        throw new Error('Failed to get current user');
      }

      // Merge the new profile data with existing profile data
      const mergedProfileData = {
        ...(currentUser.user_metadata?.profile_data || {}),
        ...profileData
      };

      // Also update top-level birth_date if provided
      const updatedUserMetadata = {
        ...currentUser.user_metadata,
        profile_data: mergedProfileData
      };

      // If birth_date is being updated, also update it at the top level
      if (profileData.birth_date) {
        updatedUserMetadata.birth_date = profileData.birth_date;
      }

      console.log('Merged profile data:', mergedProfileData);
      console.log('Birth date in merged data:', mergedProfileData.birth_date);
      console.log('Updated user metadata:', updatedUserMetadata);
      console.log('Birth date in updated metadata:', updatedUserMetadata.birth_date);

      // Update user metadata with merged data
      const { error: updateError } = await supabase.auth.updateUser({
        data: updatedUserMetadata
      });

      if (updateError) throw updateError;

      console.log('Auth user metadata updated successfully');

      // Get the updated user metadata
      const { data: { user: updatedUser } } = await supabase.auth.getUser();

      if (!updatedUser) {
        throw new Error('Failed to get updated user after update');
      }

      console.log('Updated user metadata:', updatedUser.user_metadata);
      console.log('Birth date in final metadata:', updatedUser.user_metadata?.birth_date);
      console.log('Birth date in final profile_data:', updatedUser.user_metadata?.profile_data?.birth_date);
      console.log('Profile update completed successfully - auth metadata updated');

      // Trigger a custom event to notify components about the profile update
      window.dispatchEvent(new CustomEvent('profileUpdated', {
        detail: {
          userId: user.id,
          profileData: updatedUser.user_metadata.profile_data
        }
      }));

      return { error: null };
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('אירעה שגיאה בעדכון הפרופיל');
      return { error: err };
    } finally {
      setLoading(false);
    }
  };

  return {
    updateProfile,
    loading,
    error
  };
}