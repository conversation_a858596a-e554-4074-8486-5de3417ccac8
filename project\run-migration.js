import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables. Please set VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 Starting migration...');
    
    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'supabase', 'migrations', '20250106000001_add_image_support_to_messages.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error);
          // Continue with other statements
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err);
        // Continue with other statements
      }
    }
    
    console.log('🎉 Migration completed!');
    
    // Test the new functionality
    console.log('🧪 Testing new functionality...');
    
    // Check if image_url column exists
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'messages')
      .eq('column_name', 'image_url');
    
    if (columnsError) {
      console.error('❌ Error checking columns:', columnsError);
    } else if (columns && columns.length > 0) {
      console.log('✅ image_url column exists in messages table');
    } else {
      console.log('⚠️ image_url column not found in messages table');
    }
    
    // Check if storage bucket exists
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    if (bucketsError) {
      console.error('❌ Error checking buckets:', bucketsError);
    } else {
      const chatImagesBucket = buckets.find(b => b.id === 'chat-images');
      if (chatImagesBucket) {
        console.log('✅ chat-images storage bucket exists');
      } else {
        console.log('⚠️ chat-images storage bucket not found');
      }
    }
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

// Alternative approach - run SQL directly
async function runMigrationDirect() {
  try {
    console.log('🚀 Starting direct migration...');

    // Step 1: Add image_url column
    console.log('📝 Adding image_url column...');
    try {
      const { error: alterError } = await supabase
        .from('messages')
        .select('image_url')
        .limit(1);

      if (alterError && alterError.message.includes('column "image_url" does not exist')) {
        console.log('Column does not exist, need to add it manually via SQL');
        // We'll need to add this via SQL in the database directly
      } else {
        console.log('✅ image_url column already exists or accessible');
      }
    } catch (error) {
      console.log('Column check failed, assuming it needs to be added');
    }

    // Step 2: Create storage bucket
    console.log('📝 Creating storage bucket...');
    const { error: bucketError } = await supabase.storage.createBucket('chat-images', {
      public: true
    });

    if (bucketError && !bucketError.message.includes('already exists')) {
      console.error('❌ Error creating bucket:', bucketError);
    } else {
      console.log('✅ chat-images bucket ready');
    }

    // Step 3: Test basic functionality
    console.log('🧪 Testing basic message functionality...');
    const { data: testData, error: testError } = await supabase
      .from('messages')
      .select('id, content, chat_id, sender_id')
      .limit(1);

    if (testError) {
      console.error('❌ Basic message test failed:', testError);
    } else {
      console.log('✅ Basic message functionality works');
    }

    console.log('🎉 Direct migration completed!');

  } catch (error) {
    console.error('💥 Direct migration failed:', error);
  }
}

// Run the migration
console.log('🔧 Running database migration for image support...');
runMigrationDirect();
