import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';

interface FreeTodayStatus {
  isFreeToday: boolean;
  expiresAt: string | null;
  timeRemaining: string | null;
  loading: boolean;
  error: string | null;
}

export function useFreeTodayWithExpiry() {
  const { user } = useAuth();
  const [status, setStatus] = useState<FreeTodayStatus>({
    isFreeToday: false,
    expiresAt: null,
    timeRemaining: null,
    loading: false,
    error: null
  });

  // Function to format time remaining
  const formatTimeRemaining = (expiresAt: string): string => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry.getTime() - now.getTime();

    if (diff <= 0) {
      return 'Expired';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else {
      return `${minutes}m remaining`;
    }
  };

  // Function to check current status
  const checkStatus = useCallback(async () => {
    if (!user?.id) return;

    setStatus(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Try to use the RPC function first, fallback to direct query
      let isFree = false;

      try {
        const { data: rpcResult, error: rpcError } = await supabase
          .rpc('is_user_free_today', { p_user_id: user.id });

        if (!rpcError) {
          isFree = rpcResult;
        } else {
          throw rpcError;
        }
      } catch (rpcErr) {
        console.log('RPC not available, using direct query');
        // Fallback to direct query
        const { data: directResult, error: directError } = await supabase
          .from('free_today')
          .select('id, expires_at')
          .eq('user_id', user.id)
          .maybeSingle();

        if (directError) {
          throw directError;
        }

        // Check if record exists and not expired
        if (directResult) {
          if (directResult.expires_at) {
            isFree = new Date(directResult.expires_at) > new Date();
          } else {
            // No expiry set, assume still valid for now
            isFree = true;
          }
        } else {
          isFree = false;
        }
      }

      if (isFree) {
        // Get the expiry details
        const { data: freeData, error: freeError } = await supabase
          .from('free_today')
          .select('expires_at')
          .eq('user_id', user.id)
          .single();

        if (freeError) {
          throw freeError;
        }

        const timeRemaining = freeData?.expires_at ? formatTimeRemaining(freeData.expires_at) : null;

        setStatus({
          isFreeToday: true,
          expiresAt: freeData?.expires_at || null,
          timeRemaining,
          loading: false,
          error: null
        });
      } else {
        setStatus({
          isFreeToday: false,
          expiresAt: null,
          timeRemaining: null,
          loading: false,
          error: null
        });
      }
    } catch (error: any) {
      console.error('Error checking free today status:', error);
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to check status'
      }));
    }
  }, [user?.id]);

  // Function to join free today
  const joinFreeToday = useCallback(async () => {
    if (!user?.id) return { success: false, error: 'User not authenticated' };

    setStatus(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Try RPC first, fallback to direct insert
      try {
        const { error: rpcError } = await supabase
          .rpc('add_user_to_free_today', { p_user_id: user.id });

        if (rpcError) {
          throw rpcError;
        }
      } catch (rpcErr) {
        console.log('RPC not available, using direct insert');
        // Fallback to direct insert with expiry
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24);

        const { error: insertError } = await supabase
          .from('free_today')
          .upsert({
            user_id: user.id,
            expires_at: expiresAt.toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          throw insertError;
        }
      }

      // Refresh status after joining
      await checkStatus();

      console.log('✅ Successfully joined free today');
      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error joining free today:', error);
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to join free today'
      }));
      return { success: false, error: error.message || 'Failed to join free today' };
    }
  }, [user?.id, checkStatus]);

  // Function to leave free today
  const leaveFreeToday = useCallback(async () => {
    if (!user?.id) return { success: false, error: 'User not authenticated' };

    setStatus(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Try RPC first, fallback to direct delete
      try {
        const { error: rpcError } = await supabase
          .rpc('remove_user_from_free_today', { p_user_id: user.id });

        if (rpcError) {
          throw rpcError;
        }
      } catch (rpcErr) {
        console.log('RPC not available, using direct delete');
        // Fallback to direct delete
        const { error: deleteError } = await supabase
          .from('free_today')
          .delete()
          .eq('user_id', user.id);

        if (deleteError) {
          throw deleteError;
        }
      }

      setStatus({
        isFreeToday: false,
        expiresAt: null,
        timeRemaining: null,
        loading: false,
        error: null
      });

      console.log('✅ Successfully left free today');
      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error leaving free today:', error);
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to leave free today'
      }));
      return { success: false, error: error.message || 'Failed to leave free today' };
    }
  }, [user?.id]);

  // Check status on mount and when user changes
  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  // Update time remaining every minute
  useEffect(() => {
    if (!status.isFreeToday || !status.expiresAt) return;

    const interval = setInterval(() => {
      const timeRemaining = formatTimeRemaining(status.expiresAt!);
      
      if (timeRemaining === 'Expired') {
        // Status has expired, refresh from server
        checkStatus();
      } else {
        setStatus(prev => ({ ...prev, timeRemaining }));
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [status.isFreeToday, status.expiresAt, checkStatus]);

  return {
    ...status,
    joinFreeToday,
    leaveFreeToday,
    refreshStatus: checkStatus
  };
}

// Hook to get all free today users
export function useFreeTodayUsers() {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error: fetchError } = await supabase
        .rpc('get_free_today_users');

      if (fetchError) {
        throw fetchError;
      }

      setUsers(data || []);
    } catch (error: any) {
      console.error('Error fetching free today users:', error);
      setError(error.message || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    users,
    loading,
    error,
    refreshUsers: fetchUsers
  };
}
