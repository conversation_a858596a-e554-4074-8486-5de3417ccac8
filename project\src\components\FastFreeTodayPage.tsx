import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Search, Users, Timer, Grid3X3, List } from 'lucide-react';
import MiniUserCard from './MiniUserCard';
import CompactUserCard from './CompactUserCard';
import HorizontalUserCard from './HorizontalUserCard';
import { Profile } from '../types/supabase';
import { useFreeTodayWithExpiry } from '../hooks/useFreeTodayWithExpiry';

interface FastFreeTodayPageProps {
  profiles: Profile[];
  isVip: boolean;
}

function FastFreeTodayPage({ profiles, isVip }: FastFreeTodayPageProps) {
  // Debug logging for real profiles
  console.log('📊 FastFreeTodayPage - Real profiles received:', {
    count: profiles.length,
    profiles: profiles.map(p => ({
      id: p.id,
      username: p.username,
      city: p.city,
      free_today: p.free_today,
      is_vip: p.is_vip
    }))
  });
  // Use the hook for free today functionality
  const {
    isFreeToday,
    timeRemaining,
    loading: freeTodayLoading,
    joinFreeToday,
    leaveFreeToday
  } = useFreeTodayWithExpiry();

  // Responsive view mode - mobile: mini (grid), desktop: compact (horizontal)
  const [isMobile, setIsMobile] = useState(typeof window !== 'undefined' ? window.innerWidth < 768 : false);
  const [viewMode, setViewMode] = useState<'mini' | 'compact'>(isMobile ? 'mini' : 'compact');

  // Listen for window resize
  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      setViewMode(mobile ? 'mini' : 'compact');
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const [searchTerm, setSearchTerm] = useState('');

  // Use only real profiles
  const realProfiles = profiles;

  // Fast filtering with useMemo
  const filteredProfiles = useMemo(() => {
    if (!searchTerm) return realProfiles;

    return realProfiles.filter(profile =>
      profile.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (profile.city && profile.city.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [realProfiles, searchTerm]);

  return (
    <div className="space-y-4 max-w-6xl mx-auto px-4">
      {/* Simple Header */}
      <div className="bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl p-6 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Calendar className="w-6 h-6" />
              פנויים היום
            </h1>
            <p className="text-amber-100 text-sm">
              {filteredProfiles.length} משתמשים פנויים למפגש היום
            </p>
          </div>
          
          {isVip && (
            <div className="flex flex-col items-end gap-2">
              <button
                onClick={isFreeToday ? leaveFreeToday : joinFreeToday}
                disabled={freeTodayLoading}
                className={`flex items-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all disabled:opacity-50 ${
                  isFreeToday
                    ? 'bg-white text-amber-600'
                    : 'bg-amber-700 text-white hover:bg-amber-800'
                }`}
              >
                <Calendar className="w-4 h-4" />
                {freeTodayLoading 
                  ? (isFreeToday ? 'יוצא...' : 'מצטרף...') 
                  : (isFreeToday ? 'לא פנוי היום' : 'פנוי היום')
                }
              </button>
              
              {isFreeToday && timeRemaining && (
                <div className="flex items-center gap-1 text-xs text-amber-100">
                  <Timer className="w-3 h-3" />
                  {timeRemaining}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Simple Search and View Toggle */}
      <div className="bg-white rounded-xl shadow-sm p-4 flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="חפש לפי שם או עיר..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-10 pl-4 py-2 border border-gray-200 rounded-lg focus:border-amber-500 focus:ring-1 focus:ring-amber-500"
          />
        </div>
        
        {/* View toggle only on desktop */}
        {!isMobile && (
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('mini')}
              className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
                viewMode === 'mini' ? 'bg-white text-amber-700 shadow-sm' : 'text-gray-600'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
              רשת
            </button>
            <button
              onClick={() => setViewMode('compact')}
              className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
                viewMode === 'compact' ? 'bg-white text-amber-700 shadow-sm' : 'text-gray-600'
              }`}
            >
              <List className="w-4 h-4" />
              לרוחב
            </button>
          </div>
        )}
      </div>

      {/* Responsive Grid */}
      {filteredProfiles.length > 0 ? (
        <div className={`grid gap-4 ${
          isMobile
            ? 'grid-cols-2 sm:grid-cols-3' // Mobile: always grid
            : viewMode === 'mini'
            ? 'grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' // Desktop grid
            : 'grid-cols-1' // Desktop horizontal
        }`}>
          {filteredProfiles.map((profile) => (
            <div key={profile.id}>
              {isMobile ? (
                <MiniUserCard profile={profile} />
              ) : viewMode === 'mini' ? (
                <MiniUserCard profile={profile} />
              ) : (
                <HorizontalUserCard profile={profile} />
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl p-8 text-center">
          <div className="bg-amber-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Calendar className="w-8 h-8 text-amber-500" />
          </div>
          {searchTerm ? (
            <>
              <h3 className="text-lg font-bold text-gray-900 mb-2">אין תוצאות חיפוש</h3>
              <p className="text-gray-600">נסה לשנות את מונחי החיפוש או נקה את השדה</p>
            </>
          ) : (
            <>
              <h3 className="text-lg font-bold text-gray-900 mb-2">אין משתמשים פנויים היום</h3>
              <p className="text-gray-600 mb-4">
                כרגע אין משתמשים שסימנו את עצמם כפנויים היום.
              </p>
              {isVip && !isFreeToday && (
                <div className="mt-4">
                  <button
                    onClick={joinFreeToday}
                    disabled={freeTodayLoading}
                    className="bg-gradient-to-r from-amber-500 to-amber-600 text-white px-6 py-3 rounded-xl font-medium hover:from-amber-600 hover:to-amber-700 transition-colors shadow-md disabled:opacity-50"
                  >
                    <span className="flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      {freeTodayLoading ? 'מצטרף...' : 'היה הראשון - הצטרף לפנויים היום!'}
                    </span>
                  </button>
                </div>
              )}
              {!isVip && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-700 text-sm">
                    <strong>רוצה להצטרף לפנויים היום?</strong><br />
                    שדרג ל-VIP כדי לסמן את עצמך כפנוי ולהופיע ברשימה!
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}

export default FastFreeTodayPage;
