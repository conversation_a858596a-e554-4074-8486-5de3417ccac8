import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Search, Users, Timer, Grid3X3, List } from 'lucide-react';
import MiniUserCard from './MiniUserCard';
import CompactUserCard from './CompactUserCard';
import { Profile } from '../types/supabase';
import { useFreeTodayWithExpiry } from '../hooks/useFreeTodayWithExpiry';

interface FastFreeTodayPageProps {
  profiles: Profile[];
  isVip: boolean;
}

function FastFreeTodayPage({ profiles, isVip }: FastFreeTodayPageProps) {
  // Use the hook for free today functionality
  const {
    isFreeToday,
    timeRemaining,
    loading: freeTodayLoading,
    joinFreeToday,
    leaveFreeToday
  } = useFreeTodayWithExpiry();

  // Simple state
  const [viewMode, setViewMode] = useState<'mini' | 'compact'>('mini');
  const [searchTerm, setSearchTerm] = useState('');

  // Demo profiles if none exist
  const demoProfiles = profiles.length === 0 ? [
    {
      id: 'demo-1',
      username: 'דמו משתמש 1',
      city: 'תל אביב',
      gender: 'male' as const,
      birth_date: '1990-01-01',
      is_vip: true,
      is_online: true,
      photos: ['https://via.placeholder.com/300x400/FF6B6B/FFFFFF?text=Demo+1'],
      free_today: true,
      user_metadata: { profile_image_url: 'https://via.placeholder.com/300x400/FF6B6B/FFFFFF?text=Demo+1' }
    },
    {
      id: 'demo-2',
      username: 'דמו משתמש 2',
      city: 'חיפה',
      gender: 'female' as const,
      birth_date: '1995-05-15',
      is_vip: true,
      is_online: false,
      photos: ['https://via.placeholder.com/300x400/4ECDC4/FFFFFF?text=Demo+2'],
      free_today: true,
      user_metadata: { profile_image_url: 'https://via.placeholder.com/300x400/4ECDC4/FFFFFF?text=Demo+2' }
    },
    {
      id: 'demo-3',
      username: 'דמו זוג',
      city: 'ירושלים',
      gender: 'couple' as const,
      birth_date: '1988-12-25',
      is_vip: true,
      is_online: true,
      photos: ['https://via.placeholder.com/300x400/45B7D1/FFFFFF?text=Demo+Couple'],
      free_today: true,
      user_metadata: { profile_image_url: 'https://via.placeholder.com/300x400/45B7D1/FFFFFF?text=Demo+Couple' }
    }
  ] : profiles;

  // Fast filtering with useMemo
  const filteredProfiles = useMemo(() => {
    if (!searchTerm) return demoProfiles;
    
    return demoProfiles.filter(profile =>
      profile.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.city.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [demoProfiles, searchTerm]);

  return (
    <div className="space-y-4 max-w-6xl mx-auto px-4">
      {/* Simple Header */}
      <div className="bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl p-6 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Calendar className="w-6 h-6" />
              פנויים היום
            </h1>
            <p className="text-amber-100 text-sm">
              {filteredProfiles.length} משתמשים פנויים למפגש היום
            </p>
          </div>
          
          {isVip && (
            <div className="flex flex-col items-end gap-2">
              <button
                onClick={isFreeToday ? leaveFreeToday : joinFreeToday}
                disabled={freeTodayLoading}
                className={`flex items-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all disabled:opacity-50 ${
                  isFreeToday
                    ? 'bg-white text-amber-600'
                    : 'bg-amber-700 text-white hover:bg-amber-800'
                }`}
              >
                <Calendar className="w-4 h-4" />
                {freeTodayLoading 
                  ? (isFreeToday ? 'יוצא...' : 'מצטרף...') 
                  : (isFreeToday ? 'לא פנוי היום' : 'פנוי היום')
                }
              </button>
              
              {isFreeToday && timeRemaining && (
                <div className="flex items-center gap-1 text-xs text-amber-100">
                  <Timer className="w-3 h-3" />
                  {timeRemaining}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Simple Search and View Toggle */}
      <div className="bg-white rounded-xl shadow-sm p-4 flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="חפש לפי שם או עיר..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-10 pl-4 py-2 border border-gray-200 rounded-lg focus:border-amber-500 focus:ring-1 focus:ring-amber-500"
          />
        </div>
        
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setViewMode('mini')}
            className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
              viewMode === 'mini' ? 'bg-white text-amber-700 shadow-sm' : 'text-gray-600'
            }`}
          >
            <Grid3X3 className="w-4 h-4" />
            רשת
          </button>
          <button
            onClick={() => setViewMode('compact')}
            className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
              viewMode === 'compact' ? 'bg-white text-amber-700 shadow-sm' : 'text-gray-600'
            }`}
          >
            <List className="w-4 h-4" />
            רשימה
          </button>
        </div>
      </div>

      {/* Fast Grid - No complex animations */}
      {filteredProfiles.length > 0 ? (
        <div className={`grid gap-4 ${
          viewMode === 'mini' 
            ? 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6' 
            : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
        }`}>
          {filteredProfiles.map((profile) => (
            <div key={profile.id}>
              {viewMode === 'mini' ? (
                <MiniUserCard profile={profile} />
              ) : (
                <CompactUserCard profile={profile} />
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl p-8 text-center">
          <div className="bg-amber-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Users className="w-8 h-8 text-amber-500" />
          </div>
          <h3 className="text-lg font-bold text-gray-900 mb-2">אין תוצאות</h3>
          <p className="text-gray-600">נסה לשנות את מונחי החיפוש</p>
        </div>
      )}
    </div>
  );
}

export default FastFreeTodayPage;
