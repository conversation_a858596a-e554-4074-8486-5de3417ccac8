@tailwind base;
@tailwind components;
@tailwind utilities;

/* View-once image styles */
.view-once-image {
  filter: blur(20px);
  transition: filter 0.3s ease;
  cursor: pointer;
  position: relative;
}

.view-once-image:hover {
  filter: blur(15px);
}

.view-once-image.viewing {
  filter: blur(0px) !important;
  transform: scale(1.02);
}

.view-once-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  border-radius: 8px;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.view-once-overlay.hidden {
  opacity: 0;
}

.view-once-timer {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  animation: pulse 1s infinite;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.view-once-timer.warning {
  background: rgba(220, 38, 38, 0.9);
  animation: pulse-fast 0.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes pulse-fast {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

/* Add custom styles for RTL support */
body {
  direction: rtl;
  text-align: right;
  font-family: system-ui, -apple-system, sans-serif;
  background-color: #fdf2f8;
  -webkit-tap-highlight-color: transparent;
}

input, textarea {
  text-align: right;
}

/* Add 3D hover effects */
.hover-3d {
  transition: transform 0.3s ease-in-out;
  transform-style: preserve-3d;
}

.hover-3d:hover {
  transform: translateY(-5px) rotateX(5deg);
}

/* Background pattern for hero sections */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Animation for new items */
@keyframes pulse-once {
  0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(74, 222, 128, 0); }
  100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
}

.animate-pulse-once {
  animation: pulse-once 2s ease-out 1;
}

/* Prevent flickering during transitions */
.user-card-container {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Snap scrolling */
.snap-x {
  scroll-snap-type: x mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  /* Improve touch targets */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Prevent text zoom on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px;
  }

  /* Disable pull-to-refresh on iOS */
  html {
    overflow: hidden;
    height: 100%;
  }

  body {
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
  }
}