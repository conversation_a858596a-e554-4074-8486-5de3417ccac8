@tailwind base;
@tailwind components;
@tailwind utilities;



/* High quality image rendering */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: high-quality;
}

/* Smooth transitions for all elements */
* {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Add custom styles for RTL support */
body {
  direction: rtl;
  text-align: right;
  font-family: system-ui, -apple-system, sans-serif;
  background-color: #fdf2f8;
  -webkit-tap-highlight-color: transparent;
}

input, textarea {
  text-align: right;
}

/* Add 3D hover effects */
.hover-3d {
  transition: transform 0.3s ease-in-out;
  transform-style: preserve-3d;
}

.hover-3d:hover {
  transform: translateY(-5px) rotateX(5deg);
}

/* Background pattern for hero sections */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Animation for new items */
@keyframes pulse-once {
  0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(74, 222, 128, 0); }
  100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
}

.animate-pulse-once {
  animation: pulse-once 2s ease-out 1;
}

/* Prevent flickering during transitions */
.user-card-container {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Snap scrolling */
.snap-x {
  scroll-snap-type: x mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  /* Improve touch targets */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Prevent text zoom on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px;
  }

  /* Disable pull-to-refresh on iOS */
  html {
    overflow: hidden;
    height: 100%;
  }

  body {
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
  }
}