// Utility functions for date calculations

export function calculateAge(birthDate: string): number | null {
  if (!birthDate) return null;
  
  const birth = new Date(birthDate);
  const today = new Date();
  
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}

export function formatAge(birthDate: string): string {
  const age = calculateAge(birthDate);
  if (age === null) return '';
  return `${age} שנים`;
}

export function formatBirthDate(birthDate: string): string {
  if (!birthDate) return '';
  
  const date = new Date(birthDate);
  return date.toLocaleDateString('he-IL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
