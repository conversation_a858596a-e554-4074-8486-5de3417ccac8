import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Shield, 
  Edit3, 
  Trash2, 
  Image, 
  Database, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { useAdmin } from '../hooks/useAdmin';
import AdminEditModal from './AdminEditModal';

interface AdminPanelProps {
  userId: string;
  username?: string;
  profileData?: any;
  onUpdate?: () => void;
}

type ActionType = 'edit' | 'clear_photos' | 'clear_data' | 'clear_all' | 'delete_profile';

export default function AdminPanel({ userId, username, profileData, onUpdate }: AdminPanelProps) {
  const { isAdmin, actionLoading, executeAdminAction } = useAdmin();
  const [activeAction, setActiveAction] = useState<ActionType | null>(null);
  const [showConfirm, setShowConfirm] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  if (!isAdmin) return null;

  const handleAction = async (action: ActionType) => {
    if (['clear_photos', 'clear_data', 'clear_all', 'delete_profile'].includes(action)) {
      setActiveAction(action);
      setShowConfirm(true);
      return;
    }

    if (action === 'edit') {
      setActiveAction('edit');
    }
  };

  const confirmAction = async () => {
    if (!activeAction) return;

    const actionMap = {
      clear_photos: { type: 'clear_data' as const, data: { dataType: 'photos' } },
      clear_data: { type: 'clear_data' as const, data: { dataType: 'profile_data' } },
      clear_all: { type: 'clear_data' as const, data: { dataType: 'all' } },
      delete_profile: { type: 'delete_profile' as const, data: {} }
    };

    const actionConfig = actionMap[activeAction as keyof typeof actionMap];
    if (!actionConfig) return;

    const result = await executeAdminAction({
      type: actionConfig.type,
      userId,
      data: actionConfig.data
    });

    setMessage({
      type: result.success ? 'success' : 'error',
      text: result.message
    });

    setShowConfirm(false);
    setActiveAction(null);

    if (result.success) {
      onUpdate?.();
      // Auto-hide success message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    }
  };

  const actions = [
    {
      id: 'edit' as ActionType,
      label: 'ערוך פרופיל',
      icon: Edit3,
      color: 'blue',
      description: 'עריכת פרטי המשתמש'
    },
    {
      id: 'clear_photos' as ActionType,
      label: 'נקה תמונות',
      icon: Image,
      color: 'orange',
      description: 'מחיקת כל התמונות'
    },
    {
      id: 'clear_data' as ActionType,
      label: 'נקה נתונים',
      icon: Database,
      color: 'yellow',
      description: 'מחיקת נתוני פרופיל'
    },
    {
      id: 'clear_all' as ActionType,
      label: 'נקה הכל',
      icon: AlertTriangle,
      color: 'red',
      description: 'מחיקת כל הנתונים'
    },
    {
      id: 'delete_profile' as ActionType,
      label: 'מחק פרופיל',
      icon: Trash2,
      color: 'red',
      description: 'מחיקת הפרופיל לחלוטין'
    }
  ];

  const getActionColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-200',
      orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-200',
      yellow: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-200',
      red: 'bg-red-100 text-red-800 hover:bg-red-200 border-red-200'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const confirmMessages = {
    clear_photos: `האם אתה בטוח שברצונך למחוק את כל התמונות של ${username}?`,
    clear_data: `האם אתה בטוח שברצונך לנקות את נתוני הפרופיל של ${username}?`,
    clear_all: `האם אתה בטוח שברצונך לנקות את כל הנתונים של ${username}?`,
    delete_profile: `האם אתה בטוח שברצונך למחוק את הפרופיל של ${username} לחלוטין? פעולה זו לא ניתנת לביטול!`
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl p-6 mt-6 shadow-lg"
    >
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-red-100 rounded-lg">
          <Shield className="w-6 h-6 text-red-600" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-red-800">בקרת אדמין</h3>
          <p className="text-sm text-red-600">ניהול פרופיל {username}</p>
        </div>
      </div>

      {/* Message Display */}
      <AnimatePresence>
        {message && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className={`mb-4 p-4 rounded-lg flex items-center gap-3 ${
              message.type === 'success' 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : 'bg-red-100 text-red-800 border border-red-200'
            }`}
          >
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <XCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{message.text}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Actions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {actions.map((action) => {
          const Icon = action.icon;
          return (
            <motion.button
              key={action.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => handleAction(action.id)}
              disabled={actionLoading}
              className={`
                p-4 rounded-lg border-2 transition-all duration-200 
                ${getActionColor(action.color)}
                ${actionLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                flex flex-col items-center gap-3 text-center
              `}
            >
              <div className="p-2 bg-white rounded-lg shadow-sm">
                {actionLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Icon className="w-5 h-5" />
                )}
              </div>
              <div>
                <div className="font-semibold">{action.label}</div>
                <div className="text-xs opacity-75">{action.description}</div>
              </div>
            </motion.button>
          );
        })}
      </div>

      {/* Confirmation Modal */}
      <AnimatePresence>
        {showConfirm && activeAction && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowConfirm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-xl p-6 max-w-md mx-4 shadow-2xl"
            >
              <div className="flex items-center gap-3 mb-4">
                <AlertTriangle className="w-6 h-6 text-red-500" />
                <h3 className="text-lg font-semibold text-gray-900">אישור פעולה</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                {confirmMessages[activeAction as keyof typeof confirmMessages]}
              </p>
              
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setShowConfirm(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  ביטול
                </button>
                <button
                  onClick={confirmAction}
                  disabled={actionLoading}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center gap-2"
                >
                  {actionLoading && <Loader2 className="w-4 h-4 animate-spin" />}
                  אישור
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Modal */}
      <AdminEditModal
        isOpen={activeAction === 'edit'}
        onClose={() => setActiveAction(null)}
        userId={userId}
        profileData={profileData}
        onUpdate={() => {
          onUpdate?.();
          setActiveAction(null);
          setMessage({ type: 'success', text: 'הפרופיל עודכן בהצלחה' });
          setTimeout(() => setMessage(null), 3000);
        }}
      />
    </motion.div>
  );
}
