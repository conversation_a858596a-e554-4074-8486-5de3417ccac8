-- Update free_today table to include expiry functionality
-- Add expires_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'free_today' 
        AND column_name = 'expires_at'
    ) THEN
        ALTER TABLE free_today ADD COLUMN expires_at timestamptz;
        COMMENT ON COLUMN free_today.expires_at IS 'When the free today status expires (24 hours from creation)';
    END IF;
END $$;

-- Update existing records to have expiry time (24 hours from created_at)
UPDATE free_today 
SET expires_at = created_at + INTERVAL '24 hours'
WHERE expires_at IS NULL;

-- Create index for better performance on expiry queries
CREATE INDEX IF NOT EXISTS idx_free_today_expires_at ON free_today(expires_at);
CREATE INDEX IF NOT EXISTS idx_free_today_user_expires ON free_today(user_id, expires_at);

-- Create function to clean up expired free_today entries
CREATE OR REPLACE FUNCTION cleanup_expired_free_today()
R<PERSON><PERSON>NS void AS $$
BEGIN
  DELETE FROM free_today 
  WHERE expires_at < now();
  
  -- Log how many records were cleaned up
  GET DIAGNOSTICS var_count = ROW_COUNT;
  RAISE NOTICE 'Cleaned up % expired free_today records', var_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to add user to free_today with automatic expiry
CREATE OR REPLACE FUNCTION add_user_to_free_today(p_user_id uuid)
RETURNS void AS $$
BEGIN
  -- First clean up any existing expired entries for this user
  DELETE FROM free_today 
  WHERE user_id = p_user_id AND expires_at < now();
  
  -- Insert or update the user's free_today status
  INSERT INTO free_today (user_id, expires_at, created_at, updated_at)
  VALUES (p_user_id, now() + INTERVAL '24 hours', now(), now())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    expires_at = now() + INTERVAL '24 hours',
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to remove user from free_today
CREATE OR REPLACE FUNCTION remove_user_from_free_today(p_user_id uuid)
RETURNS void AS $$
BEGIN
  DELETE FROM free_today WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is currently free today (not expired)
CREATE OR REPLACE FUNCTION is_user_free_today(p_user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM free_today 
    WHERE user_id = p_user_id 
    AND expires_at > now()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get all currently free users (not expired)
CREATE OR REPLACE FUNCTION get_free_today_users()
RETURNS TABLE(
  user_id uuid,
  created_at timestamptz,
  expires_at timestamptz,
  time_remaining interval
) AS $$
BEGIN
  -- First clean up expired entries
  PERFORM cleanup_expired_free_today();
  
  -- Return active free_today users
  RETURN QUERY
  SELECT 
    ft.user_id,
    ft.created_at,
    ft.expires_at,
    ft.expires_at - now() as time_remaining
  FROM free_today ft
  WHERE ft.expires_at > now()
  ORDER BY ft.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled job to clean up expired entries every hour
-- Note: This requires pg_cron extension which might not be available in all environments
-- If pg_cron is not available, we'll handle cleanup in the application code
DO $$
BEGIN
  -- Try to create a cron job if pg_cron is available
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
    -- Clean up expired free_today entries every hour
    PERFORM cron.schedule('cleanup-expired-free-today', '0 * * * *', 'SELECT cleanup_expired_free_today();');
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    -- pg_cron not available, that's okay
    RAISE NOTICE 'pg_cron not available, cleanup will be handled in application code';
END $$;
