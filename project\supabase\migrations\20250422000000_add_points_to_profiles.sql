/*
  # Add Points to Profiles

  1. Changes
    - Add `points` integer column to `profiles` table with default value of 0
    
  2. Security
    - No changes to RLS policies needed as this column will be managed through existing profile policies
*/

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'points'
  ) THEN
    ALTER TABLE profiles ADD COLUMN points integer DEFAULT 0;
  END IF;
END $$;
