/*
  # Add Free Today Feature

  1. New Tables
    - `free_today` table for storing users who are free today
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `created_at` (timestamptz)
      - `expires_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - VIP users can add themselves
      - All users can view entries
    - Auto-cleanup of expired entries
*/

-- Create free_today table
CREATE TABLE IF NOT EXISTS free_today (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz DEFAULT (now() + interval '24 hours'),
  UNIQUE (user_id)
);

-- Enable RLS
ALTER TABLE free_today ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "VIP users can add themselves to free today"
  ON free_today
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND is_vip = true
    )
  );

CREATE POLICY "Users can view free today entries"
  ON free_today
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can delete their own entries"
  ON free_today
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Function to clean up expired entries
CREATE OR REPLACE FUNCTION cleanup_expired_free_today()
RETURNS void AS $$
BEGIN
  DELETE FROM free_today
  WHERE expires_at < now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;