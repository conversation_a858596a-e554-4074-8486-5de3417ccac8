-- Simplify chat system
-- This migration simplifies the chat system to fix loading issues

-- Drop all existing functions
DROP FUNCTION IF EXISTS get_user_chats(UUID);
DROP FUNCTION IF EXISTS get_chat_messages(U<PERSON><PERSON>, U<PERSON><PERSON>);
DROP FUNCTION IF EXISTS create_chat_between_users(<PERSON><PERSON><PERSON>, U<PERSON><PERSON>);
DROP FUNCTION IF EXISTS delete_message(U<PERSON>D, UUI<PERSON>);
DROP FUNCTION IF EXISTS delete_chat(UUID, UUID);

-- Add missing columns to chats table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chats' AND column_name = 'is_deleted') THEN
    ALTER TABLE chats ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chats' AND column_name = 'deleted_at') THEN
    ALTER TABLE chats ADD COLUMN deleted_at TIMESTAMPTZ;
  END IF;
END
$$;

-- Add missing columns to chat_participants table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chat_participants' AND column_name = 'is_active') THEN
    ALTER TABLE chat_participants ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chat_participants' AND column_name = 'left_at') THEN
    ALTER TABLE chat_participants ADD COLUMN left_at TIMESTAMPTZ;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chat_participants' AND column_name = 'last_read_at') THEN
    ALTER TABLE chat_participants ADD COLUMN last_read_at TIMESTAMPTZ;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chat_participants' AND column_name = 'joined_at') THEN
    ALTER TABLE chat_participants ADD COLUMN joined_at TIMESTAMPTZ DEFAULT NOW();
  END IF;
END
$$;

-- Create a simple function to create chat between users
CREATE OR REPLACE FUNCTION create_chat_between_users(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
  existing_chat_id UUID;
  new_chat_id UUID;
BEGIN
  -- Check if a chat already exists between these users
  SELECT cp1.chat_id INTO existing_chat_id
  FROM chat_participants cp1
  JOIN chat_participants cp2 ON cp1.chat_id = cp2.chat_id
  WHERE cp1.user_id = user1_id AND cp2.user_id = user2_id
  LIMIT 1;
  
  IF existing_chat_id IS NOT NULL THEN
    RETURN existing_chat_id;
  END IF;
  
  -- Create a new chat
  INSERT INTO chats (created_at, updated_at)
  VALUES (NOW(), NOW())
  RETURNING id INTO new_chat_id;
  
  -- Add participants
  INSERT INTO chat_participants (chat_id, user_id, joined_at, is_active)
  VALUES 
    (new_chat_id, user1_id, NOW(), TRUE),
    (new_chat_id, user2_id, NOW(), TRUE);
  
  RETURN new_chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simple function to get user's chats
CREATE OR REPLACE FUNCTION get_user_chats_simple(p_user_id UUID)
RETURNS TABLE (
  chat_id UUID,
  other_user_id UUID,
  other_username TEXT,
  other_profile_image TEXT,
  other_is_online BOOLEAN,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  unread_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS chat_id,
    p.id AS other_user_id,
    p.username AS other_username,
    p.profile_image_url AS other_profile_image,
    COALESCE(p.is_online, FALSE) AS other_is_online,
    c.last_message,
    c.last_message_at,
    0::BIGINT AS unread_count
  FROM 
    chats c
  JOIN 
    chat_participants cp1 ON c.id = cp1.chat_id AND cp1.user_id = p_user_id
  JOIN 
    chat_participants cp2 ON c.id = cp2.chat_id AND cp2.user_id != p_user_id
  JOIN 
    profiles p ON cp2.user_id = p.id
  WHERE
    COALESCE(c.is_deleted, FALSE) = FALSE
  ORDER BY 
    c.updated_at DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simple function to get chat messages
CREATE OR REPLACE FUNCTION get_chat_messages_simple(p_chat_id UUID)
RETURNS TABLE (
  id UUID,
  chat_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  sender_username TEXT,
  sender_profile_image TEXT,
  sender_is_online BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    m.id,
    m.chat_id,
    m.sender_id,
    m.content,
    m.created_at,
    p.username AS sender_username,
    p.profile_image_url AS sender_profile_image,
    COALESCE(p.is_online, FALSE) AS sender_is_online
  FROM 
    messages m
  JOIN 
    profiles p ON m.sender_id = p.id
  WHERE 
    m.chat_id = p_chat_id
  ORDER BY 
    m.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
