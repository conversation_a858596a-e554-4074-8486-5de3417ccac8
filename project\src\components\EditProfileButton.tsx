import React, { useState } from 'react';
import { Edit } from 'lucide-react';
import { motion } from 'framer-motion';
import EditProfileModal from './EditProfileModal';

function EditProfileButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsModalOpen(true)}
        className="flex items-center gap-2 bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 transition-colors"
      >
        <Edit className="w-4 h-4" />
        ערוך פרופיל
      </motion.button>

      <EditProfileModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}

export default EditProfileButton;