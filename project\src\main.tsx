import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { library } from '@fortawesome/fontawesome-svg-core';
import { faVenus, faMars } from '@fortawesome/free-solid-svg-icons';
import { AuthProvider } from './contexts/AuthContext';
import App from './App.tsx';
import './index.css';

// Add icons to the library
library.add(faVenus, faMars);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AuthProvider>
      <App />
    </AuthProvider>
  </StrictMode>
);