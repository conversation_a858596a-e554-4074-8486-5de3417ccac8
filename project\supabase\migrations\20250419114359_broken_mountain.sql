/*
  # Fix Message Privacy

  1. Changes
    - Drop existing message policies
    - Create new policies that ensure message privacy
    - Only allow users to see messages from their own chats
    
  2. Security
    - Messages are only visible to chat participants
    - Users can only send messages to chats they're part of
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view messages in their chats" ON messages;
DROP POLICY IF EXISTS "Users can send messages to their chats" ON messages;

-- Create new policies with proper privacy checks
CREATE POLICY "Users can view messages in their chats"
ON messages
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Users can send messages to their chats"
ON messages
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM chat_participants
    WHERE chat_participants.chat_id = messages.chat_id
    AND chat_participants.user_id = auth.uid()
  )
  AND sender_id = auth.uid()
);