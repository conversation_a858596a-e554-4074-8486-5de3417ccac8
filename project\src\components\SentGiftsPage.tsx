import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Gift,
  Heart,
  Flower,
  Wine,
  Cookie,
  CircleDot,
  Star,
  Diamond,
  Banknote,
  HeartCrack,
  ArrowRight,
  Calendar,
  User,
  Loader2,
  <PERSON><PERSON><PERSON>riangle,
  Send
} from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import Header from './Header';

interface SentGift {
  id: string;
  amount: number;
  description: string;
  created_at: string;
  receiver?: {
    username: string;
    profile_image_url?: string;
  };
}

// Function to get the appropriate icon component based on gift name
const getGiftIcon = (giftName: string) => {
  const name = giftName.toLowerCase();
  
  switch (name) {
    case 'לב':
    case 'heart':
      return <Heart className="w-8 h-8 text-red-500" />;
    case 'פרח':
    case 'flower':
      return <Flower className="w-8 h-8 text-pink-500" />;
    case 'שמפניה':
    case 'champagne':
    case 'wine':
      return <Wine className="w-8 h-8 text-purple-500" />;
    case 'שוקולד':
    case 'chocolate':
    case 'candy':
      return <Cookie className="w-8 h-8 text-yellow-500" />;
    case 'טבעת':
    case 'ring':
      return <CircleDot className="w-8 h-8 text-yellow-400" />;
    case 'כוכב':
    case 'star':
      return <Star className="w-8 h-8 text-yellow-400" />;
    case 'יהלום':
    case 'diamond':
      return <Diamond className="w-8 h-8 text-blue-400" />;
    case 'כסף':
    case 'money':
      return <Banknote className="w-8 h-8 text-green-500" />;
    default:
      return <Gift className="w-8 h-8 text-pink-500" />;
  }
};

function SentGiftsPage() {
  const { user } = useAuth();
  const [gifts, setGifts] = useState<SentGift[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchSentGifts();
    }
  }, [user?.id]);

  const fetchSentGifts = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching sent gifts for user:', user?.id);

      // Simple approach: Try to get from localStorage first
      const sentGiftsKey = `sent_gifts_${user?.id}`;
      const localGifts = localStorage.getItem(sentGiftsKey);

      if (localGifts) {
        try {
          const parsedGifts = JSON.parse(localGifts);
          console.log('Found local gifts:', parsedGifts);
          setGifts(parsedGifts);
          return;
        } catch (parseError) {
          console.error('Error parsing local gifts:', parseError);
        }
      }

      // If no local data, try to fetch from database
      try {
        const { data: notifications, error: notificationsError } = await supabase
          .from('notifications')
          .select('*')
          .eq('type', 'gift')
          .contains('data', { sender_id: user?.id })
          .order('created_at', { ascending: false });

        console.log('Notifications result:', { notifications, error: notificationsError });

        if (notificationsError) {
          console.error('Notifications error:', notificationsError);
          // Show empty list if error
          setGifts([]);
          return;
        }

        // Process notifications
        const giftsWithReceivers = await Promise.all(
          (notifications || []).map(async (notification) => {
            let receiver = null;

            try {
              if (notification.user_id) {
                const { data: receiverData } = await supabase
                  .from('profiles')
                  .select('username, profile_image_url')
                  .eq('id', notification.user_id)
                  .single();

                receiver = receiverData;
              }
            } catch (profileError) {
              console.error('Error fetching receiver profile:', profileError);
            }

            return {
              id: notification.id,
              created_at: notification.created_at,
              giftName: notification.data?.gift_name || 'מתנה',
              amount: -(notification.data?.gift_price || 0),
              receiver: receiver || { username: 'משתמש לא ידוע' }
            };
          })
        );

        console.log('Processed gifts:', giftsWithReceivers);
        setGifts(giftsWithReceivers);

        // Save to localStorage for next time
        localStorage.setItem(sentGiftsKey, JSON.stringify(giftsWithReceivers));

      } catch (dbError) {
        console.error('Database error:', dbError);
        // Show empty list if database error
        setGifts([]);
      }
    } catch (err) {
      console.error('Error fetching sent gifts:', err);
      console.error('Error details:', JSON.stringify(err, null, 2));
      setError(`אירעה שגיאה בטעינת המתנות: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `לפני ${diffInMinutes} דקות`;
    } else if (diffInHours < 24) {
      return `לפני ${diffInHours} שעות`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `לפני ${diffInDays} ימים`;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Send className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">מתנות ששלחת</h1>
          </div>
          <p className="text-gray-600">כל המתנות ששלחת למשתמשים אחרים</p>
        </motion.div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
            <span className="mr-3 text-gray-600">טוען מתנות...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchSentGifts}
              className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
            >
              נסה שוב
            </button>
          </div>
        ) : gifts.length === 0 ? (
          <div className="text-center py-12">
            <Send className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">לא שלחת מתנות עדיין</h3>
            <p className="text-gray-500">כשתשלח מתנות, הן יופיעו כאן</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {gifts.map((gift) => (
              <motion.div
                key={gift.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl shadow-md p-6 border-r-4 border-blue-500"
              >
                <div className="flex items-start gap-4">
                  {/* Gift Icon */}
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
                      {getGiftIcon(gift.giftName || '')}
                    </div>
                  </div>

                  {/* Gift Details */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          🎁 {gift.giftName} ({Math.abs(gift.amount)} נקודות)
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                          <User className="w-4 h-4" />
                          <span>נשלח אל: {gift.receiver?.username}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(gift.created_at)}</span>
                      </div>
                    </div>

                    {/* Status */}
                    <div className="flex items-center gap-2 mt-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-green-600 font-medium">נשלח בהצלחה</span>
                    </div>
                  </div>

                  {/* Arrow Icon */}
                  <div className="flex-shrink-0">
                    <ArrowRight className="w-6 h-6 text-blue-500" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default SentGiftsPage;
