/*
  # Add Gifts Table

  1. New Tables
    - `gifts` table for storing gift items
      - `id` (uuid, primary key)
      - `name` (text)
      - `price` (integer)
      - `image_url` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS
    - Add policies for:
      - <PERSON><PERSON> can manage gifts
      - Authenticated users can view gifts
*/

-- Create gifts table
CREATE TABLE IF NOT EXISTS gifts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  price integer NOT NULL,
  image_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE gifts ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "<PERSON><PERSON> can manage gifts"
  ON gifts
  FOR ALL
  TO authenticated
  USING (auth.email() = '<EMAIL>')
  WITH CHECK (auth.email() = '<EMAIL>');

CREATE POLICY "Users can view gifts"
  ON gifts
  FOR SELECT
  TO authenticated
  USING (true);

-- Create storage bucket for gift images
INSERT INTO storage.buckets (id, name, public)
VALUES ('gift-images', 'gift-images', true)
ON CONFLICT (id) DO NOTHING;

-- Set bucket configuration
UPDATE storage.buckets
SET file_size_limit = 5242880, -- 5MB in bytes
    allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
WHERE id = 'gift-images';