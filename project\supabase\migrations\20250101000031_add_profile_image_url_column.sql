-- Add profile_image_url column to profiles table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'profile_image_url'
    ) THEN
        ALTER TABLE profiles ADD COLUMN profile_image_url TEXT;
        COMMENT ON COLUMN profiles.profile_image_url IS 'URL of the user profile image';
    END IF;
END $$;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_profile_image_url ON profiles(profile_image_url);

-- Update existing profiles with profile image from user_metadata if available
UPDATE profiles 
SET profile_image_url = (user_metadata->>'profile_image_url')
WHERE user_metadata->>'profile_image_url' IS NOT NULL 
AND (profile_image_url IS NULL OR profile_image_url = '');
