/*
  # Add Online Status Tracking

  1. Changes
    - Add is_online column to profiles table
    - Add last_active column to profiles table
    - Add index on is_online for performance
    
  2. Security
    - Maintain existing RLS policies
    - Allow users to update their own online status
*/

-- Add columns if they don't exist
DO $$ 
BEGIN
  -- Add is_online column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'is_online'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_online boolean DEFAULT false;
  END IF;

  -- Add last_active column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'last_active'
  ) THEN
    ALTER TABLE profiles ADD COLUMN last_active timestamptz DEFAULT now();
  END IF;
END $$;

-- Create index on is_online for better query performance
CREATE INDEX IF NOT EXISTS idx_profiles_is_online ON profiles(is_online);

-- Add policy for users to update their own online status
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can update their online status'
  ) THEN
    CREATE POLICY "Users can update their online status"
      ON profiles
      FOR UPDATE
      TO authenticated
      USING (auth.uid() = id)
      WITH CHECK (auth.uid() = id);
  END IF;
END $$;